{"version": 3, "file": "bootstrap.bundle.js", "sources": ["../../rollupPluginBabelHelpers", "../../js/src/util.js", "../../js/src/alert.js", "../../js/src/button.js", "../../js/src/carousel.js", "../../js/src/collapse.js", "../../node_modules/popper.js/dist/esm/popper.js", "../../js/src/dropdown.js", "../../js/src/modal.js", "../../js/src/tooltip.js", "../../js/src/popover.js", "../../js/src/scrollspy.js", "../../js/src/tab.js", "../../js/src/index.js"], "sourcesContent": ["export { _createClass as createClass, _extends as extends, _inherits<PERSON>oose as inherits<PERSON><PERSON>e };\n\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\n\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  return Constructor;\n}\n\nfunction _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n\n    return target;\n  };\n\n  return _extends.apply(this, arguments);\n}\n\nfunction _inheritsLoose(subClass, superClass) {\n  subClass.prototype = Object.create(superClass.prototype);\n  subClass.prototype.constructor = subClass;\n  subClass.__proto__ = superClass;\n}", "import $ from 'jquery'\n\n/**\n * --------------------------------------------------------------------------\n * <PERSON>trap (v4.0.0): util.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst Util = (($) => {\n  /**\n   * ------------------------------------------------------------------------\n   * Private TransitionEnd Helpers\n   * ------------------------------------------------------------------------\n   */\n\n  let transition = false\n\n  const MAX_UID = 1000000\n\n  // Shoutout AngusCroll (https://goo.gl/pxwQGp)\n  function toType(obj) {\n    return {}.toString.call(obj).match(/\\s([a-zA-Z]+)/)[1].toLowerCase()\n  }\n\n  function getSpecialTransitionEndEvent() {\n    return {\n      bindType: transition.end,\n      delegateType: transition.end,\n      handle(event) {\n        if ($(event.target).is(this)) {\n          return event.handleObj.handler.apply(this, arguments) // eslint-disable-line prefer-rest-params\n        }\n        return undefined // eslint-disable-line no-undefined\n      }\n    }\n  }\n\n  function transitionEndTest() {\n    if (typeof window !== 'undefined' && window.QUnit) {\n      return false\n    }\n\n    return {\n      end: 'transitionend'\n    }\n  }\n\n  function transitionEndEmulator(duration) {\n    let called = false\n\n    $(this).one(Util.TRANSITION_END, () => {\n      called = true\n    })\n\n    setTimeout(() => {\n      if (!called) {\n        Util.triggerTransitionEnd(this)\n      }\n    }, duration)\n\n    return this\n  }\n\n  function setTransitionEndSupport() {\n    transition = transitionEndTest()\n\n    $.fn.emulateTransitionEnd = transitionEndEmulator\n\n    if (Util.supportsTransitionEnd()) {\n      $.event.special[Util.TRANSITION_END] = getSpecialTransitionEndEvent()\n    }\n  }\n\n  function escapeId(selector) {\n    // We escape IDs in case of special selectors (selector = '#myId:something')\n    // $.escapeSelector does not exist in jQuery < 3\n    selector = typeof $.escapeSelector === 'function' ? $.escapeSelector(selector).substr(1)\n      : selector.replace(/(:|\\.|\\[|\\]|,|=|@)/g, '\\\\$1')\n\n    return selector\n  }\n\n  /**\n   * --------------------------------------------------------------------------\n   * Public Util Api\n   * --------------------------------------------------------------------------\n   */\n\n  const Util = {\n\n    TRANSITION_END: 'bsTransitionEnd',\n\n    getUID(prefix) {\n      do {\n        // eslint-disable-next-line no-bitwise\n        prefix += ~~(Math.random() * MAX_UID) // \"~~\" acts like a faster Math.floor() here\n      } while (document.getElementById(prefix))\n      return prefix\n    },\n\n    getSelectorFromElement(element) {\n      let selector = element.getAttribute('data-target')\n      if (!selector || selector === '#') {\n        selector = element.getAttribute('href') || ''\n      }\n\n      // If it's an ID\n      if (selector.charAt(0) === '#') {\n        selector = escapeId(selector)\n      }\n\n      try {\n        const $selector = $(document).find(selector)\n        return $selector.length > 0 ? selector : null\n      } catch (err) {\n        return null\n      }\n    },\n\n    reflow(element) {\n      return element.offsetHeight\n    },\n\n    triggerTransitionEnd(element) {\n      $(element).trigger(transition.end)\n    },\n\n    supportsTransitionEnd() {\n      return Boolean(transition)\n    },\n\n    isElement(obj) {\n      return (obj[0] || obj).nodeType\n    },\n\n    typeCheckConfig(componentName, config, configTypes) {\n      for (const property in configTypes) {\n        if (Object.prototype.hasOwnProperty.call(configTypes, property)) {\n          const expectedTypes = configTypes[property]\n          const value         = config[property]\n          const valueType     = value && Util.isElement(value)\n            ? 'element' : toType(value)\n\n          if (!new RegExp(expectedTypes).test(valueType)) {\n            throw new Error(\n              `${componentName.toUpperCase()}: ` +\n              `Option \"${property}\" provided type \"${valueType}\" ` +\n              `but expected type \"${expectedTypes}\".`)\n          }\n        }\n      }\n    }\n  }\n\n  setTransitionEndSupport()\n\n  return Util\n})($)\n\nexport default Util\n", "import $ from 'jquery'\nimport Util from './util'\n\n/**\n * --------------------------------------------------------------------------\n * <PERSON>trap (v4.0.0): alert.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst Alert = (($) => {\n  /**\n   * ------------------------------------------------------------------------\n   * Constants\n   * ------------------------------------------------------------------------\n   */\n\n  const NAME                = 'alert'\n  const VERSION             = '4.0.0'\n  const DATA_KEY            = 'bs.alert'\n  const EVENT_KEY           = `.${DATA_KEY}`\n  const DATA_API_KEY        = '.data-api'\n  const JQUERY_NO_CONFLICT  = $.fn[NAME]\n  const TRANSITION_DURATION = 150\n\n  const Selector = {\n    DISMISS : '[data-dismiss=\"alert\"]'\n  }\n\n  const Event = {\n    CLOSE          : `close${EVENT_KEY}`,\n    CLOSED         : `closed${EVENT_KEY}`,\n    CLICK_DATA_API : `click${EVENT_KEY}${DATA_API_KEY}`\n  }\n\n  const ClassName = {\n    ALERT : 'alert',\n    FADE  : 'fade',\n    SHOW  : 'show'\n  }\n\n  /**\n   * ------------------------------------------------------------------------\n   * Class Definition\n   * ------------------------------------------------------------------------\n   */\n\n  class Alert {\n    constructor(element) {\n      this._element = element\n    }\n\n    // Getters\n\n    static get VERSION() {\n      return VERSION\n    }\n\n    // Public\n\n    close(element) {\n      element = element || this._element\n\n      const rootElement = this._getRootElement(element)\n      const customEvent = this._triggerCloseEvent(rootElement)\n\n      if (customEvent.isDefaultPrevented()) {\n        return\n      }\n\n      this._removeElement(rootElement)\n    }\n\n    dispose() {\n      $.removeData(this._element, DATA_KEY)\n      this._element = null\n    }\n\n    // Private\n\n    _getRootElement(element) {\n      const selector = Util.getSelectorFromElement(element)\n      let parent     = false\n\n      if (selector) {\n        parent = $(selector)[0]\n      }\n\n      if (!parent) {\n        parent = $(element).closest(`.${ClassName.ALERT}`)[0]\n      }\n\n      return parent\n    }\n\n    _triggerCloseEvent(element) {\n      const closeEvent = $.Event(Event.CLOSE)\n\n      $(element).trigger(closeEvent)\n      return closeEvent\n    }\n\n    _removeElement(element) {\n      $(element).removeClass(ClassName.SHOW)\n\n      if (!Util.supportsTransitionEnd() ||\n          !$(element).hasClass(ClassName.FADE)) {\n        this._destroyElement(element)\n        return\n      }\n\n      $(element)\n        .one(Util.TRANSITION_END, (event) => this._destroyElement(element, event))\n        .emulateTransitionEnd(TRANSITION_DURATION)\n    }\n\n    _destroyElement(element) {\n      $(element)\n        .detach()\n        .trigger(Event.CLOSED)\n        .remove()\n    }\n\n    // Static\n\n    static _jQueryInterface(config) {\n      return this.each(function () {\n        const $element = $(this)\n        let data       = $element.data(DATA_KEY)\n\n        if (!data) {\n          data = new Alert(this)\n          $element.data(DATA_KEY, data)\n        }\n\n        if (config === 'close') {\n          data[config](this)\n        }\n      })\n    }\n\n    static _handleDismiss(alertInstance) {\n      return function (event) {\n        if (event) {\n          event.preventDefault()\n        }\n\n        alertInstance.close(this)\n      }\n    }\n  }\n\n  /**\n   * ------------------------------------------------------------------------\n   * Data Api implementation\n   * ------------------------------------------------------------------------\n   */\n\n  $(document).on(\n    Event.CLICK_DATA_API,\n    Selector.DISMISS,\n    Alert._handleDismiss(new Alert())\n  )\n\n  /**\n   * ------------------------------------------------------------------------\n   * jQuery\n   * ------------------------------------------------------------------------\n   */\n\n  $.fn[NAME]             = Alert._jQueryInterface\n  $.fn[NAME].Constructor = Alert\n  $.fn[NAME].noConflict  = function () {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Alert._jQueryInterface\n  }\n\n  return Alert\n})($)\n\nexport default Alert\n", "import $ from 'jquery'\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.0.0): button.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst Button = (($) => {\n  /**\n   * ------------------------------------------------------------------------\n   * Constants\n   * ------------------------------------------------------------------------\n   */\n\n  const NAME                = 'button'\n  const VERSION             = '4.0.0'\n  const DATA_KEY            = 'bs.button'\n  const EVENT_KEY           = `.${DATA_KEY}`\n  const DATA_API_KEY        = '.data-api'\n  const JQUERY_NO_CONFLICT  = $.fn[NAME]\n\n  const ClassName = {\n    ACTIVE : 'active',\n    BUTTON : 'btn',\n    FOCUS  : 'focus'\n  }\n\n  const Selector = {\n    DATA_TOGGLE_CARROT : '[data-toggle^=\"button\"]',\n    DATA_TOGGLE        : '[data-toggle=\"buttons\"]',\n    INPUT              : 'input',\n    ACTIVE             : '.active',\n    BUTTON             : '.btn'\n  }\n\n  const Event = {\n    CLICK_DATA_API      : `click${EVENT_KEY}${DATA_API_KEY}`,\n    FOCUS_BLUR_DATA_API : `focus${EVENT_KEY}${DATA_API_KEY} ` +\n                            `blur${EVENT_KEY}${DATA_API_KEY}`\n  }\n\n  /**\n   * ------------------------------------------------------------------------\n   * Class Definition\n   * ------------------------------------------------------------------------\n   */\n\n  class Button {\n    constructor(element) {\n      this._element = element\n    }\n\n    // Getters\n\n    static get VERSION() {\n      return VERSION\n    }\n\n    // Public\n\n    toggle() {\n      let triggerChangeEvent = true\n      let addAriaPressed = true\n      const rootElement = $(this._element).closest(\n        Selector.DATA_TOGGLE\n      )[0]\n\n      if (rootElement) {\n        const input = $(this._element).find(Selector.INPUT)[0]\n\n        if (input) {\n          if (input.type === 'radio') {\n            if (input.checked &&\n              $(this._element).hasClass(ClassName.ACTIVE)) {\n              triggerChangeEvent = false\n            } else {\n              const activeElement = $(rootElement).find(Selector.ACTIVE)[0]\n\n              if (activeElement) {\n                $(activeElement).removeClass(ClassName.ACTIVE)\n              }\n            }\n          }\n\n          if (triggerChangeEvent) {\n            if (input.hasAttribute('disabled') ||\n              rootElement.hasAttribute('disabled') ||\n              input.classList.contains('disabled') ||\n              rootElement.classList.contains('disabled')) {\n              return\n            }\n            input.checked = !$(this._element).hasClass(ClassName.ACTIVE)\n            $(input).trigger('change')\n          }\n\n          input.focus()\n          addAriaPressed = false\n        }\n      }\n\n      if (addAriaPressed) {\n        this._element.setAttribute('aria-pressed',\n          !$(this._element).hasClass(ClassName.ACTIVE))\n      }\n\n      if (triggerChangeEvent) {\n        $(this._element).toggleClass(ClassName.ACTIVE)\n      }\n    }\n\n    dispose() {\n      $.removeData(this._element, DATA_KEY)\n      this._element = null\n    }\n\n    // Static\n\n    static _jQueryInterface(config) {\n      return this.each(function () {\n        let data = $(this).data(DATA_KEY)\n\n        if (!data) {\n          data = new Button(this)\n          $(this).data(DATA_KEY, data)\n        }\n\n        if (config === 'toggle') {\n          data[config]()\n        }\n      })\n    }\n  }\n\n  /**\n   * ------------------------------------------------------------------------\n   * Data Api implementation\n   * ------------------------------------------------------------------------\n   */\n\n  $(document)\n    .on(Event.CLICK_DATA_API, Selector.DATA_TOGGLE_CARROT, (event) => {\n      event.preventDefault()\n\n      let button = event.target\n\n      if (!$(button).hasClass(ClassName.BUTTON)) {\n        button = $(button).closest(Selector.BUTTON)\n      }\n\n      Button._jQueryInterface.call($(button), 'toggle')\n    })\n    .on(Event.FOCUS_BLUR_DATA_API, Selector.DATA_TOGGLE_CARROT, (event) => {\n      const button = $(event.target).closest(Selector.BUTTON)[0]\n      $(button).toggleClass(ClassName.FOCUS, /^focus(in)?$/.test(event.type))\n    })\n\n  /**\n   * ------------------------------------------------------------------------\n   * jQuery\n   * ------------------------------------------------------------------------\n   */\n\n  $.fn[NAME] = Button._jQueryInterface\n  $.fn[NAME].Constructor = Button\n  $.fn[NAME].noConflict = function () {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Button._jQueryInterface\n  }\n\n  return Button\n})($)\n\nexport default Button\n", "import $ from 'jquery'\nimport Util from './util'\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.0.0): carousel.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst Carousel = (($) => {\n  /**\n   * ------------------------------------------------------------------------\n   * Constants\n   * ------------------------------------------------------------------------\n   */\n\n  const NAME                   = 'carousel'\n  const VERSION                = '4.0.0'\n  const DATA_KEY               = 'bs.carousel'\n  const EVENT_KEY              = `.${DATA_KEY}`\n  const DATA_API_KEY           = '.data-api'\n  const JQUERY_NO_CONFLICT     = $.fn[NAME]\n  const TRANSITION_DURATION    = 600\n  const ARROW_LEFT_KEYCODE     = 37 // KeyboardEvent.which value for left arrow key\n  const ARROW_RIGHT_KEYCODE    = 39 // KeyboardEvent.which value for right arrow key\n  const TOUCHEVENT_COMPAT_WAIT = 500 // Time for mouse compat events to fire after touch\n\n  const Default = {\n    interval : 5000,\n    keyboard : true,\n    slide    : false,\n    pause    : 'hover',\n    wrap     : true\n  }\n\n  const DefaultType = {\n    interval : '(number|boolean)',\n    keyboard : 'boolean',\n    slide    : '(boolean|string)',\n    pause    : '(string|boolean)',\n    wrap     : 'boolean'\n  }\n\n  const Direction = {\n    NEXT     : 'next',\n    PREV     : 'prev',\n    LEFT     : 'left',\n    RIGHT    : 'right'\n  }\n\n  const Event = {\n    SLIDE          : `slide${EVENT_KEY}`,\n    SLID           : `slid${EVENT_KEY}`,\n    KEYDOWN        : `keydown${EVENT_KEY}`,\n    MOUSEENTER     : `mouseenter${EVENT_KEY}`,\n    MOUSELEAVE     : `mouseleave${EVENT_KEY}`,\n    TOUCHEND       : `touchend${EVENT_KEY}`,\n    LOAD_DATA_API  : `load${EVENT_KEY}${DATA_API_KEY}`,\n    CLICK_DATA_API : `click${EVENT_KEY}${DATA_API_KEY}`\n  }\n\n  const ClassName = {\n    CAROUSEL : 'carousel',\n    ACTIVE   : 'active',\n    SLIDE    : 'slide',\n    RIGHT    : 'carousel-item-right',\n    LEFT     : 'carousel-item-left',\n    NEXT     : 'carousel-item-next',\n    PREV     : 'carousel-item-prev',\n    ITEM     : 'carousel-item'\n  }\n\n  const Selector = {\n    ACTIVE      : '.active',\n    ACTIVE_ITEM : '.active.carousel-item',\n    ITEM        : '.carousel-item',\n    NEXT_PREV   : '.carousel-item-next, .carousel-item-prev',\n    INDICATORS  : '.carousel-indicators',\n    DATA_SLIDE  : '[data-slide], [data-slide-to]',\n    DATA_RIDE   : '[data-ride=\"carousel\"]'\n  }\n\n  /**\n   * ------------------------------------------------------------------------\n   * Class Definition\n   * ------------------------------------------------------------------------\n   */\n\n  class Carousel {\n    constructor(element, config) {\n      this._items             = null\n      this._interval          = null\n      this._activeElement     = null\n\n      this._isPaused          = false\n      this._isSliding         = false\n\n      this.touchTimeout       = null\n\n      this._config            = this._getConfig(config)\n      this._element           = $(element)[0]\n      this._indicatorsElement = $(this._element).find(Selector.INDICATORS)[0]\n\n      this._addEventListeners()\n    }\n\n    // Getters\n\n    static get VERSION() {\n      return VERSION\n    }\n\n    static get Default() {\n      return Default\n    }\n\n    // Public\n\n    next() {\n      if (!this._isSliding) {\n        this._slide(Direction.NEXT)\n      }\n    }\n\n    nextWhenVisible() {\n      // Don't call next when the page isn't visible\n      // or the carousel or its parent isn't visible\n      if (!document.hidden &&\n        ($(this._element).is(':visible') && $(this._element).css('visibility') !== 'hidden')) {\n        this.next()\n      }\n    }\n\n    prev() {\n      if (!this._isSliding) {\n        this._slide(Direction.PREV)\n      }\n    }\n\n    pause(event) {\n      if (!event) {\n        this._isPaused = true\n      }\n\n      if ($(this._element).find(Selector.NEXT_PREV)[0] &&\n        Util.supportsTransitionEnd()) {\n        Util.triggerTransitionEnd(this._element)\n        this.cycle(true)\n      }\n\n      clearInterval(this._interval)\n      this._interval = null\n    }\n\n    cycle(event) {\n      if (!event) {\n        this._isPaused = false\n      }\n\n      if (this._interval) {\n        clearInterval(this._interval)\n        this._interval = null\n      }\n\n      if (this._config.interval && !this._isPaused) {\n        this._interval = setInterval(\n          (document.visibilityState ? this.nextWhenVisible : this.next).bind(this),\n          this._config.interval\n        )\n      }\n    }\n\n    to(index) {\n      this._activeElement = $(this._element).find(Selector.ACTIVE_ITEM)[0]\n\n      const activeIndex = this._getItemIndex(this._activeElement)\n\n      if (index > this._items.length - 1 || index < 0) {\n        return\n      }\n\n      if (this._isSliding) {\n        $(this._element).one(Event.SLID, () => this.to(index))\n        return\n      }\n\n      if (activeIndex === index) {\n        this.pause()\n        this.cycle()\n        return\n      }\n\n      const direction = index > activeIndex\n        ? Direction.NEXT\n        : Direction.PREV\n\n      this._slide(direction, this._items[index])\n    }\n\n    dispose() {\n      $(this._element).off(EVENT_KEY)\n      $.removeData(this._element, DATA_KEY)\n\n      this._items             = null\n      this._config            = null\n      this._element           = null\n      this._interval          = null\n      this._isPaused          = null\n      this._isSliding         = null\n      this._activeElement     = null\n      this._indicatorsElement = null\n    }\n\n    // Private\n\n    _getConfig(config) {\n      config = {\n        ...Default,\n        ...config\n      }\n      Util.typeCheckConfig(NAME, config, DefaultType)\n      return config\n    }\n\n    _addEventListeners() {\n      if (this._config.keyboard) {\n        $(this._element)\n          .on(Event.KEYDOWN, (event) => this._keydown(event))\n      }\n\n      if (this._config.pause === 'hover') {\n        $(this._element)\n          .on(Event.MOUSEENTER, (event) => this.pause(event))\n          .on(Event.MOUSELEAVE, (event) => this.cycle(event))\n        if ('ontouchstart' in document.documentElement) {\n          // If it's a touch-enabled device, mouseenter/leave are fired as\n          // part of the mouse compatibility events on first tap - the carousel\n          // would stop cycling until user tapped out of it;\n          // here, we listen for touchend, explicitly pause the carousel\n          // (as if it's the second time we tap on it, mouseenter compat event\n          // is NOT fired) and after a timeout (to allow for mouse compatibility\n          // events to fire) we explicitly restart cycling\n          $(this._element).on(Event.TOUCHEND, () => {\n            this.pause()\n            if (this.touchTimeout) {\n              clearTimeout(this.touchTimeout)\n            }\n            this.touchTimeout = setTimeout((event) => this.cycle(event), TOUCHEVENT_COMPAT_WAIT + this._config.interval)\n          })\n        }\n      }\n    }\n\n    _keydown(event) {\n      if (/input|textarea/i.test(event.target.tagName)) {\n        return\n      }\n\n      switch (event.which) {\n        case ARROW_LEFT_KEYCODE:\n          event.preventDefault()\n          this.prev()\n          break\n        case ARROW_RIGHT_KEYCODE:\n          event.preventDefault()\n          this.next()\n          break\n        default:\n      }\n    }\n\n    _getItemIndex(element) {\n      this._items = $.makeArray($(element).parent().find(Selector.ITEM))\n      return this._items.indexOf(element)\n    }\n\n    _getItemByDirection(direction, activeElement) {\n      const isNextDirection = direction === Direction.NEXT\n      const isPrevDirection = direction === Direction.PREV\n      const activeIndex     = this._getItemIndex(activeElement)\n      const lastItemIndex   = this._items.length - 1\n      const isGoingToWrap   = isPrevDirection && activeIndex === 0 ||\n                              isNextDirection && activeIndex === lastItemIndex\n\n      if (isGoingToWrap && !this._config.wrap) {\n        return activeElement\n      }\n\n      const delta     = direction === Direction.PREV ? -1 : 1\n      const itemIndex = (activeIndex + delta) % this._items.length\n\n      return itemIndex === -1\n        ? this._items[this._items.length - 1] : this._items[itemIndex]\n    }\n\n    _triggerSlideEvent(relatedTarget, eventDirectionName) {\n      const targetIndex = this._getItemIndex(relatedTarget)\n      const fromIndex = this._getItemIndex($(this._element).find(Selector.ACTIVE_ITEM)[0])\n      const slideEvent = $.Event(Event.SLIDE, {\n        relatedTarget,\n        direction: eventDirectionName,\n        from: fromIndex,\n        to: targetIndex\n      })\n\n      $(this._element).trigger(slideEvent)\n\n      return slideEvent\n    }\n\n    _setActiveIndicatorElement(element) {\n      if (this._indicatorsElement) {\n        $(this._indicatorsElement)\n          .find(Selector.ACTIVE)\n          .removeClass(ClassName.ACTIVE)\n\n        const nextIndicator = this._indicatorsElement.children[\n          this._getItemIndex(element)\n        ]\n\n        if (nextIndicator) {\n          $(nextIndicator).addClass(ClassName.ACTIVE)\n        }\n      }\n    }\n\n    _slide(direction, element) {\n      const activeElement = $(this._element).find(Selector.ACTIVE_ITEM)[0]\n      const activeElementIndex = this._getItemIndex(activeElement)\n      const nextElement   = element || activeElement &&\n        this._getItemByDirection(direction, activeElement)\n      const nextElementIndex = this._getItemIndex(nextElement)\n      const isCycling = Boolean(this._interval)\n\n      let directionalClassName\n      let orderClassName\n      let eventDirectionName\n\n      if (direction === Direction.NEXT) {\n        directionalClassName = ClassName.LEFT\n        orderClassName = ClassName.NEXT\n        eventDirectionName = Direction.LEFT\n      } else {\n        directionalClassName = ClassName.RIGHT\n        orderClassName = ClassName.PREV\n        eventDirectionName = Direction.RIGHT\n      }\n\n      if (nextElement && $(nextElement).hasClass(ClassName.ACTIVE)) {\n        this._isSliding = false\n        return\n      }\n\n      const slideEvent = this._triggerSlideEvent(nextElement, eventDirectionName)\n      if (slideEvent.isDefaultPrevented()) {\n        return\n      }\n\n      if (!activeElement || !nextElement) {\n        // Some weirdness is happening, so we bail\n        return\n      }\n\n      this._isSliding = true\n\n      if (isCycling) {\n        this.pause()\n      }\n\n      this._setActiveIndicatorElement(nextElement)\n\n      const slidEvent = $.Event(Event.SLID, {\n        relatedTarget: nextElement,\n        direction: eventDirectionName,\n        from: activeElementIndex,\n        to: nextElementIndex\n      })\n\n      if (Util.supportsTransitionEnd() &&\n        $(this._element).hasClass(ClassName.SLIDE)) {\n        $(nextElement).addClass(orderClassName)\n\n        Util.reflow(nextElement)\n\n        $(activeElement).addClass(directionalClassName)\n        $(nextElement).addClass(directionalClassName)\n\n        $(activeElement)\n          .one(Util.TRANSITION_END, () => {\n            $(nextElement)\n              .removeClass(`${directionalClassName} ${orderClassName}`)\n              .addClass(ClassName.ACTIVE)\n\n            $(activeElement).removeClass(`${ClassName.ACTIVE} ${orderClassName} ${directionalClassName}`)\n\n            this._isSliding = false\n\n            setTimeout(() => $(this._element).trigger(slidEvent), 0)\n          })\n          .emulateTransitionEnd(TRANSITION_DURATION)\n      } else {\n        $(activeElement).removeClass(ClassName.ACTIVE)\n        $(nextElement).addClass(ClassName.ACTIVE)\n\n        this._isSliding = false\n        $(this._element).trigger(slidEvent)\n      }\n\n      if (isCycling) {\n        this.cycle()\n      }\n    }\n\n    // Static\n\n    static _jQueryInterface(config) {\n      return this.each(function () {\n        let data = $(this).data(DATA_KEY)\n        let _config = {\n          ...Default,\n          ...$(this).data()\n        }\n\n        if (typeof config === 'object') {\n          _config = {\n            ..._config,\n            ...config\n          }\n        }\n\n        const action = typeof config === 'string' ? config : _config.slide\n\n        if (!data) {\n          data = new Carousel(this, _config)\n          $(this).data(DATA_KEY, data)\n        }\n\n        if (typeof config === 'number') {\n          data.to(config)\n        } else if (typeof action === 'string') {\n          if (typeof data[action] === 'undefined') {\n            throw new TypeError(`No method named \"${action}\"`)\n          }\n          data[action]()\n        } else if (_config.interval) {\n          data.pause()\n          data.cycle()\n        }\n      })\n    }\n\n    static _dataApiClickHandler(event) {\n      const selector = Util.getSelectorFromElement(this)\n\n      if (!selector) {\n        return\n      }\n\n      const target = $(selector)[0]\n\n      if (!target || !$(target).hasClass(ClassName.CAROUSEL)) {\n        return\n      }\n\n      const config = {\n        ...$(target).data(),\n        ...$(this).data()\n      }\n      const slideIndex = this.getAttribute('data-slide-to')\n\n      if (slideIndex) {\n        config.interval = false\n      }\n\n      Carousel._jQueryInterface.call($(target), config)\n\n      if (slideIndex) {\n        $(target).data(DATA_KEY).to(slideIndex)\n      }\n\n      event.preventDefault()\n    }\n  }\n\n  /**\n   * ------------------------------------------------------------------------\n   * Data Api implementation\n   * ------------------------------------------------------------------------\n   */\n\n  $(document)\n    .on(Event.CLICK_DATA_API, Selector.DATA_SLIDE, Carousel._dataApiClickHandler)\n\n  $(window).on(Event.LOAD_DATA_API, () => {\n    $(Selector.DATA_RIDE).each(function () {\n      const $carousel = $(this)\n      Carousel._jQueryInterface.call($carousel, $carousel.data())\n    })\n  })\n\n  /**\n   * ------------------------------------------------------------------------\n   * jQuery\n   * ------------------------------------------------------------------------\n   */\n\n  $.fn[NAME] = Carousel._jQueryInterface\n  $.fn[NAME].Constructor = Carousel\n  $.fn[NAME].noConflict = function () {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Carousel._jQueryInterface\n  }\n\n  return Carousel\n})($)\n\nexport default Carousel\n", "import $ from 'jquery'\nimport Util from './util'\n\n/**\n * --------------------------------------------------------------------------\n * <PERSON>tra<PERSON> (v4.0.0): collapse.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst Collapse = (($) => {\n  /**\n   * ------------------------------------------------------------------------\n   * Constants\n   * ------------------------------------------------------------------------\n   */\n\n  const NAME                = 'collapse'\n  const VERSION             = '4.0.0'\n  const DATA_KEY            = 'bs.collapse'\n  const EVENT_KEY           = `.${DATA_KEY}`\n  const DATA_API_KEY        = '.data-api'\n  const JQUERY_NO_CONFLICT  = $.fn[NAME]\n  const TRANSITION_DURATION = 600\n\n  const Default = {\n    toggle : true,\n    parent : ''\n  }\n\n  const DefaultType = {\n    toggle : 'boolean',\n    parent : '(string|element)'\n  }\n\n  const Event = {\n    SHOW           : `show${EVENT_KEY}`,\n    SHOWN          : `shown${EVENT_KEY}`,\n    HIDE           : `hide${EVENT_KEY}`,\n    HIDDEN         : `hidden${EVENT_KEY}`,\n    CLICK_DATA_API : `click${EVENT_KEY}${DATA_API_KEY}`\n  }\n\n  const ClassName = {\n    SHOW       : 'show',\n    COLLAPSE   : 'collapse',\n    COLLAPSING : 'collapsing',\n    COLLAPSED  : 'collapsed'\n  }\n\n  const Dimension = {\n    WIDTH  : 'width',\n    HEIGHT : 'height'\n  }\n\n  const Selector = {\n    ACTIVES     : '.show, .collapsing',\n    DATA_TOGGLE : '[data-toggle=\"collapse\"]'\n  }\n\n  /**\n   * ------------------------------------------------------------------------\n   * Class Definition\n   * ------------------------------------------------------------------------\n   */\n\n  class Collapse {\n    constructor(element, config) {\n      this._isTransitioning = false\n      this._element         = element\n      this._config          = this._getConfig(config)\n      this._triggerArray    = $.makeArray($(\n        `[data-toggle=\"collapse\"][href=\"#${element.id}\"],` +\n        `[data-toggle=\"collapse\"][data-target=\"#${element.id}\"]`\n      ))\n      const tabToggles = $(Selector.DATA_TOGGLE)\n      for (let i = 0; i < tabToggles.length; i++) {\n        const elem = tabToggles[i]\n        const selector = Util.getSelectorFromElement(elem)\n        if (selector !== null && $(selector).filter(element).length > 0) {\n          this._selector = selector\n          this._triggerArray.push(elem)\n        }\n      }\n\n      this._parent = this._config.parent ? this._getParent() : null\n\n      if (!this._config.parent) {\n        this._addAriaAndCollapsedClass(this._element, this._triggerArray)\n      }\n\n      if (this._config.toggle) {\n        this.toggle()\n      }\n    }\n\n    // Getters\n\n    static get VERSION() {\n      return VERSION\n    }\n\n    static get Default() {\n      return Default\n    }\n\n    // Public\n\n    toggle() {\n      if ($(this._element).hasClass(ClassName.SHOW)) {\n        this.hide()\n      } else {\n        this.show()\n      }\n    }\n\n    show() {\n      if (this._isTransitioning ||\n        $(this._element).hasClass(ClassName.SHOW)) {\n        return\n      }\n\n      let actives\n      let activesData\n\n      if (this._parent) {\n        actives = $.makeArray(\n          $(this._parent)\n            .find(Selector.ACTIVES)\n            .filter(`[data-parent=\"${this._config.parent}\"]`)\n        )\n        if (actives.length === 0) {\n          actives = null\n        }\n      }\n\n      if (actives) {\n        activesData = $(actives).not(this._selector).data(DATA_KEY)\n        if (activesData && activesData._isTransitioning) {\n          return\n        }\n      }\n\n      const startEvent = $.Event(Event.SHOW)\n      $(this._element).trigger(startEvent)\n      if (startEvent.isDefaultPrevented()) {\n        return\n      }\n\n      if (actives) {\n        Collapse._jQueryInterface.call($(actives).not(this._selector), 'hide')\n        if (!activesData) {\n          $(actives).data(DATA_KEY, null)\n        }\n      }\n\n      const dimension = this._getDimension()\n\n      $(this._element)\n        .removeClass(ClassName.COLLAPSE)\n        .addClass(ClassName.COLLAPSING)\n\n      this._element.style[dimension] = 0\n\n      if (this._triggerArray.length > 0) {\n        $(this._triggerArray)\n          .removeClass(ClassName.COLLAPSED)\n          .attr('aria-expanded', true)\n      }\n\n      this.setTransitioning(true)\n\n      const complete = () => {\n        $(this._element)\n          .removeClass(ClassName.COLLAPSING)\n          .addClass(ClassName.COLLAPSE)\n          .addClass(ClassName.SHOW)\n\n        this._element.style[dimension] = ''\n\n        this.setTransitioning(false)\n\n        $(this._element).trigger(Event.SHOWN)\n      }\n\n      if (!Util.supportsTransitionEnd()) {\n        complete()\n        return\n      }\n\n      const capitalizedDimension = dimension[0].toUpperCase() + dimension.slice(1)\n      const scrollSize = `scroll${capitalizedDimension}`\n\n      $(this._element)\n        .one(Util.TRANSITION_END, complete)\n        .emulateTransitionEnd(TRANSITION_DURATION)\n\n      this._element.style[dimension] = `${this._element[scrollSize]}px`\n    }\n\n    hide() {\n      if (this._isTransitioning ||\n        !$(this._element).hasClass(ClassName.SHOW)) {\n        return\n      }\n\n      const startEvent = $.Event(Event.HIDE)\n      $(this._element).trigger(startEvent)\n      if (startEvent.isDefaultPrevented()) {\n        return\n      }\n\n      const dimension = this._getDimension()\n\n      this._element.style[dimension] = `${this._element.getBoundingClientRect()[dimension]}px`\n\n      Util.reflow(this._element)\n\n      $(this._element)\n        .addClass(ClassName.COLLAPSING)\n        .removeClass(ClassName.COLLAPSE)\n        .removeClass(ClassName.SHOW)\n\n      if (this._triggerArray.length > 0) {\n        for (let i = 0; i < this._triggerArray.length; i++) {\n          const trigger = this._triggerArray[i]\n          const selector = Util.getSelectorFromElement(trigger)\n          if (selector !== null) {\n            const $elem = $(selector)\n            if (!$elem.hasClass(ClassName.SHOW)) {\n              $(trigger).addClass(ClassName.COLLAPSED)\n                .attr('aria-expanded', false)\n            }\n          }\n        }\n      }\n\n      this.setTransitioning(true)\n\n      const complete = () => {\n        this.setTransitioning(false)\n        $(this._element)\n          .removeClass(ClassName.COLLAPSING)\n          .addClass(ClassName.COLLAPSE)\n          .trigger(Event.HIDDEN)\n      }\n\n      this._element.style[dimension] = ''\n\n      if (!Util.supportsTransitionEnd()) {\n        complete()\n        return\n      }\n\n      $(this._element)\n        .one(Util.TRANSITION_END, complete)\n        .emulateTransitionEnd(TRANSITION_DURATION)\n    }\n\n    setTransitioning(isTransitioning) {\n      this._isTransitioning = isTransitioning\n    }\n\n    dispose() {\n      $.removeData(this._element, DATA_KEY)\n\n      this._config          = null\n      this._parent          = null\n      this._element         = null\n      this._triggerArray    = null\n      this._isTransitioning = null\n    }\n\n    // Private\n\n    _getConfig(config) {\n      config = {\n        ...Default,\n        ...config\n      }\n      config.toggle = Boolean(config.toggle) // Coerce string values\n      Util.typeCheckConfig(NAME, config, DefaultType)\n      return config\n    }\n\n    _getDimension() {\n      const hasWidth = $(this._element).hasClass(Dimension.WIDTH)\n      return hasWidth ? Dimension.WIDTH : Dimension.HEIGHT\n    }\n\n    _getParent() {\n      let parent = null\n      if (Util.isElement(this._config.parent)) {\n        parent = this._config.parent\n\n        // It's a jQuery object\n        if (typeof this._config.parent.jquery !== 'undefined') {\n          parent = this._config.parent[0]\n        }\n      } else {\n        parent = $(this._config.parent)[0]\n      }\n\n      const selector =\n        `[data-toggle=\"collapse\"][data-parent=\"${this._config.parent}\"]`\n\n      $(parent).find(selector).each((i, element) => {\n        this._addAriaAndCollapsedClass(\n          Collapse._getTargetFromElement(element),\n          [element]\n        )\n      })\n\n      return parent\n    }\n\n    _addAriaAndCollapsedClass(element, triggerArray) {\n      if (element) {\n        const isOpen = $(element).hasClass(ClassName.SHOW)\n\n        if (triggerArray.length > 0) {\n          $(triggerArray)\n            .toggleClass(ClassName.COLLAPSED, !isOpen)\n            .attr('aria-expanded', isOpen)\n        }\n      }\n    }\n\n    // Static\n\n    static _getTargetFromElement(element) {\n      const selector = Util.getSelectorFromElement(element)\n      return selector ? $(selector)[0] : null\n    }\n\n    static _jQueryInterface(config) {\n      return this.each(function () {\n        const $this   = $(this)\n        let data      = $this.data(DATA_KEY)\n        const _config = {\n          ...Default,\n          ...$this.data(),\n          ...typeof config === 'object' && config\n        }\n\n        if (!data && _config.toggle && /show|hide/.test(config)) {\n          _config.toggle = false\n        }\n\n        if (!data) {\n          data = new Collapse(this, _config)\n          $this.data(DATA_KEY, data)\n        }\n\n        if (typeof config === 'string') {\n          if (typeof data[config] === 'undefined') {\n            throw new TypeError(`No method named \"${config}\"`)\n          }\n          data[config]()\n        }\n      })\n    }\n  }\n\n  /**\n   * ------------------------------------------------------------------------\n   * Data Api implementation\n   * ------------------------------------------------------------------------\n   */\n\n  $(document).on(Event.CLICK_DATA_API, Selector.DATA_TOGGLE, function (event) {\n    // preventDefault only for <a> elements (which change the URL) not inside the collapsible element\n    if (event.currentTarget.tagName === 'A') {\n      event.preventDefault()\n    }\n\n    const $trigger = $(this)\n    const selector = Util.getSelectorFromElement(this)\n    $(selector).each(function () {\n      const $target = $(this)\n      const data    = $target.data(DATA_KEY)\n      const config  = data ? 'toggle' : $trigger.data()\n      Collapse._jQueryInterface.call($target, config)\n    })\n  })\n\n  /**\n   * ------------------------------------------------------------------------\n   * jQuery\n   * ------------------------------------------------------------------------\n   */\n\n  $.fn[NAME] = Collapse._jQueryInterface\n  $.fn[NAME].Constructor = Collapse\n  $.fn[NAME].noConflict = function () {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Collapse._jQueryInterface\n  }\n\n  return Collapse\n})($)\n\nexport default Collapse\n", "/**!\n * @fileOverview Kickass library to create and place poppers near their reference elements.\n * @version 1.12.9\n * @license\n * Copyright (c) 2016 <PERSON> and contributors\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all\n * copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n * SOFTWARE.\n */\nvar isBrowser = typeof window !== 'undefined' && typeof document !== 'undefined';\nvar longerTimeoutBrowsers = ['Edge', 'Trident', 'Firefox'];\nvar timeoutDuration = 0;\nfor (var i = 0; i < longerTimeoutBrowsers.length; i += 1) {\n  if (isBrowser && navigator.userAgent.indexOf(longerTimeoutBrowsers[i]) >= 0) {\n    timeoutDuration = 1;\n    break;\n  }\n}\n\nfunction microtaskDebounce(fn) {\n  var called = false;\n  return function () {\n    if (called) {\n      return;\n    }\n    called = true;\n    window.Promise.resolve().then(function () {\n      called = false;\n      fn();\n    });\n  };\n}\n\nfunction taskDebounce(fn) {\n  var scheduled = false;\n  return function () {\n    if (!scheduled) {\n      scheduled = true;\n      setTimeout(function () {\n        scheduled = false;\n        fn();\n      }, timeoutDuration);\n    }\n  };\n}\n\nvar supportsMicroTasks = isBrowser && window.Promise;\n\n/**\n* Create a debounced version of a method, that's asynchronously deferred\n* but called in the minimum time possible.\n*\n* @method\n* @memberof Popper.Utils\n* @argument {Function} fn\n* @returns {Function}\n*/\nvar debounce = supportsMicroTasks ? microtaskDebounce : taskDebounce;\n\n/**\n * Check if the given variable is a function\n * @method\n * @memberof Popper.Utils\n * @argument {Any} functionToCheck - variable to check\n * @returns {Boolean} answer to: is a function?\n */\nfunction isFunction(functionToCheck) {\n  var getType = {};\n  return functionToCheck && getType.toString.call(functionToCheck) === '[object Function]';\n}\n\n/**\n * Get CSS computed property of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Eement} element\n * @argument {String} property\n */\nfunction getStyleComputedProperty(element, property) {\n  if (element.nodeType !== 1) {\n    return [];\n  }\n  // NOTE: 1 DOM access here\n  var css = getComputedStyle(element, null);\n  return property ? css[property] : css;\n}\n\n/**\n * Returns the parentNode or the host of the element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} parent\n */\nfunction getParentNode(element) {\n  if (element.nodeName === 'HTML') {\n    return element;\n  }\n  return element.parentNode || element.host;\n}\n\n/**\n * Returns the scrolling parent of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} scroll parent\n */\nfunction getScrollParent(element) {\n  // Return body, `getScroll` will take care to get the correct `scrollTop` from it\n  if (!element) {\n    return document.body;\n  }\n\n  switch (element.nodeName) {\n    case 'HTML':\n    case 'BODY':\n      return element.ownerDocument.body;\n    case '#document':\n      return element.body;\n  }\n\n  // Firefox want us to check `-x` and `-y` variations as well\n\n  var _getStyleComputedProp = getStyleComputedProperty(element),\n      overflow = _getStyleComputedProp.overflow,\n      overflowX = _getStyleComputedProp.overflowX,\n      overflowY = _getStyleComputedProp.overflowY;\n\n  if (/(auto|scroll)/.test(overflow + overflowY + overflowX)) {\n    return element;\n  }\n\n  return getScrollParent(getParentNode(element));\n}\n\n/**\n * Returns the offset parent of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} offset parent\n */\nfunction getOffsetParent(element) {\n  // NOTE: 1 DOM access here\n  var offsetParent = element && element.offsetParent;\n  var nodeName = offsetParent && offsetParent.nodeName;\n\n  if (!nodeName || nodeName === 'BODY' || nodeName === 'HTML') {\n    if (element) {\n      return element.ownerDocument.documentElement;\n    }\n\n    return document.documentElement;\n  }\n\n  // .offsetParent will return the closest TD or TABLE in case\n  // no offsetParent is present, I hate this job...\n  if (['TD', 'TABLE'].indexOf(offsetParent.nodeName) !== -1 && getStyleComputedProperty(offsetParent, 'position') === 'static') {\n    return getOffsetParent(offsetParent);\n  }\n\n  return offsetParent;\n}\n\nfunction isOffsetContainer(element) {\n  var nodeName = element.nodeName;\n\n  if (nodeName === 'BODY') {\n    return false;\n  }\n  return nodeName === 'HTML' || getOffsetParent(element.firstElementChild) === element;\n}\n\n/**\n * Finds the root node (document, shadowDOM root) of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} node\n * @returns {Element} root node\n */\nfunction getRoot(node) {\n  if (node.parentNode !== null) {\n    return getRoot(node.parentNode);\n  }\n\n  return node;\n}\n\n/**\n * Finds the offset parent common to the two provided nodes\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element1\n * @argument {Element} element2\n * @returns {Element} common offset parent\n */\nfunction findCommonOffsetParent(element1, element2) {\n  // This check is needed to avoid errors in case one of the elements isn't defined for any reason\n  if (!element1 || !element1.nodeType || !element2 || !element2.nodeType) {\n    return document.documentElement;\n  }\n\n  // Here we make sure to give as \"start\" the element that comes first in the DOM\n  var order = element1.compareDocumentPosition(element2) & Node.DOCUMENT_POSITION_FOLLOWING;\n  var start = order ? element1 : element2;\n  var end = order ? element2 : element1;\n\n  // Get common ancestor container\n  var range = document.createRange();\n  range.setStart(start, 0);\n  range.setEnd(end, 0);\n  var commonAncestorContainer = range.commonAncestorContainer;\n\n  // Both nodes are inside #document\n\n  if (element1 !== commonAncestorContainer && element2 !== commonAncestorContainer || start.contains(end)) {\n    if (isOffsetContainer(commonAncestorContainer)) {\n      return commonAncestorContainer;\n    }\n\n    return getOffsetParent(commonAncestorContainer);\n  }\n\n  // one of the nodes is inside shadowDOM, find which one\n  var element1root = getRoot(element1);\n  if (element1root.host) {\n    return findCommonOffsetParent(element1root.host, element2);\n  } else {\n    return findCommonOffsetParent(element1, getRoot(element2).host);\n  }\n}\n\n/**\n * Gets the scroll value of the given element in the given side (top and left)\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @argument {String} side `top` or `left`\n * @returns {number} amount of scrolled pixels\n */\nfunction getScroll(element) {\n  var side = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'top';\n\n  var upperSide = side === 'top' ? 'scrollTop' : 'scrollLeft';\n  var nodeName = element.nodeName;\n\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    var html = element.ownerDocument.documentElement;\n    var scrollingElement = element.ownerDocument.scrollingElement || html;\n    return scrollingElement[upperSide];\n  }\n\n  return element[upperSide];\n}\n\n/*\n * Sum or subtract the element scroll values (left and top) from a given rect object\n * @method\n * @memberof Popper.Utils\n * @param {Object} rect - Rect object you want to change\n * @param {HTMLElement} element - The element from the function reads the scroll values\n * @param {Boolean} subtract - set to true if you want to subtract the scroll values\n * @return {Object} rect - The modifier rect object\n */\nfunction includeScroll(rect, element) {\n  var subtract = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n\n  var scrollTop = getScroll(element, 'top');\n  var scrollLeft = getScroll(element, 'left');\n  var modifier = subtract ? -1 : 1;\n  rect.top += scrollTop * modifier;\n  rect.bottom += scrollTop * modifier;\n  rect.left += scrollLeft * modifier;\n  rect.right += scrollLeft * modifier;\n  return rect;\n}\n\n/*\n * Helper to detect borders of a given element\n * @method\n * @memberof Popper.Utils\n * @param {CSSStyleDeclaration} styles\n * Result of `getStyleComputedProperty` on the given element\n * @param {String} axis - `x` or `y`\n * @return {number} borders - The borders size of the given axis\n */\n\nfunction getBordersSize(styles, axis) {\n  var sideA = axis === 'x' ? 'Left' : 'Top';\n  var sideB = sideA === 'Left' ? 'Right' : 'Bottom';\n\n  return parseFloat(styles['border' + sideA + 'Width'], 10) + parseFloat(styles['border' + sideB + 'Width'], 10);\n}\n\n/**\n * Tells if you are running Internet Explorer 10\n * @method\n * @memberof Popper.Utils\n * @returns {Boolean} isIE10\n */\nvar isIE10 = undefined;\n\nvar isIE10$1 = function () {\n  if (isIE10 === undefined) {\n    isIE10 = navigator.appVersion.indexOf('MSIE 10') !== -1;\n  }\n  return isIE10;\n};\n\nfunction getSize(axis, body, html, computedStyle) {\n  return Math.max(body['offset' + axis], body['scroll' + axis], html['client' + axis], html['offset' + axis], html['scroll' + axis], isIE10$1() ? html['offset' + axis] + computedStyle['margin' + (axis === 'Height' ? 'Top' : 'Left')] + computedStyle['margin' + (axis === 'Height' ? 'Bottom' : 'Right')] : 0);\n}\n\nfunction getWindowSizes() {\n  var body = document.body;\n  var html = document.documentElement;\n  var computedStyle = isIE10$1() && getComputedStyle(html);\n\n  return {\n    height: getSize('Height', body, html, computedStyle),\n    width: getSize('Width', body, html, computedStyle)\n  };\n}\n\nvar classCallCheck = function (instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n};\n\nvar createClass = function () {\n  function defineProperties(target, props) {\n    for (var i = 0; i < props.length; i++) {\n      var descriptor = props[i];\n      descriptor.enumerable = descriptor.enumerable || false;\n      descriptor.configurable = true;\n      if (\"value\" in descriptor) descriptor.writable = true;\n      Object.defineProperty(target, descriptor.key, descriptor);\n    }\n  }\n\n  return function (Constructor, protoProps, staticProps) {\n    if (protoProps) defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) defineProperties(Constructor, staticProps);\n    return Constructor;\n  };\n}();\n\n\n\n\n\nvar defineProperty = function (obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n};\n\nvar _extends = Object.assign || function (target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i];\n\n    for (var key in source) {\n      if (Object.prototype.hasOwnProperty.call(source, key)) {\n        target[key] = source[key];\n      }\n    }\n  }\n\n  return target;\n};\n\n/**\n * Given element offsets, generate an output similar to getBoundingClientRect\n * @method\n * @memberof Popper.Utils\n * @argument {Object} offsets\n * @returns {Object} ClientRect like output\n */\nfunction getClientRect(offsets) {\n  return _extends({}, offsets, {\n    right: offsets.left + offsets.width,\n    bottom: offsets.top + offsets.height\n  });\n}\n\n/**\n * Get bounding client rect of given element\n * @method\n * @memberof Popper.Utils\n * @param {HTMLElement} element\n * @return {Object} client rect\n */\nfunction getBoundingClientRect(element) {\n  var rect = {};\n\n  // IE10 10 FIX: Please, don't ask, the element isn't\n  // considered in DOM in some circumstances...\n  // This isn't reproducible in IE10 compatibility mode of IE11\n  if (isIE10$1()) {\n    try {\n      rect = element.getBoundingClientRect();\n      var scrollTop = getScroll(element, 'top');\n      var scrollLeft = getScroll(element, 'left');\n      rect.top += scrollTop;\n      rect.left += scrollLeft;\n      rect.bottom += scrollTop;\n      rect.right += scrollLeft;\n    } catch (err) {}\n  } else {\n    rect = element.getBoundingClientRect();\n  }\n\n  var result = {\n    left: rect.left,\n    top: rect.top,\n    width: rect.right - rect.left,\n    height: rect.bottom - rect.top\n  };\n\n  // subtract scrollbar size from sizes\n  var sizes = element.nodeName === 'HTML' ? getWindowSizes() : {};\n  var width = sizes.width || element.clientWidth || result.right - result.left;\n  var height = sizes.height || element.clientHeight || result.bottom - result.top;\n\n  var horizScrollbar = element.offsetWidth - width;\n  var vertScrollbar = element.offsetHeight - height;\n\n  // if an hypothetical scrollbar is detected, we must be sure it's not a `border`\n  // we make this check conditional for performance reasons\n  if (horizScrollbar || vertScrollbar) {\n    var styles = getStyleComputedProperty(element);\n    horizScrollbar -= getBordersSize(styles, 'x');\n    vertScrollbar -= getBordersSize(styles, 'y');\n\n    result.width -= horizScrollbar;\n    result.height -= vertScrollbar;\n  }\n\n  return getClientRect(result);\n}\n\nfunction getOffsetRectRelativeToArbitraryNode(children, parent) {\n  var isIE10 = isIE10$1();\n  var isHTML = parent.nodeName === 'HTML';\n  var childrenRect = getBoundingClientRect(children);\n  var parentRect = getBoundingClientRect(parent);\n  var scrollParent = getScrollParent(children);\n\n  var styles = getStyleComputedProperty(parent);\n  var borderTopWidth = parseFloat(styles.borderTopWidth, 10);\n  var borderLeftWidth = parseFloat(styles.borderLeftWidth, 10);\n\n  var offsets = getClientRect({\n    top: childrenRect.top - parentRect.top - borderTopWidth,\n    left: childrenRect.left - parentRect.left - borderLeftWidth,\n    width: childrenRect.width,\n    height: childrenRect.height\n  });\n  offsets.marginTop = 0;\n  offsets.marginLeft = 0;\n\n  // Subtract margins of documentElement in case it's being used as parent\n  // we do this only on HTML because it's the only element that behaves\n  // differently when margins are applied to it. The margins are included in\n  // the box of the documentElement, in the other cases not.\n  if (!isIE10 && isHTML) {\n    var marginTop = parseFloat(styles.marginTop, 10);\n    var marginLeft = parseFloat(styles.marginLeft, 10);\n\n    offsets.top -= borderTopWidth - marginTop;\n    offsets.bottom -= borderTopWidth - marginTop;\n    offsets.left -= borderLeftWidth - marginLeft;\n    offsets.right -= borderLeftWidth - marginLeft;\n\n    // Attach marginTop and marginLeft because in some circumstances we may need them\n    offsets.marginTop = marginTop;\n    offsets.marginLeft = marginLeft;\n  }\n\n  if (isIE10 ? parent.contains(scrollParent) : parent === scrollParent && scrollParent.nodeName !== 'BODY') {\n    offsets = includeScroll(offsets, parent);\n  }\n\n  return offsets;\n}\n\nfunction getViewportOffsetRectRelativeToArtbitraryNode(element) {\n  var html = element.ownerDocument.documentElement;\n  var relativeOffset = getOffsetRectRelativeToArbitraryNode(element, html);\n  var width = Math.max(html.clientWidth, window.innerWidth || 0);\n  var height = Math.max(html.clientHeight, window.innerHeight || 0);\n\n  var scrollTop = getScroll(html);\n  var scrollLeft = getScroll(html, 'left');\n\n  var offset = {\n    top: scrollTop - relativeOffset.top + relativeOffset.marginTop,\n    left: scrollLeft - relativeOffset.left + relativeOffset.marginLeft,\n    width: width,\n    height: height\n  };\n\n  return getClientRect(offset);\n}\n\n/**\n * Check if the given element is fixed or is inside a fixed parent\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @argument {Element} customContainer\n * @returns {Boolean} answer to \"isFixed?\"\n */\nfunction isFixed(element) {\n  var nodeName = element.nodeName;\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    return false;\n  }\n  if (getStyleComputedProperty(element, 'position') === 'fixed') {\n    return true;\n  }\n  return isFixed(getParentNode(element));\n}\n\n/**\n * Computed the boundaries limits and return them\n * @method\n * @memberof Popper.Utils\n * @param {HTMLElement} popper\n * @param {HTMLElement} reference\n * @param {number} padding\n * @param {HTMLElement} boundariesElement - Element used to define the boundaries\n * @returns {Object} Coordinates of the boundaries\n */\nfunction getBoundaries(popper, reference, padding, boundariesElement) {\n  // NOTE: 1 DOM access here\n  var boundaries = { top: 0, left: 0 };\n  var offsetParent = findCommonOffsetParent(popper, reference);\n\n  // Handle viewport case\n  if (boundariesElement === 'viewport') {\n    boundaries = getViewportOffsetRectRelativeToArtbitraryNode(offsetParent);\n  } else {\n    // Handle other cases based on DOM element used as boundaries\n    var boundariesNode = void 0;\n    if (boundariesElement === 'scrollParent') {\n      boundariesNode = getScrollParent(getParentNode(reference));\n      if (boundariesNode.nodeName === 'BODY') {\n        boundariesNode = popper.ownerDocument.documentElement;\n      }\n    } else if (boundariesElement === 'window') {\n      boundariesNode = popper.ownerDocument.documentElement;\n    } else {\n      boundariesNode = boundariesElement;\n    }\n\n    var offsets = getOffsetRectRelativeToArbitraryNode(boundariesNode, offsetParent);\n\n    // In case of HTML, we need a different computation\n    if (boundariesNode.nodeName === 'HTML' && !isFixed(offsetParent)) {\n      var _getWindowSizes = getWindowSizes(),\n          height = _getWindowSizes.height,\n          width = _getWindowSizes.width;\n\n      boundaries.top += offsets.top - offsets.marginTop;\n      boundaries.bottom = height + offsets.top;\n      boundaries.left += offsets.left - offsets.marginLeft;\n      boundaries.right = width + offsets.left;\n    } else {\n      // for all the other DOM elements, this one is good\n      boundaries = offsets;\n    }\n  }\n\n  // Add paddings\n  boundaries.left += padding;\n  boundaries.top += padding;\n  boundaries.right -= padding;\n  boundaries.bottom -= padding;\n\n  return boundaries;\n}\n\nfunction getArea(_ref) {\n  var width = _ref.width,\n      height = _ref.height;\n\n  return width * height;\n}\n\n/**\n * Utility used to transform the `auto` placement to the placement with more\n * available space.\n * @method\n * @memberof Popper.Utils\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction computeAutoPlacement(placement, refRect, popper, reference, boundariesElement) {\n  var padding = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : 0;\n\n  if (placement.indexOf('auto') === -1) {\n    return placement;\n  }\n\n  var boundaries = getBoundaries(popper, reference, padding, boundariesElement);\n\n  var rects = {\n    top: {\n      width: boundaries.width,\n      height: refRect.top - boundaries.top\n    },\n    right: {\n      width: boundaries.right - refRect.right,\n      height: boundaries.height\n    },\n    bottom: {\n      width: boundaries.width,\n      height: boundaries.bottom - refRect.bottom\n    },\n    left: {\n      width: refRect.left - boundaries.left,\n      height: boundaries.height\n    }\n  };\n\n  var sortedAreas = Object.keys(rects).map(function (key) {\n    return _extends({\n      key: key\n    }, rects[key], {\n      area: getArea(rects[key])\n    });\n  }).sort(function (a, b) {\n    return b.area - a.area;\n  });\n\n  var filteredAreas = sortedAreas.filter(function (_ref2) {\n    var width = _ref2.width,\n        height = _ref2.height;\n    return width >= popper.clientWidth && height >= popper.clientHeight;\n  });\n\n  var computedPlacement = filteredAreas.length > 0 ? filteredAreas[0].key : sortedAreas[0].key;\n\n  var variation = placement.split('-')[1];\n\n  return computedPlacement + (variation ? '-' + variation : '');\n}\n\n/**\n * Get offsets to the reference element\n * @method\n * @memberof Popper.Utils\n * @param {Object} state\n * @param {Element} popper - the popper element\n * @param {Element} reference - the reference element (the popper will be relative to this)\n * @returns {Object} An object containing the offsets which will be applied to the popper\n */\nfunction getReferenceOffsets(state, popper, reference) {\n  var commonOffsetParent = findCommonOffsetParent(popper, reference);\n  return getOffsetRectRelativeToArbitraryNode(reference, commonOffsetParent);\n}\n\n/**\n * Get the outer sizes of the given element (offset size + margins)\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Object} object containing width and height properties\n */\nfunction getOuterSizes(element) {\n  var styles = getComputedStyle(element);\n  var x = parseFloat(styles.marginTop) + parseFloat(styles.marginBottom);\n  var y = parseFloat(styles.marginLeft) + parseFloat(styles.marginRight);\n  var result = {\n    width: element.offsetWidth + y,\n    height: element.offsetHeight + x\n  };\n  return result;\n}\n\n/**\n * Get the opposite placement of the given one\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement\n * @returns {String} flipped placement\n */\nfunction getOppositePlacement(placement) {\n  var hash = { left: 'right', right: 'left', bottom: 'top', top: 'bottom' };\n  return placement.replace(/left|right|bottom|top/g, function (matched) {\n    return hash[matched];\n  });\n}\n\n/**\n * Get offsets to the popper\n * @method\n * @memberof Popper.Utils\n * @param {Object} position - CSS position the Popper will get applied\n * @param {HTMLElement} popper - the popper element\n * @param {Object} referenceOffsets - the reference offsets (the popper will be relative to this)\n * @param {String} placement - one of the valid placement options\n * @returns {Object} popperOffsets - An object containing the offsets which will be applied to the popper\n */\nfunction getPopperOffsets(popper, referenceOffsets, placement) {\n  placement = placement.split('-')[0];\n\n  // Get popper node sizes\n  var popperRect = getOuterSizes(popper);\n\n  // Add position, width and height to our offsets object\n  var popperOffsets = {\n    width: popperRect.width,\n    height: popperRect.height\n  };\n\n  // depending by the popper placement we have to compute its offsets slightly differently\n  var isHoriz = ['right', 'left'].indexOf(placement) !== -1;\n  var mainSide = isHoriz ? 'top' : 'left';\n  var secondarySide = isHoriz ? 'left' : 'top';\n  var measurement = isHoriz ? 'height' : 'width';\n  var secondaryMeasurement = !isHoriz ? 'height' : 'width';\n\n  popperOffsets[mainSide] = referenceOffsets[mainSide] + referenceOffsets[measurement] / 2 - popperRect[measurement] / 2;\n  if (placement === secondarySide) {\n    popperOffsets[secondarySide] = referenceOffsets[secondarySide] - popperRect[secondaryMeasurement];\n  } else {\n    popperOffsets[secondarySide] = referenceOffsets[getOppositePlacement(secondarySide)];\n  }\n\n  return popperOffsets;\n}\n\n/**\n * Mimics the `find` method of Array\n * @method\n * @memberof Popper.Utils\n * @argument {Array} arr\n * @argument prop\n * @argument value\n * @returns index or -1\n */\nfunction find(arr, check) {\n  // use native find if supported\n  if (Array.prototype.find) {\n    return arr.find(check);\n  }\n\n  // use `filter` to obtain the same behavior of `find`\n  return arr.filter(check)[0];\n}\n\n/**\n * Return the index of the matching object\n * @method\n * @memberof Popper.Utils\n * @argument {Array} arr\n * @argument prop\n * @argument value\n * @returns index or -1\n */\nfunction findIndex(arr, prop, value) {\n  // use native findIndex if supported\n  if (Array.prototype.findIndex) {\n    return arr.findIndex(function (cur) {\n      return cur[prop] === value;\n    });\n  }\n\n  // use `find` + `indexOf` if `findIndex` isn't supported\n  var match = find(arr, function (obj) {\n    return obj[prop] === value;\n  });\n  return arr.indexOf(match);\n}\n\n/**\n * Loop trough the list of modifiers and run them in order,\n * each of them will then edit the data object.\n * @method\n * @memberof Popper.Utils\n * @param {dataObject} data\n * @param {Array} modifiers\n * @param {String} ends - Optional modifier name used as stopper\n * @returns {dataObject}\n */\nfunction runModifiers(modifiers, data, ends) {\n  var modifiersToRun = ends === undefined ? modifiers : modifiers.slice(0, findIndex(modifiers, 'name', ends));\n\n  modifiersToRun.forEach(function (modifier) {\n    if (modifier['function']) {\n      // eslint-disable-line dot-notation\n      console.warn('`modifier.function` is deprecated, use `modifier.fn`!');\n    }\n    var fn = modifier['function'] || modifier.fn; // eslint-disable-line dot-notation\n    if (modifier.enabled && isFunction(fn)) {\n      // Add properties to offsets to make them a complete clientRect object\n      // we do this before each modifier to make sure the previous one doesn't\n      // mess with these values\n      data.offsets.popper = getClientRect(data.offsets.popper);\n      data.offsets.reference = getClientRect(data.offsets.reference);\n\n      data = fn(data, modifier);\n    }\n  });\n\n  return data;\n}\n\n/**\n * Updates the position of the popper, computing the new offsets and applying\n * the new style.<br />\n * Prefer `scheduleUpdate` over `update` because of performance reasons.\n * @method\n * @memberof Popper\n */\nfunction update() {\n  // if popper is destroyed, don't perform any further update\n  if (this.state.isDestroyed) {\n    return;\n  }\n\n  var data = {\n    instance: this,\n    styles: {},\n    arrowStyles: {},\n    attributes: {},\n    flipped: false,\n    offsets: {}\n  };\n\n  // compute reference element offsets\n  data.offsets.reference = getReferenceOffsets(this.state, this.popper, this.reference);\n\n  // compute auto placement, store placement inside the data object,\n  // modifiers will be able to edit `placement` if needed\n  // and refer to originalPlacement to know the original value\n  data.placement = computeAutoPlacement(this.options.placement, data.offsets.reference, this.popper, this.reference, this.options.modifiers.flip.boundariesElement, this.options.modifiers.flip.padding);\n\n  // store the computed placement inside `originalPlacement`\n  data.originalPlacement = data.placement;\n\n  // compute the popper offsets\n  data.offsets.popper = getPopperOffsets(this.popper, data.offsets.reference, data.placement);\n  data.offsets.popper.position = 'absolute';\n\n  // run the modifiers\n  data = runModifiers(this.modifiers, data);\n\n  // the first `update` will call `onCreate` callback\n  // the other ones will call `onUpdate` callback\n  if (!this.state.isCreated) {\n    this.state.isCreated = true;\n    this.options.onCreate(data);\n  } else {\n    this.options.onUpdate(data);\n  }\n}\n\n/**\n * Helper used to know if the given modifier is enabled.\n * @method\n * @memberof Popper.Utils\n * @returns {Boolean}\n */\nfunction isModifierEnabled(modifiers, modifierName) {\n  return modifiers.some(function (_ref) {\n    var name = _ref.name,\n        enabled = _ref.enabled;\n    return enabled && name === modifierName;\n  });\n}\n\n/**\n * Get the prefixed supported property name\n * @method\n * @memberof Popper.Utils\n * @argument {String} property (camelCase)\n * @returns {String} prefixed property (camelCase or PascalCase, depending on the vendor prefix)\n */\nfunction getSupportedPropertyName(property) {\n  var prefixes = [false, 'ms', 'Webkit', 'Moz', 'O'];\n  var upperProp = property.charAt(0).toUpperCase() + property.slice(1);\n\n  for (var i = 0; i < prefixes.length - 1; i++) {\n    var prefix = prefixes[i];\n    var toCheck = prefix ? '' + prefix + upperProp : property;\n    if (typeof document.body.style[toCheck] !== 'undefined') {\n      return toCheck;\n    }\n  }\n  return null;\n}\n\n/**\n * Destroy the popper\n * @method\n * @memberof Popper\n */\nfunction destroy() {\n  this.state.isDestroyed = true;\n\n  // touch DOM only if `applyStyle` modifier is enabled\n  if (isModifierEnabled(this.modifiers, 'applyStyle')) {\n    this.popper.removeAttribute('x-placement');\n    this.popper.style.left = '';\n    this.popper.style.position = '';\n    this.popper.style.top = '';\n    this.popper.style[getSupportedPropertyName('transform')] = '';\n  }\n\n  this.disableEventListeners();\n\n  // remove the popper if user explicity asked for the deletion on destroy\n  // do not use `remove` because IE11 doesn't support it\n  if (this.options.removeOnDestroy) {\n    this.popper.parentNode.removeChild(this.popper);\n  }\n  return this;\n}\n\n/**\n * Get the window associated with the element\n * @argument {Element} element\n * @returns {Window}\n */\nfunction getWindow(element) {\n  var ownerDocument = element.ownerDocument;\n  return ownerDocument ? ownerDocument.defaultView : window;\n}\n\nfunction attachToScrollParents(scrollParent, event, callback, scrollParents) {\n  var isBody = scrollParent.nodeName === 'BODY';\n  var target = isBody ? scrollParent.ownerDocument.defaultView : scrollParent;\n  target.addEventListener(event, callback, { passive: true });\n\n  if (!isBody) {\n    attachToScrollParents(getScrollParent(target.parentNode), event, callback, scrollParents);\n  }\n  scrollParents.push(target);\n}\n\n/**\n * Setup needed event listeners used to update the popper position\n * @method\n * @memberof Popper.Utils\n * @private\n */\nfunction setupEventListeners(reference, options, state, updateBound) {\n  // Resize event listener on window\n  state.updateBound = updateBound;\n  getWindow(reference).addEventListener('resize', state.updateBound, { passive: true });\n\n  // Scroll event listener on scroll parents\n  var scrollElement = getScrollParent(reference);\n  attachToScrollParents(scrollElement, 'scroll', state.updateBound, state.scrollParents);\n  state.scrollElement = scrollElement;\n  state.eventsEnabled = true;\n\n  return state;\n}\n\n/**\n * It will add resize/scroll events and start recalculating\n * position of the popper element when they are triggered.\n * @method\n * @memberof Popper\n */\nfunction enableEventListeners() {\n  if (!this.state.eventsEnabled) {\n    this.state = setupEventListeners(this.reference, this.options, this.state, this.scheduleUpdate);\n  }\n}\n\n/**\n * Remove event listeners used to update the popper position\n * @method\n * @memberof Popper.Utils\n * @private\n */\nfunction removeEventListeners(reference, state) {\n  // Remove resize event listener on window\n  getWindow(reference).removeEventListener('resize', state.updateBound);\n\n  // Remove scroll event listener on scroll parents\n  state.scrollParents.forEach(function (target) {\n    target.removeEventListener('scroll', state.updateBound);\n  });\n\n  // Reset state\n  state.updateBound = null;\n  state.scrollParents = [];\n  state.scrollElement = null;\n  state.eventsEnabled = false;\n  return state;\n}\n\n/**\n * It will remove resize/scroll events and won't recalculate popper position\n * when they are triggered. It also won't trigger onUpdate callback anymore,\n * unless you call `update` method manually.\n * @method\n * @memberof Popper\n */\nfunction disableEventListeners() {\n  if (this.state.eventsEnabled) {\n    cancelAnimationFrame(this.scheduleUpdate);\n    this.state = removeEventListeners(this.reference, this.state);\n  }\n}\n\n/**\n * Tells if a given input is a number\n * @method\n * @memberof Popper.Utils\n * @param {*} input to check\n * @return {Boolean}\n */\nfunction isNumeric(n) {\n  return n !== '' && !isNaN(parseFloat(n)) && isFinite(n);\n}\n\n/**\n * Set the style to the given popper\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element - Element to apply the style to\n * @argument {Object} styles\n * Object with a list of properties and values which will be applied to the element\n */\nfunction setStyles(element, styles) {\n  Object.keys(styles).forEach(function (prop) {\n    var unit = '';\n    // add unit if the value is numeric and is one of the following\n    if (['width', 'height', 'top', 'right', 'bottom', 'left'].indexOf(prop) !== -1 && isNumeric(styles[prop])) {\n      unit = 'px';\n    }\n    element.style[prop] = styles[prop] + unit;\n  });\n}\n\n/**\n * Set the attributes to the given popper\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element - Element to apply the attributes to\n * @argument {Object} styles\n * Object with a list of properties and values which will be applied to the element\n */\nfunction setAttributes(element, attributes) {\n  Object.keys(attributes).forEach(function (prop) {\n    var value = attributes[prop];\n    if (value !== false) {\n      element.setAttribute(prop, attributes[prop]);\n    } else {\n      element.removeAttribute(prop);\n    }\n  });\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} data.styles - List of style properties - values to apply to popper element\n * @argument {Object} data.attributes - List of attribute properties - values to apply to popper element\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The same data object\n */\nfunction applyStyle(data) {\n  // any property present in `data.styles` will be applied to the popper,\n  // in this way we can make the 3rd party modifiers add custom styles to it\n  // Be aware, modifiers could override the properties defined in the previous\n  // lines of this modifier!\n  setStyles(data.instance.popper, data.styles);\n\n  // any property present in `data.attributes` will be applied to the popper,\n  // they will be set as HTML attributes of the element\n  setAttributes(data.instance.popper, data.attributes);\n\n  // if arrowElement is defined and arrowStyles has some properties\n  if (data.arrowElement && Object.keys(data.arrowStyles).length) {\n    setStyles(data.arrowElement, data.arrowStyles);\n  }\n\n  return data;\n}\n\n/**\n * Set the x-placement attribute before everything else because it could be used\n * to add margins to the popper margins needs to be calculated to get the\n * correct popper offsets.\n * @method\n * @memberof Popper.modifiers\n * @param {HTMLElement} reference - The reference element used to position the popper\n * @param {HTMLElement} popper - The HTML element used as popper.\n * @param {Object} options - Popper.js options\n */\nfunction applyStyleOnLoad(reference, popper, options, modifierOptions, state) {\n  // compute reference element offsets\n  var referenceOffsets = getReferenceOffsets(state, popper, reference);\n\n  // compute auto placement, store placement inside the data object,\n  // modifiers will be able to edit `placement` if needed\n  // and refer to originalPlacement to know the original value\n  var placement = computeAutoPlacement(options.placement, referenceOffsets, popper, reference, options.modifiers.flip.boundariesElement, options.modifiers.flip.padding);\n\n  popper.setAttribute('x-placement', placement);\n\n  // Apply `position` to popper before anything else because\n  // without the position applied we can't guarantee correct computations\n  setStyles(popper, { position: 'absolute' });\n\n  return options;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction computeStyle(data, options) {\n  var x = options.x,\n      y = options.y;\n  var popper = data.offsets.popper;\n\n  // Remove this legacy support in Popper.js v2\n\n  var legacyGpuAccelerationOption = find(data.instance.modifiers, function (modifier) {\n    return modifier.name === 'applyStyle';\n  }).gpuAcceleration;\n  if (legacyGpuAccelerationOption !== undefined) {\n    console.warn('WARNING: `gpuAcceleration` option moved to `computeStyle` modifier and will not be supported in future versions of Popper.js!');\n  }\n  var gpuAcceleration = legacyGpuAccelerationOption !== undefined ? legacyGpuAccelerationOption : options.gpuAcceleration;\n\n  var offsetParent = getOffsetParent(data.instance.popper);\n  var offsetParentRect = getBoundingClientRect(offsetParent);\n\n  // Styles\n  var styles = {\n    position: popper.position\n  };\n\n  // floor sides to avoid blurry text\n  var offsets = {\n    left: Math.floor(popper.left),\n    top: Math.floor(popper.top),\n    bottom: Math.floor(popper.bottom),\n    right: Math.floor(popper.right)\n  };\n\n  var sideA = x === 'bottom' ? 'top' : 'bottom';\n  var sideB = y === 'right' ? 'left' : 'right';\n\n  // if gpuAcceleration is set to `true` and transform is supported,\n  //  we use `translate3d` to apply the position to the popper we\n  // automatically use the supported prefixed version if needed\n  var prefixedProperty = getSupportedPropertyName('transform');\n\n  // now, let's make a step back and look at this code closely (wtf?)\n  // If the content of the popper grows once it's been positioned, it\n  // may happen that the popper gets misplaced because of the new content\n  // overflowing its reference element\n  // To avoid this problem, we provide two options (x and y), which allow\n  // the consumer to define the offset origin.\n  // If we position a popper on top of a reference element, we can set\n  // `x` to `top` to make the popper grow towards its top instead of\n  // its bottom.\n  var left = void 0,\n      top = void 0;\n  if (sideA === 'bottom') {\n    top = -offsetParentRect.height + offsets.bottom;\n  } else {\n    top = offsets.top;\n  }\n  if (sideB === 'right') {\n    left = -offsetParentRect.width + offsets.right;\n  } else {\n    left = offsets.left;\n  }\n  if (gpuAcceleration && prefixedProperty) {\n    styles[prefixedProperty] = 'translate3d(' + left + 'px, ' + top + 'px, 0)';\n    styles[sideA] = 0;\n    styles[sideB] = 0;\n    styles.willChange = 'transform';\n  } else {\n    // othwerise, we use the standard `top`, `left`, `bottom` and `right` properties\n    var invertTop = sideA === 'bottom' ? -1 : 1;\n    var invertLeft = sideB === 'right' ? -1 : 1;\n    styles[sideA] = top * invertTop;\n    styles[sideB] = left * invertLeft;\n    styles.willChange = sideA + ', ' + sideB;\n  }\n\n  // Attributes\n  var attributes = {\n    'x-placement': data.placement\n  };\n\n  // Update `data` attributes, styles and arrowStyles\n  data.attributes = _extends({}, attributes, data.attributes);\n  data.styles = _extends({}, styles, data.styles);\n  data.arrowStyles = _extends({}, data.offsets.arrow, data.arrowStyles);\n\n  return data;\n}\n\n/**\n * Helper used to know if the given modifier depends from another one.<br />\n * It checks if the needed modifier is listed and enabled.\n * @method\n * @memberof Popper.Utils\n * @param {Array} modifiers - list of modifiers\n * @param {String} requestingName - name of requesting modifier\n * @param {String} requestedName - name of requested modifier\n * @returns {Boolean}\n */\nfunction isModifierRequired(modifiers, requestingName, requestedName) {\n  var requesting = find(modifiers, function (_ref) {\n    var name = _ref.name;\n    return name === requestingName;\n  });\n\n  var isRequired = !!requesting && modifiers.some(function (modifier) {\n    return modifier.name === requestedName && modifier.enabled && modifier.order < requesting.order;\n  });\n\n  if (!isRequired) {\n    var _requesting = '`' + requestingName + '`';\n    var requested = '`' + requestedName + '`';\n    console.warn(requested + ' modifier is required by ' + _requesting + ' modifier in order to work, be sure to include it before ' + _requesting + '!');\n  }\n  return isRequired;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction arrow(data, options) {\n  var _data$offsets$arrow;\n\n  // arrow depends on keepTogether in order to work\n  if (!isModifierRequired(data.instance.modifiers, 'arrow', 'keepTogether')) {\n    return data;\n  }\n\n  var arrowElement = options.element;\n\n  // if arrowElement is a string, suppose it's a CSS selector\n  if (typeof arrowElement === 'string') {\n    arrowElement = data.instance.popper.querySelector(arrowElement);\n\n    // if arrowElement is not found, don't run the modifier\n    if (!arrowElement) {\n      return data;\n    }\n  } else {\n    // if the arrowElement isn't a query selector we must check that the\n    // provided DOM node is child of its popper node\n    if (!data.instance.popper.contains(arrowElement)) {\n      console.warn('WARNING: `arrow.element` must be child of its popper element!');\n      return data;\n    }\n  }\n\n  var placement = data.placement.split('-')[0];\n  var _data$offsets = data.offsets,\n      popper = _data$offsets.popper,\n      reference = _data$offsets.reference;\n\n  var isVertical = ['left', 'right'].indexOf(placement) !== -1;\n\n  var len = isVertical ? 'height' : 'width';\n  var sideCapitalized = isVertical ? 'Top' : 'Left';\n  var side = sideCapitalized.toLowerCase();\n  var altSide = isVertical ? 'left' : 'top';\n  var opSide = isVertical ? 'bottom' : 'right';\n  var arrowElementSize = getOuterSizes(arrowElement)[len];\n\n  //\n  // extends keepTogether behavior making sure the popper and its\n  // reference have enough pixels in conjuction\n  //\n\n  // top/left side\n  if (reference[opSide] - arrowElementSize < popper[side]) {\n    data.offsets.popper[side] -= popper[side] - (reference[opSide] - arrowElementSize);\n  }\n  // bottom/right side\n  if (reference[side] + arrowElementSize > popper[opSide]) {\n    data.offsets.popper[side] += reference[side] + arrowElementSize - popper[opSide];\n  }\n  data.offsets.popper = getClientRect(data.offsets.popper);\n\n  // compute center of the popper\n  var center = reference[side] + reference[len] / 2 - arrowElementSize / 2;\n\n  // Compute the sideValue using the updated popper offsets\n  // take popper margin in account because we don't have this info available\n  var css = getStyleComputedProperty(data.instance.popper);\n  var popperMarginSide = parseFloat(css['margin' + sideCapitalized], 10);\n  var popperBorderSide = parseFloat(css['border' + sideCapitalized + 'Width'], 10);\n  var sideValue = center - data.offsets.popper[side] - popperMarginSide - popperBorderSide;\n\n  // prevent arrowElement from being placed not contiguously to its popper\n  sideValue = Math.max(Math.min(popper[len] - arrowElementSize, sideValue), 0);\n\n  data.arrowElement = arrowElement;\n  data.offsets.arrow = (_data$offsets$arrow = {}, defineProperty(_data$offsets$arrow, side, Math.round(sideValue)), defineProperty(_data$offsets$arrow, altSide, ''), _data$offsets$arrow);\n\n  return data;\n}\n\n/**\n * Get the opposite placement variation of the given one\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement variation\n * @returns {String} flipped placement variation\n */\nfunction getOppositeVariation(variation) {\n  if (variation === 'end') {\n    return 'start';\n  } else if (variation === 'start') {\n    return 'end';\n  }\n  return variation;\n}\n\n/**\n * List of accepted placements to use as values of the `placement` option.<br />\n * Valid placements are:\n * - `auto`\n * - `top`\n * - `right`\n * - `bottom`\n * - `left`\n *\n * Each placement can have a variation from this list:\n * - `-start`\n * - `-end`\n *\n * Variations are interpreted easily if you think of them as the left to right\n * written languages. Horizontally (`top` and `bottom`), `start` is left and `end`\n * is right.<br />\n * Vertically (`left` and `right`), `start` is top and `end` is bottom.\n *\n * Some valid examples are:\n * - `top-end` (on top of reference, right aligned)\n * - `right-start` (on right of reference, top aligned)\n * - `bottom` (on bottom, centered)\n * - `auto-right` (on the side with more space available, alignment depends by placement)\n *\n * @static\n * @type {Array}\n * @enum {String}\n * @readonly\n * @method placements\n * @memberof Popper\n */\nvar placements = ['auto-start', 'auto', 'auto-end', 'top-start', 'top', 'top-end', 'right-start', 'right', 'right-end', 'bottom-end', 'bottom', 'bottom-start', 'left-end', 'left', 'left-start'];\n\n// Get rid of `auto` `auto-start` and `auto-end`\nvar validPlacements = placements.slice(3);\n\n/**\n * Given an initial placement, returns all the subsequent placements\n * clockwise (or counter-clockwise).\n *\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement - A valid placement (it accepts variations)\n * @argument {Boolean} counter - Set to true to walk the placements counterclockwise\n * @returns {Array} placements including their variations\n */\nfunction clockwise(placement) {\n  var counter = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n\n  var index = validPlacements.indexOf(placement);\n  var arr = validPlacements.slice(index + 1).concat(validPlacements.slice(0, index));\n  return counter ? arr.reverse() : arr;\n}\n\nvar BEHAVIORS = {\n  FLIP: 'flip',\n  CLOCKWISE: 'clockwise',\n  COUNTERCLOCKWISE: 'counterclockwise'\n};\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction flip(data, options) {\n  // if `inner` modifier is enabled, we can't use the `flip` modifier\n  if (isModifierEnabled(data.instance.modifiers, 'inner')) {\n    return data;\n  }\n\n  if (data.flipped && data.placement === data.originalPlacement) {\n    // seems like flip is trying to loop, probably there's not enough space on any of the flippable sides\n    return data;\n  }\n\n  var boundaries = getBoundaries(data.instance.popper, data.instance.reference, options.padding, options.boundariesElement);\n\n  var placement = data.placement.split('-')[0];\n  var placementOpposite = getOppositePlacement(placement);\n  var variation = data.placement.split('-')[1] || '';\n\n  var flipOrder = [];\n\n  switch (options.behavior) {\n    case BEHAVIORS.FLIP:\n      flipOrder = [placement, placementOpposite];\n      break;\n    case BEHAVIORS.CLOCKWISE:\n      flipOrder = clockwise(placement);\n      break;\n    case BEHAVIORS.COUNTERCLOCKWISE:\n      flipOrder = clockwise(placement, true);\n      break;\n    default:\n      flipOrder = options.behavior;\n  }\n\n  flipOrder.forEach(function (step, index) {\n    if (placement !== step || flipOrder.length === index + 1) {\n      return data;\n    }\n\n    placement = data.placement.split('-')[0];\n    placementOpposite = getOppositePlacement(placement);\n\n    var popperOffsets = data.offsets.popper;\n    var refOffsets = data.offsets.reference;\n\n    // using floor because the reference offsets may contain decimals we are not going to consider here\n    var floor = Math.floor;\n    var overlapsRef = placement === 'left' && floor(popperOffsets.right) > floor(refOffsets.left) || placement === 'right' && floor(popperOffsets.left) < floor(refOffsets.right) || placement === 'top' && floor(popperOffsets.bottom) > floor(refOffsets.top) || placement === 'bottom' && floor(popperOffsets.top) < floor(refOffsets.bottom);\n\n    var overflowsLeft = floor(popperOffsets.left) < floor(boundaries.left);\n    var overflowsRight = floor(popperOffsets.right) > floor(boundaries.right);\n    var overflowsTop = floor(popperOffsets.top) < floor(boundaries.top);\n    var overflowsBottom = floor(popperOffsets.bottom) > floor(boundaries.bottom);\n\n    var overflowsBoundaries = placement === 'left' && overflowsLeft || placement === 'right' && overflowsRight || placement === 'top' && overflowsTop || placement === 'bottom' && overflowsBottom;\n\n    // flip the variation if required\n    var isVertical = ['top', 'bottom'].indexOf(placement) !== -1;\n    var flippedVariation = !!options.flipVariations && (isVertical && variation === 'start' && overflowsLeft || isVertical && variation === 'end' && overflowsRight || !isVertical && variation === 'start' && overflowsTop || !isVertical && variation === 'end' && overflowsBottom);\n\n    if (overlapsRef || overflowsBoundaries || flippedVariation) {\n      // this boolean to detect any flip loop\n      data.flipped = true;\n\n      if (overlapsRef || overflowsBoundaries) {\n        placement = flipOrder[index + 1];\n      }\n\n      if (flippedVariation) {\n        variation = getOppositeVariation(variation);\n      }\n\n      data.placement = placement + (variation ? '-' + variation : '');\n\n      // this object contains `position`, we want to preserve it along with\n      // any additional property we may add in the future\n      data.offsets.popper = _extends({}, data.offsets.popper, getPopperOffsets(data.instance.popper, data.offsets.reference, data.placement));\n\n      data = runModifiers(data.instance.modifiers, data, 'flip');\n    }\n  });\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction keepTogether(data) {\n  var _data$offsets = data.offsets,\n      popper = _data$offsets.popper,\n      reference = _data$offsets.reference;\n\n  var placement = data.placement.split('-')[0];\n  var floor = Math.floor;\n  var isVertical = ['top', 'bottom'].indexOf(placement) !== -1;\n  var side = isVertical ? 'right' : 'bottom';\n  var opSide = isVertical ? 'left' : 'top';\n  var measurement = isVertical ? 'width' : 'height';\n\n  if (popper[side] < floor(reference[opSide])) {\n    data.offsets.popper[opSide] = floor(reference[opSide]) - popper[measurement];\n  }\n  if (popper[opSide] > floor(reference[side])) {\n    data.offsets.popper[opSide] = floor(reference[side]);\n  }\n\n  return data;\n}\n\n/**\n * Converts a string containing value + unit into a px value number\n * @function\n * @memberof {modifiers~offset}\n * @private\n * @argument {String} str - Value + unit string\n * @argument {String} measurement - `height` or `width`\n * @argument {Object} popperOffsets\n * @argument {Object} referenceOffsets\n * @returns {Number|String}\n * Value in pixels, or original string if no values were extracted\n */\nfunction toValue(str, measurement, popperOffsets, referenceOffsets) {\n  // separate value from unit\n  var split = str.match(/((?:\\-|\\+)?\\d*\\.?\\d*)(.*)/);\n  var value = +split[1];\n  var unit = split[2];\n\n  // If it's not a number it's an operator, I guess\n  if (!value) {\n    return str;\n  }\n\n  if (unit.indexOf('%') === 0) {\n    var element = void 0;\n    switch (unit) {\n      case '%p':\n        element = popperOffsets;\n        break;\n      case '%':\n      case '%r':\n      default:\n        element = referenceOffsets;\n    }\n\n    var rect = getClientRect(element);\n    return rect[measurement] / 100 * value;\n  } else if (unit === 'vh' || unit === 'vw') {\n    // if is a vh or vw, we calculate the size based on the viewport\n    var size = void 0;\n    if (unit === 'vh') {\n      size = Math.max(document.documentElement.clientHeight, window.innerHeight || 0);\n    } else {\n      size = Math.max(document.documentElement.clientWidth, window.innerWidth || 0);\n    }\n    return size / 100 * value;\n  } else {\n    // if is an explicit pixel unit, we get rid of the unit and keep the value\n    // if is an implicit unit, it's px, and we return just the value\n    return value;\n  }\n}\n\n/**\n * Parse an `offset` string to extrapolate `x` and `y` numeric offsets.\n * @function\n * @memberof {modifiers~offset}\n * @private\n * @argument {String} offset\n * @argument {Object} popperOffsets\n * @argument {Object} referenceOffsets\n * @argument {String} basePlacement\n * @returns {Array} a two cells array with x and y offsets in numbers\n */\nfunction parseOffset(offset, popperOffsets, referenceOffsets, basePlacement) {\n  var offsets = [0, 0];\n\n  // Use height if placement is left or right and index is 0 otherwise use width\n  // in this way the first offset will use an axis and the second one\n  // will use the other one\n  var useHeight = ['right', 'left'].indexOf(basePlacement) !== -1;\n\n  // Split the offset string to obtain a list of values and operands\n  // The regex addresses values with the plus or minus sign in front (+10, -20, etc)\n  var fragments = offset.split(/(\\+|\\-)/).map(function (frag) {\n    return frag.trim();\n  });\n\n  // Detect if the offset string contains a pair of values or a single one\n  // they could be separated by comma or space\n  var divider = fragments.indexOf(find(fragments, function (frag) {\n    return frag.search(/,|\\s/) !== -1;\n  }));\n\n  if (fragments[divider] && fragments[divider].indexOf(',') === -1) {\n    console.warn('Offsets separated by white space(s) are deprecated, use a comma (,) instead.');\n  }\n\n  // If divider is found, we divide the list of values and operands to divide\n  // them by ofset X and Y.\n  var splitRegex = /\\s*,\\s*|\\s+/;\n  var ops = divider !== -1 ? [fragments.slice(0, divider).concat([fragments[divider].split(splitRegex)[0]]), [fragments[divider].split(splitRegex)[1]].concat(fragments.slice(divider + 1))] : [fragments];\n\n  // Convert the values with units to absolute pixels to allow our computations\n  ops = ops.map(function (op, index) {\n    // Most of the units rely on the orientation of the popper\n    var measurement = (index === 1 ? !useHeight : useHeight) ? 'height' : 'width';\n    var mergeWithPrevious = false;\n    return op\n    // This aggregates any `+` or `-` sign that aren't considered operators\n    // e.g.: 10 + +5 => [10, +, +5]\n    .reduce(function (a, b) {\n      if (a[a.length - 1] === '' && ['+', '-'].indexOf(b) !== -1) {\n        a[a.length - 1] = b;\n        mergeWithPrevious = true;\n        return a;\n      } else if (mergeWithPrevious) {\n        a[a.length - 1] += b;\n        mergeWithPrevious = false;\n        return a;\n      } else {\n        return a.concat(b);\n      }\n    }, [])\n    // Here we convert the string values into number values (in px)\n    .map(function (str) {\n      return toValue(str, measurement, popperOffsets, referenceOffsets);\n    });\n  });\n\n  // Loop trough the offsets arrays and execute the operations\n  ops.forEach(function (op, index) {\n    op.forEach(function (frag, index2) {\n      if (isNumeric(frag)) {\n        offsets[index] += frag * (op[index2 - 1] === '-' ? -1 : 1);\n      }\n    });\n  });\n  return offsets;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @argument {Number|String} options.offset=0\n * The offset value as described in the modifier description\n * @returns {Object} The data object, properly modified\n */\nfunction offset(data, _ref) {\n  var offset = _ref.offset;\n  var placement = data.placement,\n      _data$offsets = data.offsets,\n      popper = _data$offsets.popper,\n      reference = _data$offsets.reference;\n\n  var basePlacement = placement.split('-')[0];\n\n  var offsets = void 0;\n  if (isNumeric(+offset)) {\n    offsets = [+offset, 0];\n  } else {\n    offsets = parseOffset(offset, popper, reference, basePlacement);\n  }\n\n  if (basePlacement === 'left') {\n    popper.top += offsets[0];\n    popper.left -= offsets[1];\n  } else if (basePlacement === 'right') {\n    popper.top += offsets[0];\n    popper.left += offsets[1];\n  } else if (basePlacement === 'top') {\n    popper.left += offsets[0];\n    popper.top -= offsets[1];\n  } else if (basePlacement === 'bottom') {\n    popper.left += offsets[0];\n    popper.top += offsets[1];\n  }\n\n  data.popper = popper;\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction preventOverflow(data, options) {\n  var boundariesElement = options.boundariesElement || getOffsetParent(data.instance.popper);\n\n  // If offsetParent is the reference element, we really want to\n  // go one step up and use the next offsetParent as reference to\n  // avoid to make this modifier completely useless and look like broken\n  if (data.instance.reference === boundariesElement) {\n    boundariesElement = getOffsetParent(boundariesElement);\n  }\n\n  var boundaries = getBoundaries(data.instance.popper, data.instance.reference, options.padding, boundariesElement);\n  options.boundaries = boundaries;\n\n  var order = options.priority;\n  var popper = data.offsets.popper;\n\n  var check = {\n    primary: function primary(placement) {\n      var value = popper[placement];\n      if (popper[placement] < boundaries[placement] && !options.escapeWithReference) {\n        value = Math.max(popper[placement], boundaries[placement]);\n      }\n      return defineProperty({}, placement, value);\n    },\n    secondary: function secondary(placement) {\n      var mainSide = placement === 'right' ? 'left' : 'top';\n      var value = popper[mainSide];\n      if (popper[placement] > boundaries[placement] && !options.escapeWithReference) {\n        value = Math.min(popper[mainSide], boundaries[placement] - (placement === 'right' ? popper.width : popper.height));\n      }\n      return defineProperty({}, mainSide, value);\n    }\n  };\n\n  order.forEach(function (placement) {\n    var side = ['left', 'top'].indexOf(placement) !== -1 ? 'primary' : 'secondary';\n    popper = _extends({}, popper, check[side](placement));\n  });\n\n  data.offsets.popper = popper;\n\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction shift(data) {\n  var placement = data.placement;\n  var basePlacement = placement.split('-')[0];\n  var shiftvariation = placement.split('-')[1];\n\n  // if shift shiftvariation is specified, run the modifier\n  if (shiftvariation) {\n    var _data$offsets = data.offsets,\n        reference = _data$offsets.reference,\n        popper = _data$offsets.popper;\n\n    var isVertical = ['bottom', 'top'].indexOf(basePlacement) !== -1;\n    var side = isVertical ? 'left' : 'top';\n    var measurement = isVertical ? 'width' : 'height';\n\n    var shiftOffsets = {\n      start: defineProperty({}, side, reference[side]),\n      end: defineProperty({}, side, reference[side] + reference[measurement] - popper[measurement])\n    };\n\n    data.offsets.popper = _extends({}, popper, shiftOffsets[shiftvariation]);\n  }\n\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction hide(data) {\n  if (!isModifierRequired(data.instance.modifiers, 'hide', 'preventOverflow')) {\n    return data;\n  }\n\n  var refRect = data.offsets.reference;\n  var bound = find(data.instance.modifiers, function (modifier) {\n    return modifier.name === 'preventOverflow';\n  }).boundaries;\n\n  if (refRect.bottom < bound.top || refRect.left > bound.right || refRect.top > bound.bottom || refRect.right < bound.left) {\n    // Avoid unnecessary DOM access if visibility hasn't changed\n    if (data.hide === true) {\n      return data;\n    }\n\n    data.hide = true;\n    data.attributes['x-out-of-boundaries'] = '';\n  } else {\n    // Avoid unnecessary DOM access if visibility hasn't changed\n    if (data.hide === false) {\n      return data;\n    }\n\n    data.hide = false;\n    data.attributes['x-out-of-boundaries'] = false;\n  }\n\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction inner(data) {\n  var placement = data.placement;\n  var basePlacement = placement.split('-')[0];\n  var _data$offsets = data.offsets,\n      popper = _data$offsets.popper,\n      reference = _data$offsets.reference;\n\n  var isHoriz = ['left', 'right'].indexOf(basePlacement) !== -1;\n\n  var subtractLength = ['top', 'left'].indexOf(basePlacement) === -1;\n\n  popper[isHoriz ? 'left' : 'top'] = reference[basePlacement] - (subtractLength ? popper[isHoriz ? 'width' : 'height'] : 0);\n\n  data.placement = getOppositePlacement(placement);\n  data.offsets.popper = getClientRect(popper);\n\n  return data;\n}\n\n/**\n * Modifier function, each modifier can have a function of this type assigned\n * to its `fn` property.<br />\n * These functions will be called on each update, this means that you must\n * make sure they are performant enough to avoid performance bottlenecks.\n *\n * @function ModifierFn\n * @argument {dataObject} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {dataObject} The data object, properly modified\n */\n\n/**\n * Modifiers are plugins used to alter the behavior of your poppers.<br />\n * Popper.js uses a set of 9 modifiers to provide all the basic functionalities\n * needed by the library.\n *\n * Usually you don't want to override the `order`, `fn` and `onLoad` props.\n * All the other properties are configurations that could be tweaked.\n * @namespace modifiers\n */\nvar modifiers = {\n  /**\n   * Modifier used to shift the popper on the start or end of its reference\n   * element.<br />\n   * It will read the variation of the `placement` property.<br />\n   * It can be one either `-end` or `-start`.\n   * @memberof modifiers\n   * @inner\n   */\n  shift: {\n    /** @prop {number} order=100 - Index used to define the order of execution */\n    order: 100,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: shift\n  },\n\n  /**\n   * The `offset` modifier can shift your popper on both its axis.\n   *\n   * It accepts the following units:\n   * - `px` or unitless, interpreted as pixels\n   * - `%` or `%r`, percentage relative to the length of the reference element\n   * - `%p`, percentage relative to the length of the popper element\n   * - `vw`, CSS viewport width unit\n   * - `vh`, CSS viewport height unit\n   *\n   * For length is intended the main axis relative to the placement of the popper.<br />\n   * This means that if the placement is `top` or `bottom`, the length will be the\n   * `width`. In case of `left` or `right`, it will be the height.\n   *\n   * You can provide a single value (as `Number` or `String`), or a pair of values\n   * as `String` divided by a comma or one (or more) white spaces.<br />\n   * The latter is a deprecated method because it leads to confusion and will be\n   * removed in v2.<br />\n   * Additionally, it accepts additions and subtractions between different units.\n   * Note that multiplications and divisions aren't supported.\n   *\n   * Valid examples are:\n   * ```\n   * 10\n   * '10%'\n   * '10, 10'\n   * '10%, 10'\n   * '10 + 10%'\n   * '10 - 5vh + 3%'\n   * '-10px + 5vh, 5px - 6%'\n   * ```\n   * > **NB**: If you desire to apply offsets to your poppers in a way that may make them overlap\n   * > with their reference element, unfortunately, you will have to disable the `flip` modifier.\n   * > More on this [reading this issue](https://github.com/FezVrasta/popper.js/issues/373)\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  offset: {\n    /** @prop {number} order=200 - Index used to define the order of execution */\n    order: 200,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: offset,\n    /** @prop {Number|String} offset=0\n     * The offset value as described in the modifier description\n     */\n    offset: 0\n  },\n\n  /**\n   * Modifier used to prevent the popper from being positioned outside the boundary.\n   *\n   * An scenario exists where the reference itself is not within the boundaries.<br />\n   * We can say it has \"escaped the boundaries\" — or just \"escaped\".<br />\n   * In this case we need to decide whether the popper should either:\n   *\n   * - detach from the reference and remain \"trapped\" in the boundaries, or\n   * - if it should ignore the boundary and \"escape with its reference\"\n   *\n   * When `escapeWithReference` is set to`true` and reference is completely\n   * outside its boundaries, the popper will overflow (or completely leave)\n   * the boundaries in order to remain attached to the edge of the reference.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  preventOverflow: {\n    /** @prop {number} order=300 - Index used to define the order of execution */\n    order: 300,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: preventOverflow,\n    /**\n     * @prop {Array} [priority=['left','right','top','bottom']]\n     * Popper will try to prevent overflow following these priorities by default,\n     * then, it could overflow on the left and on top of the `boundariesElement`\n     */\n    priority: ['left', 'right', 'top', 'bottom'],\n    /**\n     * @prop {number} padding=5\n     * Amount of pixel used to define a minimum distance between the boundaries\n     * and the popper this makes sure the popper has always a little padding\n     * between the edges of its container\n     */\n    padding: 5,\n    /**\n     * @prop {String|HTMLElement} boundariesElement='scrollParent'\n     * Boundaries used by the modifier, can be `scrollParent`, `window`,\n     * `viewport` or any DOM element.\n     */\n    boundariesElement: 'scrollParent'\n  },\n\n  /**\n   * Modifier used to make sure the reference and its popper stay near eachothers\n   * without leaving any gap between the two. Expecially useful when the arrow is\n   * enabled and you want to assure it to point to its reference element.\n   * It cares only about the first axis, you can still have poppers with margin\n   * between the popper and its reference element.\n   * @memberof modifiers\n   * @inner\n   */\n  keepTogether: {\n    /** @prop {number} order=400 - Index used to define the order of execution */\n    order: 400,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: keepTogether\n  },\n\n  /**\n   * This modifier is used to move the `arrowElement` of the popper to make\n   * sure it is positioned between the reference element and its popper element.\n   * It will read the outer size of the `arrowElement` node to detect how many\n   * pixels of conjuction are needed.\n   *\n   * It has no effect if no `arrowElement` is provided.\n   * @memberof modifiers\n   * @inner\n   */\n  arrow: {\n    /** @prop {number} order=500 - Index used to define the order of execution */\n    order: 500,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: arrow,\n    /** @prop {String|HTMLElement} element='[x-arrow]' - Selector or node used as arrow */\n    element: '[x-arrow]'\n  },\n\n  /**\n   * Modifier used to flip the popper's placement when it starts to overlap its\n   * reference element.\n   *\n   * Requires the `preventOverflow` modifier before it in order to work.\n   *\n   * **NOTE:** this modifier will interrupt the current update cycle and will\n   * restart it if it detects the need to flip the placement.\n   * @memberof modifiers\n   * @inner\n   */\n  flip: {\n    /** @prop {number} order=600 - Index used to define the order of execution */\n    order: 600,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: flip,\n    /**\n     * @prop {String|Array} behavior='flip'\n     * The behavior used to change the popper's placement. It can be one of\n     * `flip`, `clockwise`, `counterclockwise` or an array with a list of valid\n     * placements (with optional variations).\n     */\n    behavior: 'flip',\n    /**\n     * @prop {number} padding=5\n     * The popper will flip if it hits the edges of the `boundariesElement`\n     */\n    padding: 5,\n    /**\n     * @prop {String|HTMLElement} boundariesElement='viewport'\n     * The element which will define the boundaries of the popper position,\n     * the popper will never be placed outside of the defined boundaries\n     * (except if keepTogether is enabled)\n     */\n    boundariesElement: 'viewport'\n  },\n\n  /**\n   * Modifier used to make the popper flow toward the inner of the reference element.\n   * By default, when this modifier is disabled, the popper will be placed outside\n   * the reference element.\n   * @memberof modifiers\n   * @inner\n   */\n  inner: {\n    /** @prop {number} order=700 - Index used to define the order of execution */\n    order: 700,\n    /** @prop {Boolean} enabled=false - Whether the modifier is enabled or not */\n    enabled: false,\n    /** @prop {ModifierFn} */\n    fn: inner\n  },\n\n  /**\n   * Modifier used to hide the popper when its reference element is outside of the\n   * popper boundaries. It will set a `x-out-of-boundaries` attribute which can\n   * be used to hide with a CSS selector the popper when its reference is\n   * out of boundaries.\n   *\n   * Requires the `preventOverflow` modifier before it in order to work.\n   * @memberof modifiers\n   * @inner\n   */\n  hide: {\n    /** @prop {number} order=800 - Index used to define the order of execution */\n    order: 800,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: hide\n  },\n\n  /**\n   * Computes the style that will be applied to the popper element to gets\n   * properly positioned.\n   *\n   * Note that this modifier will not touch the DOM, it just prepares the styles\n   * so that `applyStyle` modifier can apply it. This separation is useful\n   * in case you need to replace `applyStyle` with a custom implementation.\n   *\n   * This modifier has `850` as `order` value to maintain backward compatibility\n   * with previous versions of Popper.js. Expect the modifiers ordering method\n   * to change in future major versions of the library.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  computeStyle: {\n    /** @prop {number} order=850 - Index used to define the order of execution */\n    order: 850,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: computeStyle,\n    /**\n     * @prop {Boolean} gpuAcceleration=true\n     * If true, it uses the CSS 3d transformation to position the popper.\n     * Otherwise, it will use the `top` and `left` properties.\n     */\n    gpuAcceleration: true,\n    /**\n     * @prop {string} [x='bottom']\n     * Where to anchor the X axis (`bottom` or `top`). AKA X offset origin.\n     * Change this if your popper should grow in a direction different from `bottom`\n     */\n    x: 'bottom',\n    /**\n     * @prop {string} [x='left']\n     * Where to anchor the Y axis (`left` or `right`). AKA Y offset origin.\n     * Change this if your popper should grow in a direction different from `right`\n     */\n    y: 'right'\n  },\n\n  /**\n   * Applies the computed styles to the popper element.\n   *\n   * All the DOM manipulations are limited to this modifier. This is useful in case\n   * you want to integrate Popper.js inside a framework or view library and you\n   * want to delegate all the DOM manipulations to it.\n   *\n   * Note that if you disable this modifier, you must make sure the popper element\n   * has its position set to `absolute` before Popper.js can do its work!\n   *\n   * Just disable this modifier and define you own to achieve the desired effect.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  applyStyle: {\n    /** @prop {number} order=900 - Index used to define the order of execution */\n    order: 900,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: applyStyle,\n    /** @prop {Function} */\n    onLoad: applyStyleOnLoad,\n    /**\n     * @deprecated since version 1.10.0, the property moved to `computeStyle` modifier\n     * @prop {Boolean} gpuAcceleration=true\n     * If true, it uses the CSS 3d transformation to position the popper.\n     * Otherwise, it will use the `top` and `left` properties.\n     */\n    gpuAcceleration: undefined\n  }\n};\n\n/**\n * The `dataObject` is an object containing all the informations used by Popper.js\n * this object get passed to modifiers and to the `onCreate` and `onUpdate` callbacks.\n * @name dataObject\n * @property {Object} data.instance The Popper.js instance\n * @property {String} data.placement Placement applied to popper\n * @property {String} data.originalPlacement Placement originally defined on init\n * @property {Boolean} data.flipped True if popper has been flipped by flip modifier\n * @property {Boolean} data.hide True if the reference element is out of boundaries, useful to know when to hide the popper.\n * @property {HTMLElement} data.arrowElement Node used as arrow by arrow modifier\n * @property {Object} data.styles Any CSS property defined here will be applied to the popper, it expects the JavaScript nomenclature (eg. `marginBottom`)\n * @property {Object} data.arrowStyles Any CSS property defined here will be applied to the popper arrow, it expects the JavaScript nomenclature (eg. `marginBottom`)\n * @property {Object} data.boundaries Offsets of the popper boundaries\n * @property {Object} data.offsets The measurements of popper, reference and arrow elements.\n * @property {Object} data.offsets.popper `top`, `left`, `width`, `height` values\n * @property {Object} data.offsets.reference `top`, `left`, `width`, `height` values\n * @property {Object} data.offsets.arrow] `top` and `left` offsets, only one of them will be different from 0\n */\n\n/**\n * Default options provided to Popper.js constructor.<br />\n * These can be overriden using the `options` argument of Popper.js.<br />\n * To override an option, simply pass as 3rd argument an object with the same\n * structure of this object, example:\n * ```\n * new Popper(ref, pop, {\n *   modifiers: {\n *     preventOverflow: { enabled: false }\n *   }\n * })\n * ```\n * @type {Object}\n * @static\n * @memberof Popper\n */\nvar Defaults = {\n  /**\n   * Popper's placement\n   * @prop {Popper.placements} placement='bottom'\n   */\n  placement: 'bottom',\n\n  /**\n   * Whether events (resize, scroll) are initially enabled\n   * @prop {Boolean} eventsEnabled=true\n   */\n  eventsEnabled: true,\n\n  /**\n   * Set to true if you want to automatically remove the popper when\n   * you call the `destroy` method.\n   * @prop {Boolean} removeOnDestroy=false\n   */\n  removeOnDestroy: false,\n\n  /**\n   * Callback called when the popper is created.<br />\n   * By default, is set to no-op.<br />\n   * Access Popper.js instance with `data.instance`.\n   * @prop {onCreate}\n   */\n  onCreate: function onCreate() {},\n\n  /**\n   * Callback called when the popper is updated, this callback is not called\n   * on the initialization/creation of the popper, but only on subsequent\n   * updates.<br />\n   * By default, is set to no-op.<br />\n   * Access Popper.js instance with `data.instance`.\n   * @prop {onUpdate}\n   */\n  onUpdate: function onUpdate() {},\n\n  /**\n   * List of modifiers used to modify the offsets before they are applied to the popper.\n   * They provide most of the functionalities of Popper.js\n   * @prop {modifiers}\n   */\n  modifiers: modifiers\n};\n\n/**\n * @callback onCreate\n * @param {dataObject} data\n */\n\n/**\n * @callback onUpdate\n * @param {dataObject} data\n */\n\n// Utils\n// Methods\nvar Popper = function () {\n  /**\n   * Create a new Popper.js instance\n   * @class Popper\n   * @param {HTMLElement|referenceObject} reference - The reference element used to position the popper\n   * @param {HTMLElement} popper - The HTML element used as popper.\n   * @param {Object} options - Your custom options to override the ones defined in [Defaults](#defaults)\n   * @return {Object} instance - The generated Popper.js instance\n   */\n  function Popper(reference, popper) {\n    var _this = this;\n\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    classCallCheck(this, Popper);\n\n    this.scheduleUpdate = function () {\n      return requestAnimationFrame(_this.update);\n    };\n\n    // make update() debounced, so that it only runs at most once-per-tick\n    this.update = debounce(this.update.bind(this));\n\n    // with {} we create a new object with the options inside it\n    this.options = _extends({}, Popper.Defaults, options);\n\n    // init state\n    this.state = {\n      isDestroyed: false,\n      isCreated: false,\n      scrollParents: []\n    };\n\n    // get reference and popper elements (allow jQuery wrappers)\n    this.reference = reference && reference.jquery ? reference[0] : reference;\n    this.popper = popper && popper.jquery ? popper[0] : popper;\n\n    // Deep merge modifiers options\n    this.options.modifiers = {};\n    Object.keys(_extends({}, Popper.Defaults.modifiers, options.modifiers)).forEach(function (name) {\n      _this.options.modifiers[name] = _extends({}, Popper.Defaults.modifiers[name] || {}, options.modifiers ? options.modifiers[name] : {});\n    });\n\n    // Refactoring modifiers' list (Object => Array)\n    this.modifiers = Object.keys(this.options.modifiers).map(function (name) {\n      return _extends({\n        name: name\n      }, _this.options.modifiers[name]);\n    })\n    // sort the modifiers by order\n    .sort(function (a, b) {\n      return a.order - b.order;\n    });\n\n    // modifiers have the ability to execute arbitrary code when Popper.js get inited\n    // such code is executed in the same order of its modifier\n    // they could add new properties to their options configuration\n    // BE AWARE: don't add options to `options.modifiers.name` but to `modifierOptions`!\n    this.modifiers.forEach(function (modifierOptions) {\n      if (modifierOptions.enabled && isFunction(modifierOptions.onLoad)) {\n        modifierOptions.onLoad(_this.reference, _this.popper, _this.options, modifierOptions, _this.state);\n      }\n    });\n\n    // fire the first update to position the popper in the right place\n    this.update();\n\n    var eventsEnabled = this.options.eventsEnabled;\n    if (eventsEnabled) {\n      // setup event listeners, they will take care of update the position in specific situations\n      this.enableEventListeners();\n    }\n\n    this.state.eventsEnabled = eventsEnabled;\n  }\n\n  // We can't use class properties because they don't get listed in the\n  // class prototype and break stuff like Sinon stubs\n\n\n  createClass(Popper, [{\n    key: 'update',\n    value: function update$$1() {\n      return update.call(this);\n    }\n  }, {\n    key: 'destroy',\n    value: function destroy$$1() {\n      return destroy.call(this);\n    }\n  }, {\n    key: 'enableEventListeners',\n    value: function enableEventListeners$$1() {\n      return enableEventListeners.call(this);\n    }\n  }, {\n    key: 'disableEventListeners',\n    value: function disableEventListeners$$1() {\n      return disableEventListeners.call(this);\n    }\n\n    /**\n     * Schedule an update, it will run on the next UI update available\n     * @method scheduleUpdate\n     * @memberof Popper\n     */\n\n\n    /**\n     * Collection of utilities useful when writing custom modifiers.\n     * Starting from version 1.7, this method is available only if you\n     * include `popper-utils.js` before `popper.js`.\n     *\n     * **DEPRECATION**: This way to access PopperUtils is deprecated\n     * and will be removed in v2! Use the PopperUtils module directly instead.\n     * Due to the high instability of the methods contained in Utils, we can't\n     * guarantee them to follow semver. Use them at your own risk!\n     * @static\n     * @private\n     * @type {Object}\n     * @deprecated since version 1.8\n     * @member Utils\n     * @memberof Popper\n     */\n\n  }]);\n  return Popper;\n}();\n\n/**\n * The `referenceObject` is an object that provides an interface compatible with Popper.js\n * and lets you use it as replacement of a real DOM node.<br />\n * You can use this method to position a popper relatively to a set of coordinates\n * in case you don't have a DOM node to use as reference.\n *\n * ```\n * new Popper(referenceObject, popperNode);\n * ```\n *\n * NB: This feature isn't supported in Internet Explorer 10\n * @name referenceObject\n * @property {Function} data.getBoundingClientRect\n * A function that returns a set of coordinates compatible with the native `getBoundingClientRect` method.\n * @property {number} data.clientWidth\n * An ES6 getter that will return the width of the virtual reference element.\n * @property {number} data.clientHeight\n * An ES6 getter that will return the height of the virtual reference element.\n */\n\n\nPopper.Utils = (typeof window !== 'undefined' ? window : global).PopperUtils;\nPopper.placements = placements;\nPopper.Defaults = Defaults;\n\nexport default Popper;\n//# sourceMappingURL=popper.js.map\n", "import $ from 'jquery'\nimport Popper from 'popper.js'\nimport Util from './util'\n\n/**\n * --------------------------------------------------------------------------\n * <PERSON>trap (v4.0.0): dropdown.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst Dropdown = (($) => {\n  /**\n   * ------------------------------------------------------------------------\n   * Constants\n   * ------------------------------------------------------------------------\n   */\n\n  const NAME                     = 'dropdown'\n  const VERSION                  = '4.0.0'\n  const DATA_KEY                 = 'bs.dropdown'\n  const EVENT_KEY                = `.${DATA_KEY}`\n  const DATA_API_KEY             = '.data-api'\n  const JQUERY_NO_CONFLICT       = $.fn[NAME]\n  const ESCAPE_KEYCODE           = 27 // KeyboardEvent.which value for Escape (Esc) key\n  const SPACE_KEYCODE            = 32 // KeyboardEvent.which value for space key\n  const TAB_KEYCODE              = 9 // KeyboardEvent.which value for tab key\n  const ARROW_UP_KEYCODE         = 38 // KeyboardEvent.which value for up arrow key\n  const ARROW_DOWN_KEYCODE       = 40 // KeyboardEvent.which value for down arrow key\n  const RIGHT_MOUSE_BUTTON_WHICH = 3 // MouseEvent.which value for the right button (assuming a right-handed mouse)\n  const REGEXP_KEYDOWN           = new RegExp(`${ARROW_UP_KEYCODE}|${ARROW_DOWN_KEYCODE}|${ESCAPE_KEYCODE}`)\n\n  const Event = {\n    HIDE             : `hide${EVENT_KEY}`,\n    HIDDEN           : `hidden${EVENT_KEY}`,\n    SHOW             : `show${EVENT_KEY}`,\n    SHOWN            : `shown${EVENT_KEY}`,\n    CLICK            : `click${EVENT_KEY}`,\n    CLICK_DATA_API   : `click${EVENT_KEY}${DATA_API_KEY}`,\n    KEYDOWN_DATA_API : `keydown${EVENT_KEY}${DATA_API_KEY}`,\n    KEYUP_DATA_API   : `keyup${EVENT_KEY}${DATA_API_KEY}`\n  }\n\n  const ClassName = {\n    DISABLED  : 'disabled',\n    SHOW      : 'show',\n    DROPUP    : 'dropup',\n    DROPRIGHT : 'dropright',\n    DROPLEFT  : 'dropleft',\n    MENURIGHT : 'dropdown-menu-right',\n    MENULEFT  : 'dropdown-menu-left',\n    POSITION_STATIC : 'position-static'\n  }\n\n  const Selector = {\n    DATA_TOGGLE   : '[data-toggle=\"dropdown\"]',\n    FORM_CHILD    : '.dropdown form',\n    MENU          : '.dropdown-menu',\n    NAVBAR_NAV    : '.navbar-nav',\n    VISIBLE_ITEMS : '.dropdown-menu .dropdown-item:not(.disabled)'\n  }\n\n  const AttachmentMap = {\n    TOP       : 'top-start',\n    TOPEND    : 'top-end',\n    BOTTOM    : 'bottom-start',\n    BOTTOMEND : 'bottom-end',\n    RIGHT     : 'right-start',\n    RIGHTEND  : 'right-end',\n    LEFT      : 'left-start',\n    LEFTEND   : 'left-end'\n  }\n\n  const Default = {\n    offset      : 0,\n    flip        : true,\n    boundary    : 'scrollParent'\n  }\n\n  const DefaultType = {\n    offset      : '(number|string|function)',\n    flip        : 'boolean',\n    boundary    : '(string|element)'\n  }\n\n  /**\n   * ------------------------------------------------------------------------\n   * Class Definition\n   * ------------------------------------------------------------------------\n   */\n\n  class Dropdown {\n    constructor(element, config) {\n      this._element  = element\n      this._popper   = null\n      this._config   = this._getConfig(config)\n      this._menu     = this._getMenuElement()\n      this._inNavbar = this._detectNavbar()\n\n      this._addEventListeners()\n    }\n\n    // Getters\n\n    static get VERSION() {\n      return VERSION\n    }\n\n    static get Default() {\n      return Default\n    }\n\n    static get DefaultType() {\n      return DefaultType\n    }\n\n    // Public\n\n    toggle() {\n      if (this._element.disabled || $(this._element).hasClass(ClassName.DISABLED)) {\n        return\n      }\n\n      const parent   = Dropdown._getParentFromElement(this._element)\n      const isActive = $(this._menu).hasClass(ClassName.SHOW)\n\n      Dropdown._clearMenus()\n\n      if (isActive) {\n        return\n      }\n\n      const relatedTarget = {\n        relatedTarget: this._element\n      }\n      const showEvent = $.Event(Event.SHOW, relatedTarget)\n\n      $(parent).trigger(showEvent)\n\n      if (showEvent.isDefaultPrevented()) {\n        return\n      }\n\n      // Disable totally Popper.js for Dropdown in Navbar\n      if (!this._inNavbar) {\n        /**\n         * Check for Popper dependency\n         * Popper - https://popper.js.org\n         */\n        if (typeof Popper === 'undefined') {\n          throw new TypeError('Bootstrap dropdown require Popper.js (https://popper.js.org)')\n        }\n        let element = this._element\n        // For dropup with alignment we use the parent as popper container\n        if ($(parent).hasClass(ClassName.DROPUP)) {\n          if ($(this._menu).hasClass(ClassName.MENULEFT) || $(this._menu).hasClass(ClassName.MENURIGHT)) {\n            element = parent\n          }\n        }\n        // If boundary is not `scrollParent`, then set position to `static`\n        // to allow the menu to \"escape\" the scroll parent's boundaries\n        // https://github.com/twbs/bootstrap/issues/24251\n        if (this._config.boundary !== 'scrollParent') {\n          $(parent).addClass(ClassName.POSITION_STATIC)\n        }\n        this._popper = new Popper(element, this._menu, this._getPopperConfig())\n      }\n\n      // If this is a touch-enabled device we add extra\n      // empty mouseover listeners to the body's immediate children;\n      // only needed because of broken event delegation on iOS\n      // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n      if ('ontouchstart' in document.documentElement &&\n         $(parent).closest(Selector.NAVBAR_NAV).length === 0) {\n        $('body').children().on('mouseover', null, $.noop)\n      }\n\n      this._element.focus()\n      this._element.setAttribute('aria-expanded', true)\n\n      $(this._menu).toggleClass(ClassName.SHOW)\n      $(parent)\n        .toggleClass(ClassName.SHOW)\n        .trigger($.Event(Event.SHOWN, relatedTarget))\n    }\n\n    dispose() {\n      $.removeData(this._element, DATA_KEY)\n      $(this._element).off(EVENT_KEY)\n      this._element = null\n      this._menu = null\n      if (this._popper !== null) {\n        this._popper.destroy()\n        this._popper = null\n      }\n    }\n\n    update() {\n      this._inNavbar = this._detectNavbar()\n      if (this._popper !== null) {\n        this._popper.scheduleUpdate()\n      }\n    }\n\n    // Private\n\n    _addEventListeners() {\n      $(this._element).on(Event.CLICK, (event) => {\n        event.preventDefault()\n        event.stopPropagation()\n        this.toggle()\n      })\n    }\n\n    _getConfig(config) {\n      config = {\n        ...this.constructor.Default,\n        ...$(this._element).data(),\n        ...config\n      }\n\n      Util.typeCheckConfig(\n        NAME,\n        config,\n        this.constructor.DefaultType\n      )\n\n      return config\n    }\n\n    _getMenuElement() {\n      if (!this._menu) {\n        const parent = Dropdown._getParentFromElement(this._element)\n        this._menu = $(parent).find(Selector.MENU)[0]\n      }\n      return this._menu\n    }\n\n    _getPlacement() {\n      const $parentDropdown = $(this._element).parent()\n      let placement = AttachmentMap.BOTTOM\n\n      // Handle dropup\n      if ($parentDropdown.hasClass(ClassName.DROPUP)) {\n        placement = AttachmentMap.TOP\n        if ($(this._menu).hasClass(ClassName.MENURIGHT)) {\n          placement = AttachmentMap.TOPEND\n        }\n      } else if ($parentDropdown.hasClass(ClassName.DROPRIGHT)) {\n        placement = AttachmentMap.RIGHT\n      } else if ($parentDropdown.hasClass(ClassName.DROPLEFT)) {\n        placement = AttachmentMap.LEFT\n      } else if ($(this._menu).hasClass(ClassName.MENURIGHT)) {\n        placement = AttachmentMap.BOTTOMEND\n      }\n      return placement\n    }\n\n    _detectNavbar() {\n      return $(this._element).closest('.navbar').length > 0\n    }\n\n    _getPopperConfig() {\n      const offsetConf = {}\n      if (typeof this._config.offset === 'function') {\n        offsetConf.fn = (data) => {\n          data.offsets = {\n            ...data.offsets,\n            ...this._config.offset(data.offsets) || {}\n          }\n          return data\n        }\n      } else {\n        offsetConf.offset = this._config.offset\n      }\n      const popperConfig = {\n        placement: this._getPlacement(),\n        modifiers: {\n          offset: offsetConf,\n          flip: {\n            enabled: this._config.flip\n          },\n          preventOverflow: {\n            boundariesElement: this._config.boundary\n          }\n        }\n      }\n\n      return popperConfig\n    }\n\n    // Static\n\n    static _jQueryInterface(config) {\n      return this.each(function () {\n        let data = $(this).data(DATA_KEY)\n        const _config = typeof config === 'object' ? config : null\n\n        if (!data) {\n          data = new Dropdown(this, _config)\n          $(this).data(DATA_KEY, data)\n        }\n\n        if (typeof config === 'string') {\n          if (typeof data[config] === 'undefined') {\n            throw new TypeError(`No method named \"${config}\"`)\n          }\n          data[config]()\n        }\n      })\n    }\n\n    static _clearMenus(event) {\n      if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH ||\n        event.type === 'keyup' && event.which !== TAB_KEYCODE)) {\n        return\n      }\n\n      const toggles = $.makeArray($(Selector.DATA_TOGGLE))\n      for (let i = 0; i < toggles.length; i++) {\n        const parent = Dropdown._getParentFromElement(toggles[i])\n        const context = $(toggles[i]).data(DATA_KEY)\n        const relatedTarget = {\n          relatedTarget: toggles[i]\n        }\n\n        if (!context) {\n          continue\n        }\n\n        const dropdownMenu = context._menu\n        if (!$(parent).hasClass(ClassName.SHOW)) {\n          continue\n        }\n\n        if (event && (event.type === 'click' &&\n            /input|textarea/i.test(event.target.tagName) || event.type === 'keyup' && event.which === TAB_KEYCODE) &&\n            $.contains(parent, event.target)) {\n          continue\n        }\n\n        const hideEvent = $.Event(Event.HIDE, relatedTarget)\n        $(parent).trigger(hideEvent)\n        if (hideEvent.isDefaultPrevented()) {\n          continue\n        }\n\n        // If this is a touch-enabled device we remove the extra\n        // empty mouseover listeners we added for iOS support\n        if ('ontouchstart' in document.documentElement) {\n          $('body').children().off('mouseover', null, $.noop)\n        }\n\n        toggles[i].setAttribute('aria-expanded', 'false')\n\n        $(dropdownMenu).removeClass(ClassName.SHOW)\n        $(parent)\n          .removeClass(ClassName.SHOW)\n          .trigger($.Event(Event.HIDDEN, relatedTarget))\n      }\n    }\n\n    static _getParentFromElement(element) {\n      let parent\n      const selector = Util.getSelectorFromElement(element)\n\n      if (selector) {\n        parent = $(selector)[0]\n      }\n\n      return parent || element.parentNode\n    }\n\n    // eslint-disable-next-line complexity\n    static _dataApiKeydownHandler(event) {\n      // If not input/textarea:\n      //  - And not a key in REGEXP_KEYDOWN => not a dropdown command\n      // If input/textarea:\n      //  - If space key => not a dropdown command\n      //  - If key is other than escape\n      //    - If key is not up or down => not a dropdown command\n      //    - If trigger inside the menu => not a dropdown command\n      if (/input|textarea/i.test(event.target.tagName)\n        ? event.which === SPACE_KEYCODE || event.which !== ESCAPE_KEYCODE &&\n        (event.which !== ARROW_DOWN_KEYCODE && event.which !== ARROW_UP_KEYCODE ||\n          $(event.target).closest(Selector.MENU).length) : !REGEXP_KEYDOWN.test(event.which)) {\n        return\n      }\n\n      event.preventDefault()\n      event.stopPropagation()\n\n      if (this.disabled || $(this).hasClass(ClassName.DISABLED)) {\n        return\n      }\n\n      const parent   = Dropdown._getParentFromElement(this)\n      const isActive = $(parent).hasClass(ClassName.SHOW)\n\n      if (!isActive && (event.which !== ESCAPE_KEYCODE || event.which !== SPACE_KEYCODE) ||\n           isActive && (event.which === ESCAPE_KEYCODE || event.which === SPACE_KEYCODE)) {\n        if (event.which === ESCAPE_KEYCODE) {\n          const toggle = $(parent).find(Selector.DATA_TOGGLE)[0]\n          $(toggle).trigger('focus')\n        }\n\n        $(this).trigger('click')\n        return\n      }\n\n      const items = $(parent).find(Selector.VISIBLE_ITEMS).get()\n\n      if (items.length === 0) {\n        return\n      }\n\n      let index = items.indexOf(event.target)\n\n      if (event.which === ARROW_UP_KEYCODE && index > 0) { // Up\n        index--\n      }\n\n      if (event.which === ARROW_DOWN_KEYCODE && index < items.length - 1) { // Down\n        index++\n      }\n\n      if (index < 0) {\n        index = 0\n      }\n\n      items[index].focus()\n    }\n  }\n\n  /**\n   * ------------------------------------------------------------------------\n   * Data Api implementation\n   * ------------------------------------------------------------------------\n   */\n\n  $(document)\n    .on(Event.KEYDOWN_DATA_API, Selector.DATA_TOGGLE, Dropdown._dataApiKeydownHandler)\n    .on(Event.KEYDOWN_DATA_API, Selector.MENU, Dropdown._dataApiKeydownHandler)\n    .on(`${Event.CLICK_DATA_API} ${Event.KEYUP_DATA_API}`, Dropdown._clearMenus)\n    .on(Event.CLICK_DATA_API, Selector.DATA_TOGGLE, function (event) {\n      event.preventDefault()\n      event.stopPropagation()\n      Dropdown._jQueryInterface.call($(this), 'toggle')\n    })\n    .on(Event.CLICK_DATA_API, Selector.FORM_CHILD, (e) => {\n      e.stopPropagation()\n    })\n\n  /**\n   * ------------------------------------------------------------------------\n   * jQuery\n   * ------------------------------------------------------------------------\n   */\n\n  $.fn[NAME] = Dropdown._jQueryInterface\n  $.fn[NAME].Constructor = Dropdown\n  $.fn[NAME].noConflict = function () {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Dropdown._jQueryInterface\n  }\n\n  return Dropdown\n})($, Popper)\n\nexport default Dropdown\n", "import $ from 'jquery'\nimport Util from './util'\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.0.0): modal.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst Modal = (($) => {\n  /**\n   * ------------------------------------------------------------------------\n   * Constants\n   * ------------------------------------------------------------------------\n   */\n\n  const NAME                         = 'modal'\n  const VERSION                      = '4.0.0'\n  const DATA_KEY                     = 'bs.modal'\n  const EVENT_KEY                    = `.${DATA_KEY}`\n  const DATA_API_KEY                 = '.data-api'\n  const JQUERY_NO_CONFLICT           = $.fn[NAME]\n  const TRANSITION_DURATION          = 300\n  const BACKDROP_TRANSITION_DURATION = 150\n  const ESCAPE_KEYCODE               = 27 // KeyboardEvent.which value for Escape (Esc) key\n\n  const Default = {\n    backdrop : true,\n    keyboard : true,\n    focus    : true,\n    show     : true\n  }\n\n  const DefaultType = {\n    backdrop : '(boolean|string)',\n    keyboard : 'boolean',\n    focus    : 'boolean',\n    show     : 'boolean'\n  }\n\n  const Event = {\n    HIDE              : `hide${EVENT_KEY}`,\n    HIDDEN            : `hidden${EVENT_KEY}`,\n    SHOW              : `show${EVENT_KEY}`,\n    SHOWN             : `shown${EVENT_KEY}`,\n    FOCUSIN           : `focusin${EVENT_KEY}`,\n    RESIZE            : `resize${EVENT_KEY}`,\n    CLICK_DISMISS     : `click.dismiss${EVENT_KEY}`,\n    KEYDOWN_DISMISS   : `keydown.dismiss${EVENT_KEY}`,\n    MOUSEUP_DISMISS   : `mouseup.dismiss${EVENT_KEY}`,\n    MOUSEDOWN_DISMISS : `mousedown.dismiss${EVENT_KEY}`,\n    CLICK_DATA_API    : `click${EVENT_KEY}${DATA_API_KEY}`\n  }\n\n  const ClassName = {\n    SCROLLBAR_MEASURER : 'modal-scrollbar-measure',\n    BACKDROP           : 'modal-backdrop',\n    OPEN               : 'modal-open',\n    FADE               : 'fade',\n    SHOW               : 'show'\n  }\n\n  const Selector = {\n    DIALOG             : '.modal-dialog',\n    DATA_TOGGLE        : '[data-toggle=\"modal\"]',\n    DATA_DISMISS       : '[data-dismiss=\"modal\"]',\n    FIXED_CONTENT      : '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top',\n    STICKY_CONTENT     : '.sticky-top',\n    NAVBAR_TOGGLER     : '.navbar-toggler'\n  }\n\n  /**\n   * ------------------------------------------------------------------------\n   * Class Definition\n   * ------------------------------------------------------------------------\n   */\n\n  class Modal {\n    constructor(element, config) {\n      this._config              = this._getConfig(config)\n      this._element             = element\n      this._dialog              = $(element).find(Selector.DIALOG)[0]\n      this._backdrop            = null\n      this._isShown             = false\n      this._isBodyOverflowing   = false\n      this._ignoreBackdropClick = false\n      this._originalBodyPadding = 0\n      this._scrollbarWidth      = 0\n    }\n\n    // Getters\n\n    static get VERSION() {\n      return VERSION\n    }\n\n    static get Default() {\n      return Default\n    }\n\n    // Public\n\n    toggle(relatedTarget) {\n      return this._isShown ? this.hide() : this.show(relatedTarget)\n    }\n\n    show(relatedTarget) {\n      if (this._isTransitioning || this._isShown) {\n        return\n      }\n\n      if (Util.supportsTransitionEnd() && $(this._element).hasClass(ClassName.FADE)) {\n        this._isTransitioning = true\n      }\n\n      const showEvent = $.Event(Event.SHOW, {\n        relatedTarget\n      })\n\n      $(this._element).trigger(showEvent)\n\n      if (this._isShown || showEvent.isDefaultPrevented()) {\n        return\n      }\n\n      this._isShown = true\n\n      this._checkScrollbar()\n      this._setScrollbar()\n\n      this._adjustDialog()\n\n      $(document.body).addClass(ClassName.OPEN)\n\n      this._setEscapeEvent()\n      this._setResizeEvent()\n\n      $(this._element).on(\n        Event.CLICK_DISMISS,\n        Selector.DATA_DISMISS,\n        (event) => this.hide(event)\n      )\n\n      $(this._dialog).on(Event.MOUSEDOWN_DISMISS, () => {\n        $(this._element).one(Event.MOUSEUP_DISMISS, (event) => {\n          if ($(event.target).is(this._element)) {\n            this._ignoreBackdropClick = true\n          }\n        })\n      })\n\n      this._showBackdrop(() => this._showElement(relatedTarget))\n    }\n\n    hide(event) {\n      if (event) {\n        event.preventDefault()\n      }\n\n      if (this._isTransitioning || !this._isShown) {\n        return\n      }\n\n      const hideEvent = $.Event(Event.HIDE)\n\n      $(this._element).trigger(hideEvent)\n\n      if (!this._isShown || hideEvent.isDefaultPrevented()) {\n        return\n      }\n\n      this._isShown = false\n\n      const transition = Util.supportsTransitionEnd() && $(this._element).hasClass(ClassName.FADE)\n\n      if (transition) {\n        this._isTransitioning = true\n      }\n\n      this._setEscapeEvent()\n      this._setResizeEvent()\n\n      $(document).off(Event.FOCUSIN)\n\n      $(this._element).removeClass(ClassName.SHOW)\n\n      $(this._element).off(Event.CLICK_DISMISS)\n      $(this._dialog).off(Event.MOUSEDOWN_DISMISS)\n\n      if (transition) {\n        $(this._element)\n          .one(Util.TRANSITION_END, (event) => this._hideModal(event))\n          .emulateTransitionEnd(TRANSITION_DURATION)\n      } else {\n        this._hideModal()\n      }\n    }\n\n    dispose() {\n      $.removeData(this._element, DATA_KEY)\n\n      $(window, document, this._element, this._backdrop).off(EVENT_KEY)\n\n      this._config              = null\n      this._element             = null\n      this._dialog              = null\n      this._backdrop            = null\n      this._isShown             = null\n      this._isBodyOverflowing   = null\n      this._ignoreBackdropClick = null\n      this._scrollbarWidth      = null\n    }\n\n    handleUpdate() {\n      this._adjustDialog()\n    }\n\n    // Private\n\n    _getConfig(config) {\n      config = {\n        ...Default,\n        ...config\n      }\n      Util.typeCheckConfig(NAME, config, DefaultType)\n      return config\n    }\n\n    _showElement(relatedTarget) {\n      const transition = Util.supportsTransitionEnd() &&\n        $(this._element).hasClass(ClassName.FADE)\n\n      if (!this._element.parentNode ||\n         this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {\n        // Don't move modal's DOM position\n        document.body.appendChild(this._element)\n      }\n\n      this._element.style.display = 'block'\n      this._element.removeAttribute('aria-hidden')\n      this._element.scrollTop = 0\n\n      if (transition) {\n        Util.reflow(this._element)\n      }\n\n      $(this._element).addClass(ClassName.SHOW)\n\n      if (this._config.focus) {\n        this._enforceFocus()\n      }\n\n      const shownEvent = $.Event(Event.SHOWN, {\n        relatedTarget\n      })\n\n      const transitionComplete = () => {\n        if (this._config.focus) {\n          this._element.focus()\n        }\n        this._isTransitioning = false\n        $(this._element).trigger(shownEvent)\n      }\n\n      if (transition) {\n        $(this._dialog)\n          .one(Util.TRANSITION_END, transitionComplete)\n          .emulateTransitionEnd(TRANSITION_DURATION)\n      } else {\n        transitionComplete()\n      }\n    }\n\n    _enforceFocus() {\n      $(document)\n        .off(Event.FOCUSIN) // Guard against infinite focus loop\n        .on(Event.FOCUSIN, (event) => {\n          if (document !== event.target &&\n              this._element !== event.target &&\n              $(this._element).has(event.target).length === 0) {\n            this._element.focus()\n          }\n        })\n    }\n\n    _setEscapeEvent() {\n      if (this._isShown && this._config.keyboard) {\n        $(this._element).on(Event.KEYDOWN_DISMISS, (event) => {\n          if (event.which === ESCAPE_KEYCODE) {\n            event.preventDefault()\n            this.hide()\n          }\n        })\n      } else if (!this._isShown) {\n        $(this._element).off(Event.KEYDOWN_DISMISS)\n      }\n    }\n\n    _setResizeEvent() {\n      if (this._isShown) {\n        $(window).on(Event.RESIZE, (event) => this.handleUpdate(event))\n      } else {\n        $(window).off(Event.RESIZE)\n      }\n    }\n\n    _hideModal() {\n      this._element.style.display = 'none'\n      this._element.setAttribute('aria-hidden', true)\n      this._isTransitioning = false\n      this._showBackdrop(() => {\n        $(document.body).removeClass(ClassName.OPEN)\n        this._resetAdjustments()\n        this._resetScrollbar()\n        $(this._element).trigger(Event.HIDDEN)\n      })\n    }\n\n    _removeBackdrop() {\n      if (this._backdrop) {\n        $(this._backdrop).remove()\n        this._backdrop = null\n      }\n    }\n\n    _showBackdrop(callback) {\n      const animate = $(this._element).hasClass(ClassName.FADE)\n        ? ClassName.FADE : ''\n\n      if (this._isShown && this._config.backdrop) {\n        const doAnimate = Util.supportsTransitionEnd() && animate\n\n        this._backdrop = document.createElement('div')\n        this._backdrop.className = ClassName.BACKDROP\n\n        if (animate) {\n          $(this._backdrop).addClass(animate)\n        }\n\n        $(this._backdrop).appendTo(document.body)\n\n        $(this._element).on(Event.CLICK_DISMISS, (event) => {\n          if (this._ignoreBackdropClick) {\n            this._ignoreBackdropClick = false\n            return\n          }\n          if (event.target !== event.currentTarget) {\n            return\n          }\n          if (this._config.backdrop === 'static') {\n            this._element.focus()\n          } else {\n            this.hide()\n          }\n        })\n\n        if (doAnimate) {\n          Util.reflow(this._backdrop)\n        }\n\n        $(this._backdrop).addClass(ClassName.SHOW)\n\n        if (!callback) {\n          return\n        }\n\n        if (!doAnimate) {\n          callback()\n          return\n        }\n\n        $(this._backdrop)\n          .one(Util.TRANSITION_END, callback)\n          .emulateTransitionEnd(BACKDROP_TRANSITION_DURATION)\n      } else if (!this._isShown && this._backdrop) {\n        $(this._backdrop).removeClass(ClassName.SHOW)\n\n        const callbackRemove = () => {\n          this._removeBackdrop()\n          if (callback) {\n            callback()\n          }\n        }\n\n        if (Util.supportsTransitionEnd() &&\n           $(this._element).hasClass(ClassName.FADE)) {\n          $(this._backdrop)\n            .one(Util.TRANSITION_END, callbackRemove)\n            .emulateTransitionEnd(BACKDROP_TRANSITION_DURATION)\n        } else {\n          callbackRemove()\n        }\n      } else if (callback) {\n        callback()\n      }\n    }\n\n    // ----------------------------------------------------------------------\n    // the following methods are used to handle overflowing modals\n    // todo (fat): these should probably be refactored out of modal.js\n    // ----------------------------------------------------------------------\n\n    _adjustDialog() {\n      const isModalOverflowing =\n        this._element.scrollHeight > document.documentElement.clientHeight\n\n      if (!this._isBodyOverflowing && isModalOverflowing) {\n        this._element.style.paddingLeft = `${this._scrollbarWidth}px`\n      }\n\n      if (this._isBodyOverflowing && !isModalOverflowing) {\n        this._element.style.paddingRight = `${this._scrollbarWidth}px`\n      }\n    }\n\n    _resetAdjustments() {\n      this._element.style.paddingLeft = ''\n      this._element.style.paddingRight = ''\n    }\n\n    _checkScrollbar() {\n      const rect = document.body.getBoundingClientRect()\n      this._isBodyOverflowing = rect.left + rect.right < window.innerWidth\n      this._scrollbarWidth = this._getScrollbarWidth()\n    }\n\n    _setScrollbar() {\n      if (this._isBodyOverflowing) {\n        // Note: DOMNode.style.paddingRight returns the actual value or '' if not set\n        //   while $(DOMNode).css('padding-right') returns the calculated value or 0 if not set\n\n        // Adjust fixed content padding\n        $(Selector.FIXED_CONTENT).each((index, element) => {\n          const actualPadding = $(element)[0].style.paddingRight\n          const calculatedPadding = $(element).css('padding-right')\n          $(element).data('padding-right', actualPadding).css('padding-right', `${parseFloat(calculatedPadding) + this._scrollbarWidth}px`)\n        })\n\n        // Adjust sticky content margin\n        $(Selector.STICKY_CONTENT).each((index, element) => {\n          const actualMargin = $(element)[0].style.marginRight\n          const calculatedMargin = $(element).css('margin-right')\n          $(element).data('margin-right', actualMargin).css('margin-right', `${parseFloat(calculatedMargin) - this._scrollbarWidth}px`)\n        })\n\n        // Adjust navbar-toggler margin\n        $(Selector.NAVBAR_TOGGLER).each((index, element) => {\n          const actualMargin = $(element)[0].style.marginRight\n          const calculatedMargin = $(element).css('margin-right')\n          $(element).data('margin-right', actualMargin).css('margin-right', `${parseFloat(calculatedMargin) + this._scrollbarWidth}px`)\n        })\n\n        // Adjust body padding\n        const actualPadding = document.body.style.paddingRight\n        const calculatedPadding = $('body').css('padding-right')\n        $('body').data('padding-right', actualPadding).css('padding-right', `${parseFloat(calculatedPadding) + this._scrollbarWidth}px`)\n      }\n    }\n\n    _resetScrollbar() {\n      // Restore fixed content padding\n      $(Selector.FIXED_CONTENT).each((index, element) => {\n        const padding = $(element).data('padding-right')\n        if (typeof padding !== 'undefined') {\n          $(element).css('padding-right', padding).removeData('padding-right')\n        }\n      })\n\n      // Restore sticky content and navbar-toggler margin\n      $(`${Selector.STICKY_CONTENT}, ${Selector.NAVBAR_TOGGLER}`).each((index, element) => {\n        const margin = $(element).data('margin-right')\n        if (typeof margin !== 'undefined') {\n          $(element).css('margin-right', margin).removeData('margin-right')\n        }\n      })\n\n      // Restore body padding\n      const padding = $('body').data('padding-right')\n      if (typeof padding !== 'undefined') {\n        $('body').css('padding-right', padding).removeData('padding-right')\n      }\n    }\n\n    _getScrollbarWidth() { // thx d.walsh\n      const scrollDiv = document.createElement('div')\n      scrollDiv.className = ClassName.SCROLLBAR_MEASURER\n      document.body.appendChild(scrollDiv)\n      const scrollbarWidth = scrollDiv.getBoundingClientRect().width - scrollDiv.clientWidth\n      document.body.removeChild(scrollDiv)\n      return scrollbarWidth\n    }\n\n    // Static\n\n    static _jQueryInterface(config, relatedTarget) {\n      return this.each(function () {\n        let data = $(this).data(DATA_KEY)\n        const _config = {\n          ...Modal.Default,\n          ...$(this).data(),\n          ...typeof config === 'object' && config\n        }\n\n        if (!data) {\n          data = new Modal(this, _config)\n          $(this).data(DATA_KEY, data)\n        }\n\n        if (typeof config === 'string') {\n          if (typeof data[config] === 'undefined') {\n            throw new TypeError(`No method named \"${config}\"`)\n          }\n          data[config](relatedTarget)\n        } else if (_config.show) {\n          data.show(relatedTarget)\n        }\n      })\n    }\n  }\n\n  /**\n   * ------------------------------------------------------------------------\n   * Data Api implementation\n   * ------------------------------------------------------------------------\n   */\n\n  $(document).on(Event.CLICK_DATA_API, Selector.DATA_TOGGLE, function (event) {\n    let target\n    const selector = Util.getSelectorFromElement(this)\n\n    if (selector) {\n      target = $(selector)[0]\n    }\n\n    const config = $(target).data(DATA_KEY)\n      ? 'toggle' : {\n        ...$(target).data(),\n        ...$(this).data()\n      }\n\n    if (this.tagName === 'A' || this.tagName === 'AREA') {\n      event.preventDefault()\n    }\n\n    const $target = $(target).one(Event.SHOW, (showEvent) => {\n      if (showEvent.isDefaultPrevented()) {\n        // Only register focus restorer if modal will actually get shown\n        return\n      }\n\n      $target.one(Event.HIDDEN, () => {\n        if ($(this).is(':visible')) {\n          this.focus()\n        }\n      })\n    })\n\n    Modal._jQueryInterface.call($(target), config, this)\n  })\n\n  /**\n   * ------------------------------------------------------------------------\n   * jQuery\n   * ------------------------------------------------------------------------\n   */\n\n  $.fn[NAME] = Modal._jQueryInterface\n  $.fn[NAME].Constructor = Modal\n  $.fn[NAME].noConflict = function () {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Modal._jQueryInterface\n  }\n\n  return Modal\n})($)\n\nexport default Modal\n", "import $ from 'jquery'\nimport Popper from 'popper.js'\nimport Util from './util'\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.0.0): tooltip.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst Tooltip = (($) => {\n  /**\n   * ------------------------------------------------------------------------\n   * Constants\n   * ------------------------------------------------------------------------\n   */\n\n  const NAME                = 'tooltip'\n  const VERSION             = '4.0.0'\n  const DATA_KEY            = 'bs.tooltip'\n  const EVENT_KEY           = `.${DATA_KEY}`\n  const JQUERY_NO_CONFLICT  = $.fn[NAME]\n  const TRANSITION_DURATION = 150\n  const CLASS_PREFIX        = 'bs-tooltip'\n  const BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\n\n  const DefaultType = {\n    animation           : 'boolean',\n    template            : 'string',\n    title               : '(string|element|function)',\n    trigger             : 'string',\n    delay               : '(number|object)',\n    html                : 'boolean',\n    selector            : '(string|boolean)',\n    placement           : '(string|function)',\n    offset              : '(number|string)',\n    container           : '(string|element|boolean)',\n    fallbackPlacement   : '(string|array)',\n    boundary            : '(string|element)'\n  }\n\n  const AttachmentMap = {\n    AUTO   : 'auto',\n    TOP    : 'top',\n    RIGHT  : 'right',\n    BOTTOM : 'bottom',\n    LEFT   : 'left'\n  }\n\n  const Default = {\n    animation           : true,\n    template            : '<div class=\"tooltip\" role=\"tooltip\">' +\n                        '<div class=\"arrow\"></div>' +\n                        '<div class=\"tooltip-inner\"></div></div>',\n    trigger             : 'hover focus',\n    title               : '',\n    delay               : 0,\n    html                : false,\n    selector            : false,\n    placement           : 'top',\n    offset              : 0,\n    container           : false,\n    fallbackPlacement   : 'flip',\n    boundary            : 'scrollParent'\n  }\n\n  const HoverState = {\n    SHOW : 'show',\n    OUT  : 'out'\n  }\n\n  const Event = {\n    HIDE       : `hide${EVENT_KEY}`,\n    HIDDEN     : `hidden${EVENT_KEY}`,\n    SHOW       : `show${EVENT_KEY}`,\n    SHOWN      : `shown${EVENT_KEY}`,\n    INSERTED   : `inserted${EVENT_KEY}`,\n    CLICK      : `click${EVENT_KEY}`,\n    FOCUSIN    : `focusin${EVENT_KEY}`,\n    FOCUSOUT   : `focusout${EVENT_KEY}`,\n    MOUSEENTER : `mouseenter${EVENT_KEY}`,\n    MOUSELEAVE : `mouseleave${EVENT_KEY}`\n  }\n\n  const ClassName = {\n    FADE : 'fade',\n    SHOW : 'show'\n  }\n\n  const Selector = {\n    TOOLTIP       : '.tooltip',\n    TOOLTIP_INNER : '.tooltip-inner',\n    ARROW         : '.arrow'\n  }\n\n  const Trigger = {\n    HOVER  : 'hover',\n    FOCUS  : 'focus',\n    CLICK  : 'click',\n    MANUAL : 'manual'\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Class Definition\n   * ------------------------------------------------------------------------\n   */\n\n  class Tooltip {\n    constructor(element, config) {\n      /**\n       * Check for Popper dependency\n       * Popper - https://popper.js.org\n       */\n      if (typeof Popper === 'undefined') {\n        throw new TypeError('Bootstrap tooltips require Popper.js (https://popper.js.org)')\n      }\n\n      // private\n      this._isEnabled     = true\n      this._timeout       = 0\n      this._hoverState    = ''\n      this._activeTrigger = {}\n      this._popper        = null\n\n      // Protected\n      this.element = element\n      this.config  = this._getConfig(config)\n      this.tip     = null\n\n      this._setListeners()\n    }\n\n    // Getters\n\n    static get VERSION() {\n      return VERSION\n    }\n\n    static get Default() {\n      return Default\n    }\n\n    static get NAME() {\n      return NAME\n    }\n\n    static get DATA_KEY() {\n      return DATA_KEY\n    }\n\n    static get Event() {\n      return Event\n    }\n\n    static get EVENT_KEY() {\n      return EVENT_KEY\n    }\n\n    static get DefaultType() {\n      return DefaultType\n    }\n\n    // Public\n\n    enable() {\n      this._isEnabled = true\n    }\n\n    disable() {\n      this._isEnabled = false\n    }\n\n    toggleEnabled() {\n      this._isEnabled = !this._isEnabled\n    }\n\n    toggle(event) {\n      if (!this._isEnabled) {\n        return\n      }\n\n      if (event) {\n        const dataKey = this.constructor.DATA_KEY\n        let context = $(event.currentTarget).data(dataKey)\n\n        if (!context) {\n          context = new this.constructor(\n            event.currentTarget,\n            this._getDelegateConfig()\n          )\n          $(event.currentTarget).data(dataKey, context)\n        }\n\n        context._activeTrigger.click = !context._activeTrigger.click\n\n        if (context._isWithActiveTrigger()) {\n          context._enter(null, context)\n        } else {\n          context._leave(null, context)\n        }\n      } else {\n        if ($(this.getTipElement()).hasClass(ClassName.SHOW)) {\n          this._leave(null, this)\n          return\n        }\n\n        this._enter(null, this)\n      }\n    }\n\n    dispose() {\n      clearTimeout(this._timeout)\n\n      $.removeData(this.element, this.constructor.DATA_KEY)\n\n      $(this.element).off(this.constructor.EVENT_KEY)\n      $(this.element).closest('.modal').off('hide.bs.modal')\n\n      if (this.tip) {\n        $(this.tip).remove()\n      }\n\n      this._isEnabled     = null\n      this._timeout       = null\n      this._hoverState    = null\n      this._activeTrigger = null\n      if (this._popper !== null) {\n        this._popper.destroy()\n      }\n\n      this._popper = null\n      this.element = null\n      this.config  = null\n      this.tip     = null\n    }\n\n    show() {\n      if ($(this.element).css('display') === 'none') {\n        throw new Error('Please use show on visible elements')\n      }\n\n      const showEvent = $.Event(this.constructor.Event.SHOW)\n      if (this.isWithContent() && this._isEnabled) {\n        $(this.element).trigger(showEvent)\n\n        const isInTheDom = $.contains(\n          this.element.ownerDocument.documentElement,\n          this.element\n        )\n\n        if (showEvent.isDefaultPrevented() || !isInTheDom) {\n          return\n        }\n\n        const tip   = this.getTipElement()\n        const tipId = Util.getUID(this.constructor.NAME)\n\n        tip.setAttribute('id', tipId)\n        this.element.setAttribute('aria-describedby', tipId)\n\n        this.setContent()\n\n        if (this.config.animation) {\n          $(tip).addClass(ClassName.FADE)\n        }\n\n        const placement  = typeof this.config.placement === 'function'\n          ? this.config.placement.call(this, tip, this.element)\n          : this.config.placement\n\n        const attachment = this._getAttachment(placement)\n        this.addAttachmentClass(attachment)\n\n        const container = this.config.container === false ? document.body : $(this.config.container)\n\n        $(tip).data(this.constructor.DATA_KEY, this)\n\n        if (!$.contains(this.element.ownerDocument.documentElement, this.tip)) {\n          $(tip).appendTo(container)\n        }\n\n        $(this.element).trigger(this.constructor.Event.INSERTED)\n\n        this._popper = new Popper(this.element, tip, {\n          placement: attachment,\n          modifiers: {\n            offset: {\n              offset: this.config.offset\n            },\n            flip: {\n              behavior: this.config.fallbackPlacement\n            },\n            arrow: {\n              element: Selector.ARROW\n            },\n            preventOverflow: {\n              boundariesElement: this.config.boundary\n            }\n          },\n          onCreate: (data) => {\n            if (data.originalPlacement !== data.placement) {\n              this._handlePopperPlacementChange(data)\n            }\n          },\n          onUpdate: (data) => {\n            this._handlePopperPlacementChange(data)\n          }\n        })\n\n        $(tip).addClass(ClassName.SHOW)\n\n        // If this is a touch-enabled device we add extra\n        // empty mouseover listeners to the body's immediate children;\n        // only needed because of broken event delegation on iOS\n        // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n        if ('ontouchstart' in document.documentElement) {\n          $('body').children().on('mouseover', null, $.noop)\n        }\n\n        const complete = () => {\n          if (this.config.animation) {\n            this._fixTransition()\n          }\n          const prevHoverState = this._hoverState\n          this._hoverState     = null\n\n          $(this.element).trigger(this.constructor.Event.SHOWN)\n\n          if (prevHoverState === HoverState.OUT) {\n            this._leave(null, this)\n          }\n        }\n\n        if (Util.supportsTransitionEnd() && $(this.tip).hasClass(ClassName.FADE)) {\n          $(this.tip)\n            .one(Util.TRANSITION_END, complete)\n            .emulateTransitionEnd(Tooltip._TRANSITION_DURATION)\n        } else {\n          complete()\n        }\n      }\n    }\n\n    hide(callback) {\n      const tip       = this.getTipElement()\n      const hideEvent = $.Event(this.constructor.Event.HIDE)\n      const complete = () => {\n        if (this._hoverState !== HoverState.SHOW && tip.parentNode) {\n          tip.parentNode.removeChild(tip)\n        }\n\n        this._cleanTipClass()\n        this.element.removeAttribute('aria-describedby')\n        $(this.element).trigger(this.constructor.Event.HIDDEN)\n        if (this._popper !== null) {\n          this._popper.destroy()\n        }\n\n        if (callback) {\n          callback()\n        }\n      }\n\n      $(this.element).trigger(hideEvent)\n\n      if (hideEvent.isDefaultPrevented()) {\n        return\n      }\n\n      $(tip).removeClass(ClassName.SHOW)\n\n      // If this is a touch-enabled device we remove the extra\n      // empty mouseover listeners we added for iOS support\n      if ('ontouchstart' in document.documentElement) {\n        $('body').children().off('mouseover', null, $.noop)\n      }\n\n      this._activeTrigger[Trigger.CLICK] = false\n      this._activeTrigger[Trigger.FOCUS] = false\n      this._activeTrigger[Trigger.HOVER] = false\n\n      if (Util.supportsTransitionEnd() &&\n          $(this.tip).hasClass(ClassName.FADE)) {\n        $(tip)\n          .one(Util.TRANSITION_END, complete)\n          .emulateTransitionEnd(TRANSITION_DURATION)\n      } else {\n        complete()\n      }\n\n      this._hoverState = ''\n    }\n\n    update() {\n      if (this._popper !== null) {\n        this._popper.scheduleUpdate()\n      }\n    }\n\n    // Protected\n\n    isWithContent() {\n      return Boolean(this.getTitle())\n    }\n\n    addAttachmentClass(attachment) {\n      $(this.getTipElement()).addClass(`${CLASS_PREFIX}-${attachment}`)\n    }\n\n    getTipElement() {\n      this.tip = this.tip || $(this.config.template)[0]\n      return this.tip\n    }\n\n    setContent() {\n      const $tip = $(this.getTipElement())\n      this.setElementContent($tip.find(Selector.TOOLTIP_INNER), this.getTitle())\n      $tip.removeClass(`${ClassName.FADE} ${ClassName.SHOW}`)\n    }\n\n    setElementContent($element, content) {\n      const html = this.config.html\n      if (typeof content === 'object' && (content.nodeType || content.jquery)) {\n        // Content is a DOM node or a jQuery\n        if (html) {\n          if (!$(content).parent().is($element)) {\n            $element.empty().append(content)\n          }\n        } else {\n          $element.text($(content).text())\n        }\n      } else {\n        $element[html ? 'html' : 'text'](content)\n      }\n    }\n\n    getTitle() {\n      let title = this.element.getAttribute('data-original-title')\n\n      if (!title) {\n        title = typeof this.config.title === 'function'\n          ? this.config.title.call(this.element)\n          : this.config.title\n      }\n\n      return title\n    }\n\n    // Private\n\n    _getAttachment(placement) {\n      return AttachmentMap[placement.toUpperCase()]\n    }\n\n    _setListeners() {\n      const triggers = this.config.trigger.split(' ')\n\n      triggers.forEach((trigger) => {\n        if (trigger === 'click') {\n          $(this.element).on(\n            this.constructor.Event.CLICK,\n            this.config.selector,\n            (event) => this.toggle(event)\n          )\n        } else if (trigger !== Trigger.MANUAL) {\n          const eventIn = trigger === Trigger.HOVER\n            ? this.constructor.Event.MOUSEENTER\n            : this.constructor.Event.FOCUSIN\n          const eventOut = trigger === Trigger.HOVER\n            ? this.constructor.Event.MOUSELEAVE\n            : this.constructor.Event.FOCUSOUT\n\n          $(this.element)\n            .on(\n              eventIn,\n              this.config.selector,\n              (event) => this._enter(event)\n            )\n            .on(\n              eventOut,\n              this.config.selector,\n              (event) => this._leave(event)\n            )\n        }\n\n        $(this.element).closest('.modal').on(\n          'hide.bs.modal',\n          () => this.hide()\n        )\n      })\n\n      if (this.config.selector) {\n        this.config = {\n          ...this.config,\n          trigger: 'manual',\n          selector: ''\n        }\n      } else {\n        this._fixTitle()\n      }\n    }\n\n    _fixTitle() {\n      const titleType = typeof this.element.getAttribute('data-original-title')\n      if (this.element.getAttribute('title') ||\n         titleType !== 'string') {\n        this.element.setAttribute(\n          'data-original-title',\n          this.element.getAttribute('title') || ''\n        )\n        this.element.setAttribute('title', '')\n      }\n    }\n\n    _enter(event, context) {\n      const dataKey = this.constructor.DATA_KEY\n\n      context = context || $(event.currentTarget).data(dataKey)\n\n      if (!context) {\n        context = new this.constructor(\n          event.currentTarget,\n          this._getDelegateConfig()\n        )\n        $(event.currentTarget).data(dataKey, context)\n      }\n\n      if (event) {\n        context._activeTrigger[\n          event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER\n        ] = true\n      }\n\n      if ($(context.getTipElement()).hasClass(ClassName.SHOW) ||\n         context._hoverState === HoverState.SHOW) {\n        context._hoverState = HoverState.SHOW\n        return\n      }\n\n      clearTimeout(context._timeout)\n\n      context._hoverState = HoverState.SHOW\n\n      if (!context.config.delay || !context.config.delay.show) {\n        context.show()\n        return\n      }\n\n      context._timeout = setTimeout(() => {\n        if (context._hoverState === HoverState.SHOW) {\n          context.show()\n        }\n      }, context.config.delay.show)\n    }\n\n    _leave(event, context) {\n      const dataKey = this.constructor.DATA_KEY\n\n      context = context || $(event.currentTarget).data(dataKey)\n\n      if (!context) {\n        context = new this.constructor(\n          event.currentTarget,\n          this._getDelegateConfig()\n        )\n        $(event.currentTarget).data(dataKey, context)\n      }\n\n      if (event) {\n        context._activeTrigger[\n          event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER\n        ] = false\n      }\n\n      if (context._isWithActiveTrigger()) {\n        return\n      }\n\n      clearTimeout(context._timeout)\n\n      context._hoverState = HoverState.OUT\n\n      if (!context.config.delay || !context.config.delay.hide) {\n        context.hide()\n        return\n      }\n\n      context._timeout = setTimeout(() => {\n        if (context._hoverState === HoverState.OUT) {\n          context.hide()\n        }\n      }, context.config.delay.hide)\n    }\n\n    _isWithActiveTrigger() {\n      for (const trigger in this._activeTrigger) {\n        if (this._activeTrigger[trigger]) {\n          return true\n        }\n      }\n\n      return false\n    }\n\n    _getConfig(config) {\n      config = {\n        ...this.constructor.Default,\n        ...$(this.element).data(),\n        ...config\n      }\n\n      if (typeof config.delay === 'number') {\n        config.delay = {\n          show: config.delay,\n          hide: config.delay\n        }\n      }\n\n      if (typeof config.title === 'number') {\n        config.title = config.title.toString()\n      }\n\n      if (typeof config.content === 'number') {\n        config.content = config.content.toString()\n      }\n\n      Util.typeCheckConfig(\n        NAME,\n        config,\n        this.constructor.DefaultType\n      )\n\n      return config\n    }\n\n    _getDelegateConfig() {\n      const config = {}\n\n      if (this.config) {\n        for (const key in this.config) {\n          if (this.constructor.Default[key] !== this.config[key]) {\n            config[key] = this.config[key]\n          }\n        }\n      }\n\n      return config\n    }\n\n    _cleanTipClass() {\n      const $tip = $(this.getTipElement())\n      const tabClass = $tip.attr('class').match(BSCLS_PREFIX_REGEX)\n      if (tabClass !== null && tabClass.length > 0) {\n        $tip.removeClass(tabClass.join(''))\n      }\n    }\n\n    _handlePopperPlacementChange(data) {\n      this._cleanTipClass()\n      this.addAttachmentClass(this._getAttachment(data.placement))\n    }\n\n    _fixTransition() {\n      const tip = this.getTipElement()\n      const initConfigAnimation = this.config.animation\n      if (tip.getAttribute('x-placement') !== null) {\n        return\n      }\n      $(tip).removeClass(ClassName.FADE)\n      this.config.animation = false\n      this.hide()\n      this.show()\n      this.config.animation = initConfigAnimation\n    }\n\n    // Static\n\n    static _jQueryInterface(config) {\n      return this.each(function () {\n        let data = $(this).data(DATA_KEY)\n        const _config = typeof config === 'object' && config\n\n        if (!data && /dispose|hide/.test(config)) {\n          return\n        }\n\n        if (!data) {\n          data = new Tooltip(this, _config)\n          $(this).data(DATA_KEY, data)\n        }\n\n        if (typeof config === 'string') {\n          if (typeof data[config] === 'undefined') {\n            throw new TypeError(`No method named \"${config}\"`)\n          }\n          data[config]()\n        }\n      })\n    }\n  }\n\n  /**\n   * ------------------------------------------------------------------------\n   * jQuery\n   * ------------------------------------------------------------------------\n   */\n\n  $.fn[NAME] = Tooltip._jQueryInterface\n  $.fn[NAME].Constructor = Tooltip\n  $.fn[NAME].noConflict = function () {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Tooltip._jQueryInterface\n  }\n\n  return Tooltip\n})($, Popper)\n\nexport default Tooltip\n", "import $ from 'jquery'\nimport Tooltip from './tooltip'\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.0.0): popover.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst Popover = (($) => {\n  /**\n   * ------------------------------------------------------------------------\n   * Constants\n   * ------------------------------------------------------------------------\n   */\n\n  const NAME                = 'popover'\n  const VERSION             = '4.0.0'\n  const DATA_KEY            = 'bs.popover'\n  const EVENT_KEY           = `.${DATA_KEY}`\n  const JQUERY_NO_CONFLICT  = $.fn[NAME]\n  const CLASS_PREFIX        = 'bs-popover'\n  const BSCLS_PREFIX_REGEX  = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\n\n  const Default = {\n    ...Tooltip.Default,\n    placement : 'right',\n    trigger   : 'click',\n    content   : '',\n    template  : '<div class=\"popover\" role=\"tooltip\">' +\n                '<div class=\"arrow\"></div>' +\n                '<h3 class=\"popover-header\"></h3>' +\n                '<div class=\"popover-body\"></div></div>'\n  }\n\n  const DefaultType = {\n    ...Tooltip.DefaultType,\n    content : '(string|element|function)'\n  }\n\n  const ClassName = {\n    FADE : 'fade',\n    SHOW : 'show'\n  }\n\n  const Selector = {\n    TITLE   : '.popover-header',\n    CONTENT : '.popover-body'\n  }\n\n  const Event = {\n    HIDE       : `hide${EVENT_KEY}`,\n    HIDDEN     : `hidden${EVENT_KEY}`,\n    SHOW       : `show${EVENT_KEY}`,\n    SHOWN      : `shown${EVENT_KEY}`,\n    INSERTED   : `inserted${EVENT_KEY}`,\n    CLICK      : `click${EVENT_KEY}`,\n    FOCUSIN    : `focusin${EVENT_KEY}`,\n    FOCUSOUT   : `focusout${EVENT_KEY}`,\n    MOUSEENTER : `mouseenter${EVENT_KEY}`,\n    MOUSELEAVE : `mouseleave${EVENT_KEY}`\n  }\n\n  /**\n   * ------------------------------------------------------------------------\n   * Class Definition\n   * ------------------------------------------------------------------------\n   */\n\n  class Popover extends Tooltip {\n    // Getters\n\n    static get VERSION() {\n      return VERSION\n    }\n\n    static get Default() {\n      return Default\n    }\n\n    static get NAME() {\n      return NAME\n    }\n\n    static get DATA_KEY() {\n      return DATA_KEY\n    }\n\n    static get Event() {\n      return Event\n    }\n\n    static get EVENT_KEY() {\n      return EVENT_KEY\n    }\n\n    static get DefaultType() {\n      return DefaultType\n    }\n\n    // Overrides\n\n    isWithContent() {\n      return this.getTitle() || this._getContent()\n    }\n\n    addAttachmentClass(attachment) {\n      $(this.getTipElement()).addClass(`${CLASS_PREFIX}-${attachment}`)\n    }\n\n    getTipElement() {\n      this.tip = this.tip || $(this.config.template)[0]\n      return this.tip\n    }\n\n    setContent() {\n      const $tip = $(this.getTipElement())\n\n      // We use append for html objects to maintain js events\n      this.setElementContent($tip.find(Selector.TITLE), this.getTitle())\n      let content = this._getContent()\n      if (typeof content === 'function') {\n        content = content.call(this.element)\n      }\n      this.setElementContent($tip.find(Selector.CONTENT), content)\n\n      $tip.removeClass(`${ClassName.FADE} ${ClassName.SHOW}`)\n    }\n\n    // Private\n\n    _getContent() {\n      return this.element.getAttribute('data-content') ||\n        this.config.content\n    }\n\n    _cleanTipClass() {\n      const $tip = $(this.getTipElement())\n      const tabClass = $tip.attr('class').match(BSCLS_PREFIX_REGEX)\n      if (tabClass !== null && tabClass.length > 0) {\n        $tip.removeClass(tabClass.join(''))\n      }\n    }\n\n    // Static\n\n    static _jQueryInterface(config) {\n      return this.each(function () {\n        let data = $(this).data(DATA_KEY)\n        const _config = typeof config === 'object' ? config : null\n\n        if (!data && /destroy|hide/.test(config)) {\n          return\n        }\n\n        if (!data) {\n          data = new Popover(this, _config)\n          $(this).data(DATA_KEY, data)\n        }\n\n        if (typeof config === 'string') {\n          if (typeof data[config] === 'undefined') {\n            throw new TypeError(`No method named \"${config}\"`)\n          }\n          data[config]()\n        }\n      })\n    }\n  }\n\n  /**\n   * ------------------------------------------------------------------------\n   * jQuery\n   * ------------------------------------------------------------------------\n   */\n\n  $.fn[NAME] = Popover._jQueryInterface\n  $.fn[NAME].Constructor = Popover\n  $.fn[NAME].noConflict = function () {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Popover._jQueryInterface\n  }\n\n  return Popover\n})($)\n\nexport default Popover\n", "import $ from 'jquery'\nimport Util from './util'\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.0.0): scrollspy.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst ScrollSpy = (($) => {\n  /**\n   * ------------------------------------------------------------------------\n   * Constants\n   * ------------------------------------------------------------------------\n   */\n\n  const NAME               = 'scrollspy'\n  const VERSION            = '4.0.0'\n  const DATA_KEY           = 'bs.scrollspy'\n  const EVENT_KEY          = `.${DATA_KEY}`\n  const DATA_API_KEY       = '.data-api'\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n\n  const Default = {\n    offset : 10,\n    method : 'auto',\n    target : ''\n  }\n\n  const DefaultType = {\n    offset : 'number',\n    method : 'string',\n    target : '(string|element)'\n  }\n\n  const Event = {\n    ACTIVATE      : `activate${EVENT_KEY}`,\n    SCROLL        : `scroll${EVENT_KEY}`,\n    LOAD_DATA_API : `load${EVENT_KEY}${DATA_API_KEY}`\n  }\n\n  const ClassName = {\n    DROPDOWN_ITEM : 'dropdown-item',\n    DROPDOWN_MENU : 'dropdown-menu',\n    ACTIVE        : 'active'\n  }\n\n  const Selector = {\n    DATA_SPY        : '[data-spy=\"scroll\"]',\n    ACTIVE          : '.active',\n    NAV_LIST_GROUP  : '.nav, .list-group',\n    NAV_LINKS       : '.nav-link',\n    NAV_ITEMS       : '.nav-item',\n    LIST_ITEMS      : '.list-group-item',\n    DROPDOWN        : '.dropdown',\n    DROPDOWN_ITEMS  : '.dropdown-item',\n    DROPDOWN_TOGGLE : '.dropdown-toggle'\n  }\n\n  const OffsetMethod = {\n    OFFSET   : 'offset',\n    POSITION : 'position'\n  }\n\n  /**\n   * ------------------------------------------------------------------------\n   * Class Definition\n   * ------------------------------------------------------------------------\n   */\n\n  class ScrollSpy {\n    constructor(element, config) {\n      this._element       = element\n      this._scrollElement = element.tagName === 'BODY' ? window : element\n      this._config        = this._getConfig(config)\n      this._selector      = `${this._config.target} ${Selector.NAV_LINKS},` +\n                            `${this._config.target} ${Selector.LIST_ITEMS},` +\n                            `${this._config.target} ${Selector.DROPDOWN_ITEMS}`\n      this._offsets       = []\n      this._targets       = []\n      this._activeTarget  = null\n      this._scrollHeight  = 0\n\n      $(this._scrollElement).on(Event.SCROLL, (event) => this._process(event))\n\n      this.refresh()\n      this._process()\n    }\n\n    // Getters\n\n    static get VERSION() {\n      return VERSION\n    }\n\n    static get Default() {\n      return Default\n    }\n\n    // Public\n\n    refresh() {\n      const autoMethod = this._scrollElement === this._scrollElement.window\n        ? OffsetMethod.OFFSET : OffsetMethod.POSITION\n\n      const offsetMethod = this._config.method === 'auto'\n        ? autoMethod : this._config.method\n\n      const offsetBase = offsetMethod === OffsetMethod.POSITION\n        ? this._getScrollTop() : 0\n\n      this._offsets = []\n      this._targets = []\n\n      this._scrollHeight = this._getScrollHeight()\n\n      const targets = $.makeArray($(this._selector))\n\n      targets\n        .map((element) => {\n          let target\n          const targetSelector = Util.getSelectorFromElement(element)\n\n          if (targetSelector) {\n            target = $(targetSelector)[0]\n          }\n\n          if (target) {\n            const targetBCR = target.getBoundingClientRect()\n            if (targetBCR.width || targetBCR.height) {\n              // TODO (fat): remove sketch reliance on jQuery position/offset\n              return [\n                $(target)[offsetMethod]().top + offsetBase,\n                targetSelector\n              ]\n            }\n          }\n          return null\n        })\n        .filter((item) => item)\n        .sort((a, b) => a[0] - b[0])\n        .forEach((item) => {\n          this._offsets.push(item[0])\n          this._targets.push(item[1])\n        })\n    }\n\n    dispose() {\n      $.removeData(this._element, DATA_KEY)\n      $(this._scrollElement).off(EVENT_KEY)\n\n      this._element       = null\n      this._scrollElement = null\n      this._config        = null\n      this._selector      = null\n      this._offsets       = null\n      this._targets       = null\n      this._activeTarget  = null\n      this._scrollHeight  = null\n    }\n\n    // Private\n\n    _getConfig(config) {\n      config = {\n        ...Default,\n        ...config\n      }\n\n      if (typeof config.target !== 'string') {\n        let id = $(config.target).attr('id')\n        if (!id) {\n          id = Util.getUID(NAME)\n          $(config.target).attr('id', id)\n        }\n        config.target = `#${id}`\n      }\n\n      Util.typeCheckConfig(NAME, config, DefaultType)\n\n      return config\n    }\n\n    _getScrollTop() {\n      return this._scrollElement === window\n        ? this._scrollElement.pageYOffset : this._scrollElement.scrollTop\n    }\n\n    _getScrollHeight() {\n      return this._scrollElement.scrollHeight || Math.max(\n        document.body.scrollHeight,\n        document.documentElement.scrollHeight\n      )\n    }\n\n    _getOffsetHeight() {\n      return this._scrollElement === window\n        ? window.innerHeight : this._scrollElement.getBoundingClientRect().height\n    }\n\n    _process() {\n      const scrollTop    = this._getScrollTop() + this._config.offset\n      const scrollHeight = this._getScrollHeight()\n      const maxScroll    = this._config.offset +\n        scrollHeight -\n        this._getOffsetHeight()\n\n      if (this._scrollHeight !== scrollHeight) {\n        this.refresh()\n      }\n\n      if (scrollTop >= maxScroll) {\n        const target = this._targets[this._targets.length - 1]\n\n        if (this._activeTarget !== target) {\n          this._activate(target)\n        }\n        return\n      }\n\n      if (this._activeTarget && scrollTop < this._offsets[0] && this._offsets[0] > 0) {\n        this._activeTarget = null\n        this._clear()\n        return\n      }\n\n      for (let i = this._offsets.length; i--;) {\n        const isActiveTarget = this._activeTarget !== this._targets[i] &&\n            scrollTop >= this._offsets[i] &&\n            (typeof this._offsets[i + 1] === 'undefined' ||\n                scrollTop < this._offsets[i + 1])\n\n        if (isActiveTarget) {\n          this._activate(this._targets[i])\n        }\n      }\n    }\n\n    _activate(target) {\n      this._activeTarget = target\n\n      this._clear()\n\n      let queries = this._selector.split(',')\n      // eslint-disable-next-line arrow-body-style\n      queries = queries.map((selector) => {\n        return `${selector}[data-target=\"${target}\"],` +\n               `${selector}[href=\"${target}\"]`\n      })\n\n      const $link = $(queries.join(','))\n\n      if ($link.hasClass(ClassName.DROPDOWN_ITEM)) {\n        $link.closest(Selector.DROPDOWN).find(Selector.DROPDOWN_TOGGLE).addClass(ClassName.ACTIVE)\n        $link.addClass(ClassName.ACTIVE)\n      } else {\n        // Set triggered link as active\n        $link.addClass(ClassName.ACTIVE)\n        // Set triggered links parents as active\n        // With both <ul> and <nav> markup a parent is the previous sibling of any nav ancestor\n        $link.parents(Selector.NAV_LIST_GROUP).prev(`${Selector.NAV_LINKS}, ${Selector.LIST_ITEMS}`).addClass(ClassName.ACTIVE)\n        // Handle special case when .nav-link is inside .nav-item\n        $link.parents(Selector.NAV_LIST_GROUP).prev(Selector.NAV_ITEMS).children(Selector.NAV_LINKS).addClass(ClassName.ACTIVE)\n      }\n\n      $(this._scrollElement).trigger(Event.ACTIVATE, {\n        relatedTarget: target\n      })\n    }\n\n    _clear() {\n      $(this._selector).filter(Selector.ACTIVE).removeClass(ClassName.ACTIVE)\n    }\n\n    // Static\n\n    static _jQueryInterface(config) {\n      return this.each(function () {\n        let data = $(this).data(DATA_KEY)\n        const _config = typeof config === 'object' && config\n\n        if (!data) {\n          data = new ScrollSpy(this, _config)\n          $(this).data(DATA_KEY, data)\n        }\n\n        if (typeof config === 'string') {\n          if (typeof data[config] === 'undefined') {\n            throw new TypeError(`No method named \"${config}\"`)\n          }\n          data[config]()\n        }\n      })\n    }\n  }\n\n  /**\n   * ------------------------------------------------------------------------\n   * Data Api implementation\n   * ------------------------------------------------------------------------\n   */\n\n  $(window).on(Event.LOAD_DATA_API, () => {\n    const scrollSpys = $.makeArray($(Selector.DATA_SPY))\n\n    for (let i = scrollSpys.length; i--;) {\n      const $spy = $(scrollSpys[i])\n      ScrollSpy._jQueryInterface.call($spy, $spy.data())\n    }\n  })\n\n  /**\n   * ------------------------------------------------------------------------\n   * jQuery\n   * ------------------------------------------------------------------------\n   */\n\n  $.fn[NAME] = ScrollSpy._jQueryInterface\n  $.fn[NAME].Constructor = ScrollSpy\n  $.fn[NAME].noConflict = function () {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return ScrollSpy._jQueryInterface\n  }\n\n  return ScrollSpy\n})($)\n\nexport default ScrollSpy\n", "import $ from 'jquery'\nimport Util from './util'\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.0.0): tab.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst Tab = (($) => {\n  /**\n   * ------------------------------------------------------------------------\n   * Constants\n   * ------------------------------------------------------------------------\n   */\n\n  const NAME                = 'tab'\n  const VERSION             = '4.0.0'\n  const DATA_KEY            = 'bs.tab'\n  const EVENT_KEY           = `.${DATA_KEY}`\n  const DATA_API_KEY        = '.data-api'\n  const JQUERY_NO_CONFLICT  = $.fn[NAME]\n  const TRANSITION_DURATION = 150\n\n  const Event = {\n    HIDE           : `hide${EVENT_KEY}`,\n    HIDDEN         : `hidden${EVENT_KEY}`,\n    SHOW           : `show${EVENT_KEY}`,\n    SHOWN          : `shown${EVENT_KEY}`,\n    CLICK_DATA_API : `click${EVENT_KEY}${DATA_API_KEY}`\n  }\n\n  const ClassName = {\n    DROPDOWN_MENU : 'dropdown-menu',\n    ACTIVE        : 'active',\n    DISABLED      : 'disabled',\n    FADE          : 'fade',\n    SHOW          : 'show'\n  }\n\n  const Selector = {\n    DROPDOWN              : '.dropdown',\n    NAV_LIST_GROUP        : '.nav, .list-group',\n    ACTIVE                : '.active',\n    ACTIVE_UL             : '> li > .active',\n    DATA_TOGGLE           : '[data-toggle=\"tab\"], [data-toggle=\"pill\"], [data-toggle=\"list\"]',\n    DROPDOWN_TOGGLE       : '.dropdown-toggle',\n    DROPDOWN_ACTIVE_CHILD : '> .dropdown-menu .active'\n  }\n\n  /**\n   * ------------------------------------------------------------------------\n   * Class Definition\n   * ------------------------------------------------------------------------\n   */\n\n  class Tab {\n    constructor(element) {\n      this._element = element\n    }\n\n    // Getters\n\n    static get VERSION() {\n      return VERSION\n    }\n\n    // Public\n\n    show() {\n      if (this._element.parentNode &&\n          this._element.parentNode.nodeType === Node.ELEMENT_NODE &&\n          $(this._element).hasClass(ClassName.ACTIVE) ||\n          $(this._element).hasClass(ClassName.DISABLED)) {\n        return\n      }\n\n      let target\n      let previous\n      const listElement = $(this._element).closest(Selector.NAV_LIST_GROUP)[0]\n      const selector = Util.getSelectorFromElement(this._element)\n\n      if (listElement) {\n        const itemSelector = listElement.nodeName === 'UL' ? Selector.ACTIVE_UL : Selector.ACTIVE\n        previous = $.makeArray($(listElement).find(itemSelector))\n        previous = previous[previous.length - 1]\n      }\n\n      const hideEvent = $.Event(Event.HIDE, {\n        relatedTarget: this._element\n      })\n\n      const showEvent = $.Event(Event.SHOW, {\n        relatedTarget: previous\n      })\n\n      if (previous) {\n        $(previous).trigger(hideEvent)\n      }\n\n      $(this._element).trigger(showEvent)\n\n      if (showEvent.isDefaultPrevented() ||\n         hideEvent.isDefaultPrevented()) {\n        return\n      }\n\n      if (selector) {\n        target = $(selector)[0]\n      }\n\n      this._activate(\n        this._element,\n        listElement\n      )\n\n      const complete = () => {\n        const hiddenEvent = $.Event(Event.HIDDEN, {\n          relatedTarget: this._element\n        })\n\n        const shownEvent = $.Event(Event.SHOWN, {\n          relatedTarget: previous\n        })\n\n        $(previous).trigger(hiddenEvent)\n        $(this._element).trigger(shownEvent)\n      }\n\n      if (target) {\n        this._activate(target, target.parentNode, complete)\n      } else {\n        complete()\n      }\n    }\n\n    dispose() {\n      $.removeData(this._element, DATA_KEY)\n      this._element = null\n    }\n\n    // Private\n\n    _activate(element, container, callback) {\n      let activeElements\n      if (container.nodeName === 'UL') {\n        activeElements = $(container).find(Selector.ACTIVE_UL)\n      } else {\n        activeElements = $(container).children(Selector.ACTIVE)\n      }\n\n      const active = activeElements[0]\n      const isTransitioning = callback &&\n        Util.supportsTransitionEnd() &&\n        (active && $(active).hasClass(ClassName.FADE))\n\n      const complete = () => this._transitionComplete(\n        element,\n        active,\n        callback\n      )\n\n      if (active && isTransitioning) {\n        $(active)\n          .one(Util.TRANSITION_END, complete)\n          .emulateTransitionEnd(TRANSITION_DURATION)\n      } else {\n        complete()\n      }\n    }\n\n    _transitionComplete(element, active, callback) {\n      if (active) {\n        $(active).removeClass(`${ClassName.SHOW} ${ClassName.ACTIVE}`)\n\n        const dropdownChild = $(active.parentNode).find(\n          Selector.DROPDOWN_ACTIVE_CHILD\n        )[0]\n\n        if (dropdownChild) {\n          $(dropdownChild).removeClass(ClassName.ACTIVE)\n        }\n\n        if (active.getAttribute('role') === 'tab') {\n          active.setAttribute('aria-selected', false)\n        }\n      }\n\n      $(element).addClass(ClassName.ACTIVE)\n      if (element.getAttribute('role') === 'tab') {\n        element.setAttribute('aria-selected', true)\n      }\n\n      Util.reflow(element)\n      $(element).addClass(ClassName.SHOW)\n\n      if (element.parentNode &&\n          $(element.parentNode).hasClass(ClassName.DROPDOWN_MENU)) {\n        const dropdownElement = $(element).closest(Selector.DROPDOWN)[0]\n        if (dropdownElement) {\n          $(dropdownElement).find(Selector.DROPDOWN_TOGGLE).addClass(ClassName.ACTIVE)\n        }\n\n        element.setAttribute('aria-expanded', true)\n      }\n\n      if (callback) {\n        callback()\n      }\n    }\n\n    // Static\n\n    static _jQueryInterface(config) {\n      return this.each(function () {\n        const $this = $(this)\n        let data = $this.data(DATA_KEY)\n\n        if (!data) {\n          data = new Tab(this)\n          $this.data(DATA_KEY, data)\n        }\n\n        if (typeof config === 'string') {\n          if (typeof data[config] === 'undefined') {\n            throw new TypeError(`No method named \"${config}\"`)\n          }\n          data[config]()\n        }\n      })\n    }\n  }\n\n  /**\n   * ------------------------------------------------------------------------\n   * Data Api implementation\n   * ------------------------------------------------------------------------\n   */\n\n  $(document)\n    .on(Event.CLICK_DATA_API, Selector.DATA_TOGGLE, function (event) {\n      event.preventDefault()\n      Tab._jQueryInterface.call($(this), 'show')\n    })\n\n  /**\n   * ------------------------------------------------------------------------\n   * jQuery\n   * ------------------------------------------------------------------------\n   */\n\n  $.fn[NAME] = Tab._jQueryInterface\n  $.fn[NAME].Constructor = Tab\n  $.fn[NAME].noConflict = function () {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Tab._jQueryInterface\n  }\n\n  return Tab\n})($)\n\nexport default Tab\n", "import $ from 'jquery'\nimport Al<PERSON> from './alert'\nimport <PERSON><PERSON> from './button'\nimport Carousel from './carousel'\nimport Collapse from './collapse'\nimport Dropdown from './dropdown'\nimport Modal from './modal'\nimport Popover from './popover'\nimport Scrollspy from './scrollspy'\nimport Tab from './tab'\nimport Tooltip from './tooltip'\nimport Util from './util'\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.0.0-alpha.6): index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n(($) => {\n  if (typeof $ === 'undefined') {\n    throw new TypeError('Bootstrap\\'s JavaScript requires jQuery. jQuery must be included before Bootstrap\\'s JavaScript.')\n  }\n\n  const version = $.fn.jquery.split(' ')[0].split('.')\n  const minMajor = 1\n  const ltMajor = 2\n  const minMinor = 9\n  const minPatch = 1\n  const maxMajor = 4\n\n  if (version[0] < ltMajor && version[1] < minMinor || version[0] === minMajor && version[1] === minMinor && version[2] < minPatch || version[0] >= maxMajor) {\n    throw new Error('Bootstrap\\'s JavaScript requires at least jQuery v1.9.1 but less than v4.0.0')\n  }\n})($)\n\nexport {\n  Util,\n  Alert,\n  Button,\n  Carousel,\n  Collapse,\n  Dropdown,\n  Modal,\n  Popover,\n  Scrollspy,\n  Tab,\n  Tooltip\n}\n"], "names": ["<PERSON><PERSON>", "$", "transition", "MAX_UID", "toType", "obj", "toString", "call", "match", "toLowerCase", "getSpecialTransitionEndEvent", "end", "event", "target", "is", "handleObj", "handler", "apply", "arguments", "undefined", "transitionEndTest", "window", "QUnit", "transitionEndEmulator", "duration", "called", "one", "TRANSITION_END", "triggerTransitionEnd", "setTransitionEndSupport", "fn", "emulateTransitionEnd", "supportsTransitionEnd", "special", "escapeId", "selector", "escapeSelector", "substr", "replace", "prefix", "Math", "random", "document", "getElementById", "element", "getAttribute", "char<PERSON>t", "$selector", "find", "length", "err", "offsetHeight", "trigger", "Boolean", "nodeType", "componentName", "config", "configTypes", "property", "Object", "prototype", "hasOwnProperty", "expectedTypes", "value", "valueType", "isElement", "RegExp", "test", "Error", "toUpperCase", "<PERSON><PERSON>", "NAME", "VERSION", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "JQUERY_NO_CONFLICT", "TRANSITION_DURATION", "Selector", "Event", "ClassName", "_element", "close", "rootElement", "_getRootElement", "customEvent", "_triggerCloseEvent", "isDefaultPrevented", "_removeElement", "dispose", "removeData", "getSelectorFromElement", "parent", "closest", "ALERT", "closeEvent", "CLOSE", "removeClass", "SHOW", "hasClass", "FADE", "_destroyElement", "detach", "CLOSED", "remove", "_jQueryInterface", "each", "$element", "data", "_handleDismiss", "alertInstance", "preventDefault", "on", "CLICK_DATA_API", "DISMISS", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "<PERSON><PERSON>", "toggle", "triggerChangeEvent", "addAriaPressed", "DATA_TOGGLE", "input", "INPUT", "type", "checked", "ACTIVE", "activeElement", "hasAttribute", "classList", "contains", "focus", "setAttribute", "toggleClass", "DATA_TOGGLE_CARROT", "button", "BUTTON", "FOCUS_BLUR_DATA_API", "FOCUS", "Carousel", "ARROW_LEFT_KEYCODE", "ARROW_RIGHT_KEYCODE", "TOUCHEVENT_COMPAT_WAIT", "<PERSON><PERSON><PERSON>", "DefaultType", "Direction", "_items", "_interval", "_activeElement", "_isPaused", "_isSliding", "touchTimeout", "_config", "_getConfig", "_indicatorsElement", "INDICATORS", "_addEventListeners", "next", "_slide", "NEXT", "nextWhenVisible", "hidden", "css", "prev", "PREV", "pause", "NEXT_PREV", "cycle", "interval", "setInterval", "visibilityState", "bind", "to", "index", "ACTIVE_ITEM", "activeIndex", "_getItemIndex", "SLID", "direction", "off", "typeCheckConfig", "keyboard", "KEYDOWN", "_keydown", "MOUSEENTER", "MOUSELEAVE", "documentElement", "TOUCHEND", "setTimeout", "tagName", "which", "makeArray", "ITEM", "indexOf", "_getItemByDirection", "isNextDirection", "isPrevDirection", "lastItemIndex", "isGoingToWrap", "wrap", "delta", "itemIndex", "_triggerSlideEvent", "relatedTarget", "eventDirectionName", "targetIndex", "fromIndex", "slideEvent", "SLIDE", "_setActiveIndicatorElement", "nextIndicator", "children", "addClass", "activeElementIndex", "nextElement", "nextElementIndex", "isCycling", "directionalClassName", "orderClassName", "LEFT", "RIGHT", "slidEvent", "reflow", "action", "slide", "TypeError", "_dataApiClickHandler", "CAROUSEL", "slideIndex", "DATA_SLIDE", "LOAD_DATA_API", "DATA_RIDE", "$carousel", "Collapse", "Dimension", "_isTransitioning", "_triggerArray", "id", "tab<PERSON>og<PERSON>", "i", "elem", "filter", "_selector", "push", "_parent", "_getParent", "_addAriaAndCollapsedClass", "hide", "show", "actives", "activesData", "ACTIVES", "not", "startEvent", "dimension", "_getDimension", "COLLAPSE", "COLLAPSING", "style", "COLLAPSED", "attr", "setTransitioning", "complete", "SHOWN", "capitalizedDimension", "slice", "scrollSize", "HIDE", "getBoundingClientRect", "$elem", "HIDDEN", "isTransitioning", "<PERSON><PERSON><PERSON><PERSON>", "WIDTH", "HEIGHT", "j<PERSON>y", "_getTargetFromElement", "trigger<PERSON><PERSON>y", "isOpen", "$this", "currentTarget", "$trigger", "$target", "_extends", "Dropdown", "ESCAPE_KEYCODE", "SPACE_KEYCODE", "TAB_KEYCODE", "ARROW_UP_KEYCODE", "ARROW_DOWN_KEYCODE", "RIGHT_MOUSE_BUTTON_WHICH", "REGEXP_KEYDOWN", "AttachmentMap", "_popper", "_menu", "_getMenuElement", "_inNavbar", "_detectNavbar", "disabled", "DISABLED", "_getParentFromElement", "isActive", "_clearMenus", "showEvent", "<PERSON><PERSON>", "DROPUP", "MENULEFT", "MENURIGHT", "boundary", "POSITION_STATIC", "_getPopperConfig", "NAVBAR_NAV", "noop", "destroy", "update", "scheduleUpdate", "CLICK", "stopPropagation", "constructor", "MENU", "_getPlacement", "$parentDropdown", "placement", "BOTTOM", "TOP", "TOPEND", "DROPRIGHT", "DROPLEFT", "BOTTOMEND", "offsetConf", "offset", "offsets", "popperConfig", "flip", "toggles", "context", "dropdownMenu", "hideEvent", "parentNode", "_dataApiKeydownHandler", "items", "VISIBLE_ITEMS", "get", "KEYDOWN_DATA_API", "KEYUP_DATA_API", "FORM_CHILD", "e", "Modal", "BACKDROP_TRANSITION_DURATION", "_dialog", "DIALOG", "_backdrop", "_isShown", "_isBodyOverflowing", "_ignoreBackdropClick", "_originalBodyPadding", "_scrollbarWidth", "_checkScrollbar", "_setScrollbar", "_adjustDialog", "body", "OPEN", "_setEscapeEvent", "_setResizeEvent", "CLICK_DISMISS", "DATA_DISMISS", "MOUSEDOWN_DISMISS", "MOUSEUP_DISMISS", "_showBackdrop", "_showElement", "FOCUSIN", "_hideModal", "handleUpdate", "Node", "ELEMENT_NODE", "append<PERSON><PERSON><PERSON>", "display", "removeAttribute", "scrollTop", "_enforceFocus", "shownEvent", "transitionComplete", "has", "KEYDOWN_DISMISS", "RESIZE", "_resetAdjustments", "_resetScrollbar", "_removeBackdrop", "callback", "animate", "backdrop", "doAnimate", "createElement", "className", "BACKDROP", "appendTo", "callback<PERSON><PERSON><PERSON>", "isModalOverflowing", "scrollHeight", "clientHeight", "paddingLeft", "paddingRight", "rect", "left", "right", "innerWidth", "_getScrollbarWidth", "FIXED_CONTENT", "actualPadding", "calculatedPadding", "parseFloat", "STICKY_CONTENT", "<PERSON><PERSON><PERSON><PERSON>", "marginRight", "<PERSON><PERSON><PERSON><PERSON>", "NAVBAR_TOGGLER", "padding", "margin", "scrollDiv", "SCROLLBAR_MEASURER", "scrollbarWidth", "width", "clientWidth", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "CLASS_PREFIX", "BSCLS_PREFIX_REGEX", "HoverState", "<PERSON><PERSON>", "_isEnabled", "_timeout", "_hoverState", "_activeTrigger", "tip", "_setListeners", "enable", "disable", "toggle<PERSON>nabled", "dataKey", "_getDelegateConfig", "click", "_isWithActiveTrigger", "_enter", "_leave", "getTipElement", "isWithContent", "isInTheDom", "ownerDocument", "tipId", "getUID", "<PERSON><PERSON><PERSON><PERSON>", "animation", "attachment", "_getAttachment", "addAttachmentClass", "container", "INSERTED", "fallbackPlacement", "ARROW", "originalPlacement", "_handlePopperPlacementChange", "_fixTransition", "prevHoverState", "OUT", "_TRANSITION_DURATION", "_cleanTipClass", "HOVER", "getTitle", "template", "$tip", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TOOLTIP_INNER", "content", "html", "empty", "append", "text", "title", "triggers", "split", "for<PERSON>ach", "MANUAL", "eventIn", "eventOut", "FOCUSOUT", "_fixTitle", "titleType", "delay", "key", "tabClass", "join", "initConfigAnimation", "Popover", "_getContent", "TITLE", "CONTENT", "ScrollSpy", "OffsetMethod", "_scrollElement", "NAV_LINKS", "LIST_ITEMS", "DROPDOWN_ITEMS", "_offsets", "_targets", "_activeTarget", "_scrollHeight", "SCROLL", "_process", "refresh", "autoMethod", "OFFSET", "POSITION", "offsetMethod", "method", "offsetBase", "_getScrollTop", "_getScrollHeight", "targets", "map", "targetSelector", "targetBCR", "height", "top", "item", "sort", "a", "b", "pageYOffset", "max", "_getOffsetHeight", "innerHeight", "maxScroll", "_activate", "_clear", "isActiveTarget", "queries", "$link", "DROPDOWN_ITEM", "DROPDOWN", "DROPDOWN_TOGGLE", "parents", "NAV_LIST_GROUP", "NAV_ITEMS", "ACTIVATE", "scrollSpys", "DATA_SPY", "$spy", "Tab", "previous", "listElement", "itemSelector", "nodeName", "ACTIVE_UL", "hiddenEvent", "activeElements", "active", "_transitionComplete", "dropdown<PERSON><PERSON>d", "DROPDOWN_ACTIVE_CHILD", "DROPDOWN_MENU", "dropdownElement", "version", "min<PERSON><PERSON><PERSON>", "ltMajor", "minMinor", "minPatch", "max<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;;;;AAEA,SAAS,iBAAiB,CAAC,MAAM,EAAE,KAAK,EAAE;EACxC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;IACrC,IAAI,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IAC1B,UAAU,CAAC,UAAU,GAAG,UAAU,CAAC,UAAU,IAAI,KAAK,CAAC;IACvD,UAAU,CAAC,YAAY,GAAG,IAAI,CAAC;IAC/B,IAAI,OAAO,IAAI,UAAU,EAAE,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC;IACtD,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,UAAU,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;GAC3D;CACF;;AAED,SAAS,YAAY,CAAC,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE;EAC1D,IAAI,UAAU,EAAE,iBAAiB,CAAC,WAAW,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;EACrE,IAAI,WAAW,EAAE,iBAAiB,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;EAC7D,OAAO,WAAW,CAAC;CACpB;;AAED,SAAS,QAAQ,GAAG;EAClB,QAAQ,GAAG,MAAM,CAAC,MAAM,IAAI,UAAU,MAAM,EAAE;IAC5C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;MACzC,IAAI,MAAM,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;;MAE1B,KAAK,IAAI,GAAG,IAAI,MAAM,EAAE;QACtB,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE;UACrD,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;SAC3B;OACF;KACF;;IAED,OAAO,MAAM,CAAC;GACf,CAAC;;EAEF,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;CACxC;;AAED,SAAS,cAAc,CAAC,QAAQ,EAAE,UAAU,EAAE;EAC5C,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;EACzD,QAAQ,CAAC,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC;EAC1C,QAAQ,CAAC,SAAS,GAAG,UAAU,CAAC;;;CACjC,DCtCD;;;;;;;AAOA,IAAMA,OAAQ,UAACC,IAAD,EAAO;;;;;;MAOfC,aAAa,KAAjB;MAEMC,UAAU,OAAhB,CATmB;;WAYVC,MAAT,CAAgBC,GAAhB,EAAqB;WACZ,GAAGC,QAAH,CAAYC,IAAZ,CAAiBF,GAAjB,EAAsBG,KAAtB,CAA4B,eAA5B,EAA6C,CAA7C,EAAgDC,WAAhD,EAAP;;;WAGOC,4BAAT,GAAwC;WAC/B;gBACKR,WAAWS,GADhB;oBAEST,WAAWS,GAFpB;YAAA,kBAGEC,KAHF,EAGS;YACRX,KAAEW,MAAMC,MAAR,EAAgBC,EAAhB,CAAmB,IAAnB,CAAJ,EAA8B;iBACrBF,MAAMG,SAAN,CAAgBC,OAAhB,CAAwBC,KAAxB,CAA8B,IAA9B,EAAoCC,SAApC,CAAP,CAD4B;;;eAGvBC,SAAP,CAJY;;KAHhB;;;WAYOC,iBAAT,GAA6B;QACvB,OAAOC,MAAP,KAAkB,WAAlB,IAAiCA,OAAOC,KAA5C,EAAmD;aAC1C,KAAP;;;WAGK;WACA;KADP;;;WAKOC,qBAAT,CAA+BC,QAA/B,EAAyC;;;QACnCC,SAAS,KAAb;SAEE,IAAF,EAAQC,GAAR,CAAY1B,KAAK2B,cAAjB,EAAiC,YAAM;eAC5B,IAAT;KADF;eAIW,YAAM;UACX,CAACF,MAAL,EAAa;aACNG,oBAAL;;KAFJ,EAIGJ,QAJH;WAMO,IAAP;;;WAGOK,uBAAT,GAAmC;iBACpBT,mBAAb;SAEEU,EAAF,CAAKC,oBAAL,GAA4BR,qBAA5B;;QAEIvB,KAAKgC,qBAAL,EAAJ,EAAkC;WAC9BpB,KAAF,CAAQqB,OAAR,CAAgBjC,KAAK2B,cAArB,IAAuCjB,8BAAvC;;;;WAIKwB,QAAT,CAAkBC,QAAlB,EAA4B;;;eAGf,OAAOlC,KAAEmC,cAAT,KAA4B,UAA5B,GAAyCnC,KAAEmC,cAAF,CAAiBD,QAAjB,EAA2BE,MAA3B,CAAkC,CAAlC,CAAzC,GACPF,SAASG,OAAT,CAAiB,qBAAjB,EAAwC,MAAxC,CADJ;WAGOH,QAAP;;;;;;;;;MASInC,OAAO;oBAEK,iBAFL;UAAA,kBAIJuC,MAJI,EAII;SACV;;kBAES,CAAC,EAAEC,KAAKC,MAAL,KAAgBtC,OAAlB,CAAX,CAFC;OAAH,QAGSuC,SAASC,cAAT,CAAwBJ,MAAxB,CAHT;;aAIOA,MAAP;KATS;0BAAA,kCAYYK,OAZZ,EAYqB;UAC1BT,WAAWS,QAAQC,YAAR,CAAqB,aAArB,CAAf;;UACI,CAACV,QAAD,IAAaA,aAAa,GAA9B,EAAmC;mBACtBS,QAAQC,YAAR,CAAqB,MAArB,KAAgC,EAA3C;OAH4B;;;UAO1BV,SAASW,MAAT,CAAgB,CAAhB,MAAuB,GAA3B,EAAgC;mBACnBZ,SAASC,QAAT,CAAX;;;UAGE;YACIY,YAAY9C,KAAEyC,QAAF,EAAYM,IAAZ,CAAiBb,QAAjB,CAAlB;eACOY,UAAUE,MAAV,GAAmB,CAAnB,GAAuBd,QAAvB,GAAkC,IAAzC;OAFF,CAGE,OAAOe,GAAP,EAAY;eACL,IAAP;;KA3BO;UAAA,kBA+BJN,OA/BI,EA+BK;aACPA,QAAQO,YAAf;KAhCS;wBAAA,gCAmCUP,OAnCV,EAmCmB;WAC1BA,OAAF,EAAWQ,OAAX,CAAmBlD,WAAWS,GAA9B;KApCS;yBAAA,mCAuCa;aACf0C,QAAQnD,UAAR,CAAP;KAxCS;aAAA,qBA2CDG,GA3CC,EA2CI;aACN,CAACA,IAAI,CAAJ,KAAUA,GAAX,EAAgBiD,QAAvB;KA5CS;mBAAA,2BA+CKC,aA/CL,EA+CoBC,MA/CpB,EA+C4BC,WA/C5B,EA+CyC;WAC7C,IAAMC,QAAX,IAAuBD,WAAvB,EAAoC;YAC9BE,OAAOC,SAAP,CAAiBC,cAAjB,CAAgCtD,IAAhC,CAAqCkD,WAArC,EAAkDC,QAAlD,CAAJ,EAAiE;cACzDI,gBAAgBL,YAAYC,QAAZ,CAAtB;cACMK,QAAgBP,OAAOE,QAAP,CAAtB;cACMM,YAAgBD,SAAS/D,KAAKiE,SAAL,CAAeF,KAAf,CAAT,GAClB,SADkB,GACN3D,OAAO2D,KAAP,CADhB;;cAGI,CAAC,IAAIG,MAAJ,CAAWJ,aAAX,EAA0BK,IAA1B,CAA+BH,SAA/B,CAAL,EAAgD;kBACxC,IAAII,KAAJ,CACDb,cAAcc,WAAd,EAAH,yBACWX,QADX,2BACuCM,SADvC,sCAEsBF,aAFtB,SADI,CAAN;;;;;GAxDV;;SAoEO9D,IAAP;CApJW,CAqJVC,CArJU,CAAb;;ACNA;;;;;;;AAOA,IAAMqE,QAAS,UAACrE,IAAD,EAAO;;;;;;MAOdsE,OAAsB,OAA5B;MACMC,UAAsB,OAA5B;MACMC,WAAsB,UAA5B;MACMC,kBAA0BD,QAAhC;MACME,eAAsB,WAA5B;MACMC,qBAAsB3E,KAAE6B,EAAF,CAAKyC,IAAL,CAA5B;MACMM,sBAAsB,GAA5B;MAEMC,WAAW;aACL;GADZ;MAIMC,QAAQ;qBACaL,SADb;uBAEcA,SAFd;8BAGaA,SAAzB,GAAqCC;GAHvC;MAMMK,YAAY;WACR,OADQ;UAER,MAFQ;UAGR;;;;;;;GAHV;;MAYMV,KArCc;;;mBAsCN1B,OAAZ,EAAqB;WACdqC,QAAL,GAAgBrC,OAAhB;KAvCgB;;;;;;WAkDlBsC,KAlDkB,kBAkDZtC,OAlDY,EAkDH;gBACHA,WAAW,KAAKqC,QAA1B;;UAEME,cAAc,KAAKC,eAAL,CAAqBxC,OAArB,CAApB;;UACMyC,cAAc,KAAKC,kBAAL,CAAwBH,WAAxB,CAApB;;UAEIE,YAAYE,kBAAZ,EAAJ,EAAsC;;;;WAIjCC,cAAL,CAAoBL,WAApB;KA5DgB;;WA+DlBM,OA/DkB,sBA+DR;WACNC,UAAF,CAAa,KAAKT,QAAlB,EAA4BR,QAA5B;WACKQ,QAAL,GAAgB,IAAhB;KAjEgB;;;WAsElBG,eAtEkB,4BAsEFxC,OAtEE,EAsEO;UACjBT,WAAWnC,KAAK2F,sBAAL,CAA4B/C,OAA5B,CAAjB;UACIgD,SAAa,KAAjB;;UAEIzD,QAAJ,EAAc;iBACHlC,KAAEkC,QAAF,EAAY,CAAZ,CAAT;;;UAGE,CAACyD,MAAL,EAAa;iBACF3F,KAAE2C,OAAF,EAAWiD,OAAX,OAAuBb,UAAUc,KAAjC,EAA0C,CAA1C,CAAT;;;aAGKF,MAAP;KAlFgB;;WAqFlBN,kBArFkB,+BAqFC1C,OArFD,EAqFU;UACpBmD,aAAa9F,KAAE8E,KAAF,CAAQA,MAAMiB,KAAd,CAAnB;WAEEpD,OAAF,EAAWQ,OAAX,CAAmB2C,UAAnB;aACOA,UAAP;KAzFgB;;WA4FlBP,cA5FkB,2BA4FH5C,OA5FG,EA4FM;;;WACpBA,OAAF,EAAWqD,WAAX,CAAuBjB,UAAUkB,IAAjC;;UAEI,CAAClG,KAAKgC,qBAAL,EAAD,IACA,CAAC/B,KAAE2C,OAAF,EAAWuD,QAAX,CAAoBnB,UAAUoB,IAA9B,CADL,EAC0C;aACnCC,eAAL,CAAqBzD,OAArB;;;;;WAIAA,OAAF,EACGlB,GADH,CACO1B,KAAK2B,cADZ,EAC4B,UAACf,KAAD;eAAW,MAAKyF,eAAL,CAAqBzD,OAArB,EAA8BhC,KAA9B,CAAX;OAD5B,EAEGmB,oBAFH,CAEwB8C,mBAFxB;KArGgB;;WA0GlBwB,eA1GkB,4BA0GFzD,OA1GE,EA0GO;WACrBA,OAAF,EACG0D,MADH,GAEGlD,OAFH,CAEW2B,MAAMwB,MAFjB,EAGGC,MAHH;KA3GgB;;;UAmHXC,gBAnHW,6BAmHMjD,MAnHN,EAmHc;aACvB,KAAKkD,IAAL,CAAU,YAAY;YACrBC,WAAW1G,KAAE,IAAF,CAAjB;YACI2G,OAAaD,SAASC,IAAT,CAAcnC,QAAd,CAAjB;;YAEI,CAACmC,IAAL,EAAW;iBACF,IAAItC,KAAJ,CAAU,IAAV,CAAP;mBACSsC,IAAT,CAAcnC,QAAd,EAAwBmC,IAAxB;;;YAGEpD,WAAW,OAAf,EAAwB;eACjBA,MAAL,EAAa,IAAb;;OAVG,CAAP;KApHgB;;UAmIXqD,cAnIW,2BAmIIC,aAnIJ,EAmImB;aAC5B,UAAUlG,KAAV,EAAiB;YAClBA,KAAJ,EAAW;gBACHmG,cAAN;;;sBAGY7B,KAAd,CAAoB,IAApB;OALF;KApIgB;;;;0BA4CG;eACZV,OAAP;;;;;;;;;;;;OAuGF9B,QAAF,EAAYsE,EAAZ,CACEjC,MAAMkC,cADR,EAEEnC,SAASoC,OAFX,EAGE5C,MAAMuC,cAAN,CAAqB,IAAIvC,KAAJ,EAArB,CAHF;;;;;;;OAYExC,EAAF,CAAKyC,IAAL,IAAyBD,MAAMmC,gBAA/B;OACE3E,EAAF,CAAKyC,IAAL,EAAW4C,WAAX,GAAyB7C,KAAzB;;OACExC,EAAF,CAAKyC,IAAL,EAAW6C,UAAX,GAAyB,YAAY;SACjCtF,EAAF,CAAKyC,IAAL,IAAaK,kBAAb;WACON,MAAMmC,gBAAb;GAFF;;SAKOnC,KAAP;CAvKY,CAwKXrE,CAxKW,CAAd;;ACRA;;;;;;;AAOA,IAAMoH,SAAU,UAACpH,IAAD,EAAO;;;;;;MAOfsE,OAAsB,QAA5B;MACMC,UAAsB,OAA5B;MACMC,WAAsB,WAA5B;MACMC,kBAA0BD,QAAhC;MACME,eAAsB,WAA5B;MACMC,qBAAsB3E,KAAE6B,EAAF,CAAKyC,IAAL,CAA5B;MAEMS,YAAY;YACP,QADO;YAEP,KAFO;WAGP;GAHX;MAMMF,WAAW;wBACM,yBADN;iBAEM,yBAFN;WAGM,OAHN;YAIM,SAJN;YAKM;GALvB;MAQMC,QAAQ;8BACkBL,SAA9B,GAA0CC,YAD9B;yBAEU,UAAQD,SAAR,GAAoBC,YAApB,mBACSD,SADT,GACqBC,YADrB;;;;;;;GAFxB;;MAYM0C,MAxCe;;;oBAyCPzE,OAAZ,EAAqB;WACdqC,QAAL,GAAgBrC,OAAhB;KA1CiB;;;;;;WAqDnB0E,MArDmB,qBAqDV;UACHC,qBAAqB,IAAzB;UACIC,iBAAiB,IAArB;UACMrC,cAAclF,KAAE,KAAKgF,QAAP,EAAiBY,OAAjB,CAClBf,SAAS2C,WADS,EAElB,CAFkB,CAApB;;UAIItC,WAAJ,EAAiB;YACTuC,QAAQzH,KAAE,KAAKgF,QAAP,EAAiBjC,IAAjB,CAAsB8B,SAAS6C,KAA/B,EAAsC,CAAtC,CAAd;;YAEID,KAAJ,EAAW;cACLA,MAAME,IAAN,KAAe,OAAnB,EAA4B;gBACtBF,MAAMG,OAAN,IACF5H,KAAE,KAAKgF,QAAP,EAAiBkB,QAAjB,CAA0BnB,UAAU8C,MAApC,CADF,EAC+C;mCACxB,KAArB;aAFF,MAGO;kBACCC,gBAAgB9H,KAAEkF,WAAF,EAAenC,IAAf,CAAoB8B,SAASgD,MAA7B,EAAqC,CAArC,CAAtB;;kBAEIC,aAAJ,EAAmB;qBACfA,aAAF,EAAiB9B,WAAjB,CAA6BjB,UAAU8C,MAAvC;;;;;cAKFP,kBAAJ,EAAwB;gBAClBG,MAAMM,YAAN,CAAmB,UAAnB,KACF7C,YAAY6C,YAAZ,CAAyB,UAAzB,CADE,IAEFN,MAAMO,SAAN,CAAgBC,QAAhB,CAAyB,UAAzB,CAFE,IAGF/C,YAAY8C,SAAZ,CAAsBC,QAAtB,CAA+B,UAA/B,CAHF,EAG8C;;;;kBAGxCL,OAAN,GAAgB,CAAC5H,KAAE,KAAKgF,QAAP,EAAiBkB,QAAjB,CAA0BnB,UAAU8C,MAApC,CAAjB;iBACEJ,KAAF,EAAStE,OAAT,CAAiB,QAAjB;;;gBAGI+E,KAAN;2BACiB,KAAjB;;;;UAIAX,cAAJ,EAAoB;aACbvC,QAAL,CAAcmD,YAAd,CAA2B,cAA3B,EACE,CAACnI,KAAE,KAAKgF,QAAP,EAAiBkB,QAAjB,CAA0BnB,UAAU8C,MAApC,CADH;;;UAIEP,kBAAJ,EAAwB;aACpB,KAAKtC,QAAP,EAAiBoD,WAAjB,CAA6BrD,UAAU8C,MAAvC;;KAnGe;;WAuGnBrC,OAvGmB,sBAuGT;WACNC,UAAF,CAAa,KAAKT,QAAlB,EAA4BR,QAA5B;WACKQ,QAAL,GAAgB,IAAhB;KAzGiB;;;WA8GZwB,gBA9GY,6BA8GKjD,MA9GL,EA8Ga;aACvB,KAAKkD,IAAL,CAAU,YAAY;YACvBE,OAAO3G,KAAE,IAAF,EAAQ2G,IAAR,CAAanC,QAAb,CAAX;;YAEI,CAACmC,IAAL,EAAW;iBACF,IAAIS,MAAJ,CAAW,IAAX,CAAP;eACE,IAAF,EAAQT,IAAR,CAAanC,QAAb,EAAuBmC,IAAvB;;;YAGEpD,WAAW,QAAf,EAAyB;eAClBA,MAAL;;OATG,CAAP;KA/GiB;;;;0BA+CE;eACZgB,OAAP;;;;;;;;;;;;OAoFF9B,QAAF,EACGsE,EADH,CACMjC,MAAMkC,cADZ,EAC4BnC,SAASwD,kBADrC,EACyD,UAAC1H,KAAD,EAAW;UAC1DmG,cAAN;QAEIwB,SAAS3H,MAAMC,MAAnB;;QAEI,CAACZ,KAAEsI,MAAF,EAAUpC,QAAV,CAAmBnB,UAAUwD,MAA7B,CAAL,EAA2C;eAChCvI,KAAEsI,MAAF,EAAU1C,OAAV,CAAkBf,SAAS0D,MAA3B,CAAT;;;WAGK/B,gBAAP,CAAwBlG,IAAxB,CAA6BN,KAAEsI,MAAF,CAA7B,EAAwC,QAAxC;GAVJ,EAYGvB,EAZH,CAYMjC,MAAM0D,mBAZZ,EAYiC3D,SAASwD,kBAZ1C,EAY8D,UAAC1H,KAAD,EAAW;QAC/D2H,SAAStI,KAAEW,MAAMC,MAAR,EAAgBgF,OAAhB,CAAwBf,SAAS0D,MAAjC,EAAyC,CAAzC,CAAf;SACED,MAAF,EAAUF,WAAV,CAAsBrD,UAAU0D,KAAhC,EAAuC,eAAevE,IAAf,CAAoBvD,MAAMgH,IAA1B,CAAvC;GAdJ;;;;;;;OAuBE9F,EAAF,CAAKyC,IAAL,IAAa8C,OAAOZ,gBAApB;OACE3E,EAAF,CAAKyC,IAAL,EAAW4C,WAAX,GAAyBE,MAAzB;;OACEvF,EAAF,CAAKyC,IAAL,EAAW6C,UAAX,GAAwB,YAAY;SAChCtF,EAAF,CAAKyC,IAAL,IAAaK,kBAAb;WACOyC,OAAOZ,gBAAd;GAFF;;SAKOY,MAAP;CAlKa,CAmKZpH,CAnKY,CAAf;;ACNA;;;;;;;AAOA,IAAM0I,WAAY,UAAC1I,IAAD,EAAO;;;;;;MAOjBsE,OAAyB,UAA/B;MACMC,UAAyB,OAA/B;MACMC,WAAyB,aAA/B;MACMC,kBAA6BD,QAAnC;MACME,eAAyB,WAA/B;MACMC,qBAAyB3E,KAAE6B,EAAF,CAAKyC,IAAL,CAA/B;MACMM,sBAAyB,GAA/B;MACM+D,qBAAyB,EAA/B,CAduB;;MAejBC,sBAAyB,EAA/B,CAfuB;;MAgBjBC,yBAAyB,GAA/B,CAhBuB;;MAkBjBC,UAAU;cACH,IADG;cAEH,IAFG;WAGH,KAHG;WAIH,OAJG;UAKH;GALb;MAQMC,cAAc;cACP,kBADO;cAEP,SAFO;WAGP,kBAHO;WAIP,kBAJO;UAKP;GALb;MAQMC,YAAY;UACL,MADK;UAEL,MAFK;UAGL,MAHK;WAIL;GAJb;MAOMlE,QAAQ;qBACaL,SADb;mBAEYA,SAFZ;yBAGeA,SAHf;+BAIkBA,SAJlB;+BAKkBA,SALlB;2BAMgBA,SANhB;4BAOYA,SAAxB,GAAoCC,YAPxB;8BAQaD,SAAzB,GAAqCC;GARvC;MAWMK,YAAY;cACL,UADK;YAEL,QAFK;WAGL,OAHK;WAIL,qBAJK;UAKL,oBALK;UAML,oBANK;UAOL,oBAPK;UAQL;GARb;MAWMF,WAAW;YACD,SADC;iBAED,uBAFC;UAGD,gBAHC;eAID,0CAJC;gBAKD,sBALC;gBAMD,+BANC;eAOD;;;;;;;GAPhB;;MAgBM6D,QA/EiB;;;sBAgFT/F,OAAZ,EAAqBY,MAArB,EAA6B;WACtB0F,MAAL,GAA0B,IAA1B;WACKC,SAAL,GAA0B,IAA1B;WACKC,cAAL,GAA0B,IAA1B;WAEKC,SAAL,GAA0B,KAA1B;WACKC,UAAL,GAA0B,KAA1B;WAEKC,YAAL,GAA0B,IAA1B;WAEKC,OAAL,GAA0B,KAAKC,UAAL,CAAgBjG,MAAhB,CAA1B;WACKyB,QAAL,GAA0BhF,KAAE2C,OAAF,EAAW,CAAX,CAA1B;WACK8G,kBAAL,GAA0BzJ,KAAE,KAAKgF,QAAP,EAAiBjC,IAAjB,CAAsB8B,SAAS6E,UAA/B,EAA2C,CAA3C,CAA1B;;WAEKC,kBAAL;KA9FmB;;;;;;WA6GrBC,IA7GqB,mBA6Gd;UACD,CAAC,KAAKP,UAAV,EAAsB;aACfQ,MAAL,CAAYb,UAAUc,IAAtB;;KA/GiB;;WAmHrBC,eAnHqB,8BAmHH;;;UAGZ,CAACtH,SAASuH,MAAV,IACDhK,KAAE,KAAKgF,QAAP,EAAiBnE,EAAjB,CAAoB,UAApB,KAAmCb,KAAE,KAAKgF,QAAP,EAAiBiF,GAAjB,CAAqB,YAArB,MAAuC,QAD7E,EACwF;aACjFL,IAAL;;KAxHiB;;WA4HrBM,IA5HqB,mBA4Hd;UACD,CAAC,KAAKb,UAAV,EAAsB;aACfQ,MAAL,CAAYb,UAAUmB,IAAtB;;KA9HiB;;WAkIrBC,KAlIqB,kBAkIfzJ,KAlIe,EAkIR;UACP,CAACA,KAAL,EAAY;aACLyI,SAAL,GAAiB,IAAjB;;;UAGEpJ,KAAE,KAAKgF,QAAP,EAAiBjC,IAAjB,CAAsB8B,SAASwF,SAA/B,EAA0C,CAA1C,KACFtK,KAAKgC,qBAAL,EADF,EACgC;aACzBJ,oBAAL,CAA0B,KAAKqD,QAA/B;aACKsF,KAAL,CAAW,IAAX;;;oBAGY,KAAKpB,SAAnB;WACKA,SAAL,GAAiB,IAAjB;KA9ImB;;WAiJrBoB,KAjJqB,kBAiJf3J,KAjJe,EAiJR;UACP,CAACA,KAAL,EAAY;aACLyI,SAAL,GAAiB,KAAjB;;;UAGE,KAAKF,SAAT,EAAoB;sBACJ,KAAKA,SAAnB;aACKA,SAAL,GAAiB,IAAjB;;;UAGE,KAAKK,OAAL,CAAagB,QAAb,IAAyB,CAAC,KAAKnB,SAAnC,EAA8C;aACvCF,SAAL,GAAiBsB,YACf,CAAC/H,SAASgI,eAAT,GAA2B,KAAKV,eAAhC,GAAkD,KAAKH,IAAxD,EAA8Dc,IAA9D,CAAmE,IAAnE,CADe,EAEf,KAAKnB,OAAL,CAAagB,QAFE,CAAjB;;KA5JiB;;WAmKrBI,EAnKqB,eAmKlBC,KAnKkB,EAmKX;;;WACHzB,cAAL,GAAsBnJ,KAAE,KAAKgF,QAAP,EAAiBjC,IAAjB,CAAsB8B,SAASgG,WAA/B,EAA4C,CAA5C,CAAtB;;UAEMC,cAAc,KAAKC,aAAL,CAAmB,KAAK5B,cAAxB,CAApB;;UAEIyB,QAAQ,KAAK3B,MAAL,CAAYjG,MAAZ,GAAqB,CAA7B,IAAkC4H,QAAQ,CAA9C,EAAiD;;;;UAI7C,KAAKvB,UAAT,EAAqB;aACjB,KAAKrE,QAAP,EAAiBvD,GAAjB,CAAqBqD,MAAMkG,IAA3B,EAAiC;iBAAM,MAAKL,EAAL,CAAQC,KAAR,CAAN;SAAjC;;;;UAIEE,gBAAgBF,KAApB,EAA2B;aACpBR,KAAL;aACKE,KAAL;;;;UAIIW,YAAYL,QAAQE,WAAR,GACd9B,UAAUc,IADI,GAEdd,UAAUmB,IAFd;;WAIKN,MAAL,CAAYoB,SAAZ,EAAuB,KAAKhC,MAAL,CAAY2B,KAAZ,CAAvB;KA3LmB;;WA8LrBpF,OA9LqB,sBA8LX;WACN,KAAKR,QAAP,EAAiBkG,GAAjB,CAAqBzG,SAArB;WACEgB,UAAF,CAAa,KAAKT,QAAlB,EAA4BR,QAA5B;WAEKyE,MAAL,GAA0B,IAA1B;WACKM,OAAL,GAA0B,IAA1B;WACKvE,QAAL,GAA0B,IAA1B;WACKkE,SAAL,GAA0B,IAA1B;WACKE,SAAL,GAA0B,IAA1B;WACKC,UAAL,GAA0B,IAA1B;WACKF,cAAL,GAA0B,IAA1B;WACKM,kBAAL,GAA0B,IAA1B;KAzMmB;;;WA8MrBD,UA9MqB,uBA8MVjG,MA9MU,EA8MF;4BAEZuF,OADL,EAEKvF,MAFL;WAIK4H,eAAL,CAAqB7G,IAArB,EAA2Bf,MAA3B,EAAmCwF,WAAnC;aACOxF,MAAP;KApNmB;;WAuNrBoG,kBAvNqB,iCAuNA;;;UACf,KAAKJ,OAAL,CAAa6B,QAAjB,EAA2B;aACvB,KAAKpG,QAAP,EACG+B,EADH,CACMjC,MAAMuG,OADZ,EACqB,UAAC1K,KAAD;iBAAW,OAAK2K,QAAL,CAAc3K,KAAd,CAAX;SADrB;;;UAIE,KAAK4I,OAAL,CAAaa,KAAb,KAAuB,OAA3B,EAAoC;aAChC,KAAKpF,QAAP,EACG+B,EADH,CACMjC,MAAMyG,UADZ,EACwB,UAAC5K,KAAD;iBAAW,OAAKyJ,KAAL,CAAWzJ,KAAX,CAAX;SADxB,EAEGoG,EAFH,CAEMjC,MAAM0G,UAFZ,EAEwB,UAAC7K,KAAD;iBAAW,OAAK2J,KAAL,CAAW3J,KAAX,CAAX;SAFxB;;YAGI,kBAAkB8B,SAASgJ,eAA/B,EAAgD;;;;;;;;eAQ5C,KAAKzG,QAAP,EAAiB+B,EAAjB,CAAoBjC,MAAM4G,QAA1B,EAAoC,YAAM;mBACnCtB,KAAL;;gBACI,OAAKd,YAAT,EAAuB;2BACR,OAAKA,YAAlB;;;mBAEGA,YAAL,GAAoBqC,WAAW,UAAChL,KAAD;qBAAW,OAAK2J,KAAL,CAAW3J,KAAX,CAAX;aAAX,EAAyCkI,yBAAyB,OAAKU,OAAL,CAAagB,QAA/E,CAApB;WALF;;;KAzOe;;WAoPrBe,QApPqB,qBAoPZ3K,KApPY,EAoPL;UACV,kBAAkBuD,IAAlB,CAAuBvD,MAAMC,MAAN,CAAagL,OAApC,CAAJ,EAAkD;;;;cAI1CjL,MAAMkL,KAAd;aACOlD,kBAAL;gBACQ7B,cAAN;eACKoD,IAAL;;;aAEGtB,mBAAL;gBACQ9B,cAAN;eACK8C,IAAL;;;;;KAhQe;;WAsQrBmB,aAtQqB,0BAsQPpI,OAtQO,EAsQE;WAChBsG,MAAL,GAAcjJ,KAAE8L,SAAF,CAAY9L,KAAE2C,OAAF,EAAWgD,MAAX,GAAoB5C,IAApB,CAAyB8B,SAASkH,IAAlC,CAAZ,CAAd;aACO,KAAK9C,MAAL,CAAY+C,OAAZ,CAAoBrJ,OAApB,CAAP;KAxQmB;;WA2QrBsJ,mBA3QqB,gCA2QDhB,SA3QC,EA2QUnD,aA3QV,EA2QyB;UACtCoE,kBAAkBjB,cAAcjC,UAAUc,IAAhD;UACMqC,kBAAkBlB,cAAcjC,UAAUmB,IAAhD;;UACMW,cAAkB,KAAKC,aAAL,CAAmBjD,aAAnB,CAAxB;;UACMsE,gBAAkB,KAAKnD,MAAL,CAAYjG,MAAZ,GAAqB,CAA7C;UACMqJ,gBAAkBF,mBAAmBrB,gBAAgB,CAAnC,IACAoB,mBAAmBpB,gBAAgBsB,aAD3D;;UAGIC,iBAAiB,CAAC,KAAK9C,OAAL,CAAa+C,IAAnC,EAAyC;eAChCxE,aAAP;;;UAGIyE,QAAYtB,cAAcjC,UAAUmB,IAAxB,GAA+B,CAAC,CAAhC,GAAoC,CAAtD;UACMqC,YAAY,CAAC1B,cAAcyB,KAAf,IAAwB,KAAKtD,MAAL,CAAYjG,MAAtD;aAEOwJ,cAAc,CAAC,CAAf,GACH,KAAKvD,MAAL,CAAY,KAAKA,MAAL,CAAYjG,MAAZ,GAAqB,CAAjC,CADG,GACmC,KAAKiG,MAAL,CAAYuD,SAAZ,CAD1C;KA1RmB;;WA8RrBC,kBA9RqB,+BA8RFC,aA9RE,EA8RaC,kBA9Rb,EA8RiC;UAC9CC,cAAc,KAAK7B,aAAL,CAAmB2B,aAAnB,CAApB;;UACMG,YAAY,KAAK9B,aAAL,CAAmB/K,KAAE,KAAKgF,QAAP,EAAiBjC,IAAjB,CAAsB8B,SAASgG,WAA/B,EAA4C,CAA5C,CAAnB,CAAlB;;UACMiC,aAAa9M,KAAE8E,KAAF,CAAQA,MAAMiI,KAAd,EAAqB;oCAAA;mBAE3BJ,kBAF2B;cAGhCE,SAHgC;YAIlCD;OAJa,CAAnB;WAOE,KAAK5H,QAAP,EAAiB7B,OAAjB,CAAyB2J,UAAzB;aAEOA,UAAP;KA1SmB;;WA6SrBE,0BA7SqB,uCA6SMrK,OA7SN,EA6Se;UAC9B,KAAK8G,kBAAT,EAA6B;aACzB,KAAKA,kBAAP,EACG1G,IADH,CACQ8B,SAASgD,MADjB,EAEG7B,WAFH,CAEejB,UAAU8C,MAFzB;;YAIMoF,gBAAgB,KAAKxD,kBAAL,CAAwByD,QAAxB,CACpB,KAAKnC,aAAL,CAAmBpI,OAAnB,CADoB,CAAtB;;YAIIsK,aAAJ,EAAmB;eACfA,aAAF,EAAiBE,QAAjB,CAA0BpI,UAAU8C,MAApC;;;KAxTe;;WA6TrBgC,MA7TqB,mBA6TdoB,SA7Tc,EA6THtI,OA7TG,EA6TM;;;UACnBmF,gBAAgB9H,KAAE,KAAKgF,QAAP,EAAiBjC,IAAjB,CAAsB8B,SAASgG,WAA/B,EAA4C,CAA5C,CAAtB;;UACMuC,qBAAqB,KAAKrC,aAAL,CAAmBjD,aAAnB,CAA3B;;UACMuF,cAAgB1K,WAAWmF,iBAC/B,KAAKmE,mBAAL,CAAyBhB,SAAzB,EAAoCnD,aAApC,CADF;;UAEMwF,mBAAmB,KAAKvC,aAAL,CAAmBsC,WAAnB,CAAzB;;UACME,YAAYnK,QAAQ,KAAK8F,SAAb,CAAlB;UAEIsE,oBAAJ;UACIC,cAAJ;UACId,kBAAJ;;UAEI1B,cAAcjC,UAAUc,IAA5B,EAAkC;+BACT/E,UAAU2I,IAAjC;yBACiB3I,UAAU+E,IAA3B;6BACqBd,UAAU0E,IAA/B;OAHF,MAIO;+BACkB3I,UAAU4I,KAAjC;yBACiB5I,UAAUoF,IAA3B;6BACqBnB,UAAU2E,KAA/B;;;UAGEN,eAAerN,KAAEqN,WAAF,EAAenH,QAAf,CAAwBnB,UAAU8C,MAAlC,CAAnB,EAA8D;aACvDwB,UAAL,GAAkB,KAAlB;;;;UAIIyD,aAAa,KAAKL,kBAAL,CAAwBY,WAAxB,EAAqCV,kBAArC,CAAnB;;UACIG,WAAWxH,kBAAX,EAAJ,EAAqC;;;;UAIjC,CAACwC,aAAD,IAAkB,CAACuF,WAAvB,EAAoC;;;;;WAK/BhE,UAAL,GAAkB,IAAlB;;UAEIkE,SAAJ,EAAe;aACRnD,KAAL;;;WAGG4C,0BAAL,CAAgCK,WAAhC;;UAEMO,YAAY5N,KAAE8E,KAAF,CAAQA,MAAMkG,IAAd,EAAoB;uBACrBqC,WADqB;mBAEzBV,kBAFyB;cAG9BS,kBAH8B;YAIhCE;OAJY,CAAlB;;UAOIvN,KAAKgC,qBAAL,MACF/B,KAAE,KAAKgF,QAAP,EAAiBkB,QAAjB,CAA0BnB,UAAUgI,KAApC,CADF,EAC8C;aAC1CM,WAAF,EAAeF,QAAf,CAAwBM,cAAxB;aAEKI,MAAL,CAAYR,WAAZ;aAEEvF,aAAF,EAAiBqF,QAAjB,CAA0BK,oBAA1B;aACEH,WAAF,EAAeF,QAAf,CAAwBK,oBAAxB;aAEE1F,aAAF,EACGrG,GADH,CACO1B,KAAK2B,cADZ,EAC4B,YAAM;eAC5B2L,WAAF,EACGrH,WADH,CACkBwH,oBADlB,SAC0CC,cAD1C,EAEGN,QAFH,CAEYpI,UAAU8C,MAFtB;eAIEC,aAAF,EAAiB9B,WAAjB,CAAgCjB,UAAU8C,MAA1C,SAAoD4F,cAApD,SAAsED,oBAAtE;iBAEKnE,UAAL,GAAkB,KAAlB;qBAEW;mBAAMrJ,KAAE,OAAKgF,QAAP,EAAiB7B,OAAjB,CAAyByK,SAAzB,CAAN;WAAX,EAAsD,CAAtD;SAVJ,EAYG9L,oBAZH,CAYwB8C,mBAZxB;OATF,MAsBO;aACHkD,aAAF,EAAiB9B,WAAjB,CAA6BjB,UAAU8C,MAAvC;aACEwF,WAAF,EAAeF,QAAf,CAAwBpI,UAAU8C,MAAlC;aAEKwB,UAAL,GAAkB,KAAlB;aACE,KAAKrE,QAAP,EAAiB7B,OAAjB,CAAyByK,SAAzB;;;UAGEL,SAAJ,EAAe;aACRjD,KAAL;;KAhZiB;;;aAsZd9D,gBAtZc,6BAsZGjD,MAtZH,EAsZW;aACvB,KAAKkD,IAAL,CAAU,YAAY;YACvBE,OAAO3G,KAAE,IAAF,EAAQ2G,IAAR,CAAanC,QAAb,CAAX;;YACI+E,uBACCT,OADD,EAEC9I,KAAE,IAAF,EAAQ2G,IAAR,EAFD,CAAJ;;YAKI,OAAOpD,MAAP,KAAkB,QAAtB,EAAgC;iCAEzBgG,OADL,EAEKhG,MAFL;;;YAMIuK,SAAS,OAAOvK,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsCgG,QAAQwE,KAA7D;;YAEI,CAACpH,IAAL,EAAW;iBACF,IAAI+B,QAAJ,CAAa,IAAb,EAAmBa,OAAnB,CAAP;eACE,IAAF,EAAQ5C,IAAR,CAAanC,QAAb,EAAuBmC,IAAvB;;;YAGE,OAAOpD,MAAP,KAAkB,QAAtB,EAAgC;eACzBoH,EAAL,CAAQpH,MAAR;SADF,MAEO,IAAI,OAAOuK,MAAP,KAAkB,QAAtB,EAAgC;cACjC,OAAOnH,KAAKmH,MAAL,CAAP,KAAwB,WAA5B,EAAyC;kBACjC,IAAIE,SAAJ,wBAAkCF,MAAlC,QAAN;;;eAEGA,MAAL;SAJK,MAKA,IAAIvE,QAAQgB,QAAZ,EAAsB;eACtBH,KAAL;eACKE,KAAL;;OA9BG,CAAP;KAvZmB;;aA0bd2D,oBA1bc,iCA0bOtN,KA1bP,EA0bc;UAC3BuB,WAAWnC,KAAK2F,sBAAL,CAA4B,IAA5B,CAAjB;;UAEI,CAACxD,QAAL,EAAe;;;;UAITtB,SAASZ,KAAEkC,QAAF,EAAY,CAAZ,CAAf;;UAEI,CAACtB,MAAD,IAAW,CAACZ,KAAEY,MAAF,EAAUsF,QAAV,CAAmBnB,UAAUmJ,QAA7B,CAAhB,EAAwD;;;;UAIlD3K,sBACDvD,KAAEY,MAAF,EAAU+F,IAAV,EADC,EAED3G,KAAE,IAAF,EAAQ2G,IAAR,EAFC,CAAN;UAIMwH,aAAa,KAAKvL,YAAL,CAAkB,eAAlB,CAAnB;;UAEIuL,UAAJ,EAAgB;eACP5D,QAAP,GAAkB,KAAlB;;;eAGO/D,gBAAT,CAA0BlG,IAA1B,CAA+BN,KAAEY,MAAF,CAA/B,EAA0C2C,MAA1C;;UAEI4K,UAAJ,EAAgB;aACZvN,MAAF,EAAU+F,IAAV,CAAenC,QAAf,EAAyBmG,EAAzB,CAA4BwD,UAA5B;;;YAGIrH,cAAN;KAvdmB;;;;0BAmGA;eACZvC,OAAP;;;;0BAGmB;eACZuE,OAAP;;;;;;;;;;;;OAyXFrG,QAAF,EACGsE,EADH,CACMjC,MAAMkC,cADZ,EAC4BnC,SAASuJ,UADrC,EACiD1F,SAASuF,oBAD1D;OAGE7M,MAAF,EAAU2F,EAAV,CAAajC,MAAMuJ,aAAnB,EAAkC,YAAM;SACpCxJ,SAASyJ,SAAX,EAAsB7H,IAAtB,CAA2B,YAAY;UAC/B8H,YAAYvO,KAAE,IAAF,CAAlB;;eACSwG,gBAAT,CAA0BlG,IAA1B,CAA+BiO,SAA/B,EAA0CA,UAAU5H,IAAV,EAA1C;KAFF;GADF;;;;;;;OAaE9E,EAAF,CAAKyC,IAAL,IAAaoE,SAASlC,gBAAtB;OACE3E,EAAF,CAAKyC,IAAL,EAAW4C,WAAX,GAAyBwB,QAAzB;;OACE7G,EAAF,CAAKyC,IAAL,EAAW6C,UAAX,GAAwB,YAAY;SAChCtF,EAAF,CAAKyC,IAAL,IAAaK,kBAAb;WACO+D,SAASlC,gBAAhB;GAFF;;SAKOkC,QAAP;CAxfe,CAyfd1I,CAzfc,CAAjB;;ACPA;;;;;;;AAOA,IAAMwO,WAAY,UAACxO,IAAD,EAAO;;;;;;MAOjBsE,OAAsB,UAA5B;MACMC,UAAsB,OAA5B;MACMC,WAAsB,aAA5B;MACMC,kBAA0BD,QAAhC;MACME,eAAsB,WAA5B;MACMC,qBAAsB3E,KAAE6B,EAAF,CAAKyC,IAAL,CAA5B;MACMM,sBAAsB,GAA5B;MAEMkE,UAAU;YACL,IADK;YAEL;GAFX;MAKMC,cAAc;YACT,SADS;YAET;GAFX;MAKMjE,QAAQ;mBACYL,SADZ;qBAEaA,SAFb;mBAGYA,SAHZ;uBAIcA,SAJd;8BAKaA,SAAzB,GAAqCC;GALvC;MAQMK,YAAY;UACH,MADG;cAEH,UAFG;gBAGH,YAHG;eAIH;GAJf;MAOM0J,YAAY;WACP,OADO;YAEP;GAFX;MAKM5J,WAAW;aACD,oBADC;iBAED;;;;;;;GAFhB;;MAWM2J,QAxDiB;;;sBAyDT7L,OAAZ,EAAqBY,MAArB,EAA6B;WACtBmL,gBAAL,GAAwB,KAAxB;WACK1J,QAAL,GAAwBrC,OAAxB;WACK4G,OAAL,GAAwB,KAAKC,UAAL,CAAgBjG,MAAhB,CAAxB;WACKoL,aAAL,GAAwB3O,KAAE8L,SAAF,CAAY9L,KAClC,wCAAmC2C,QAAQiM,EAA3C,4DAC0CjM,QAAQiM,EADlD,SADkC,CAAZ,CAAxB;UAIMC,aAAa7O,KAAE6E,SAAS2C,WAAX,CAAnB;;WACK,IAAIsH,IAAI,CAAb,EAAgBA,IAAID,WAAW7L,MAA/B,EAAuC8L,GAAvC,EAA4C;YACpCC,OAAOF,WAAWC,CAAX,CAAb;YACM5M,WAAWnC,KAAK2F,sBAAL,CAA4BqJ,IAA5B,CAAjB;;YACI7M,aAAa,IAAb,IAAqBlC,KAAEkC,QAAF,EAAY8M,MAAZ,CAAmBrM,OAAnB,EAA4BK,MAA5B,GAAqC,CAA9D,EAAiE;eAC1DiM,SAAL,GAAiB/M,QAAjB;;eACKyM,aAAL,CAAmBO,IAAnB,CAAwBH,IAAxB;;;;WAICI,OAAL,GAAe,KAAK5F,OAAL,CAAa5D,MAAb,GAAsB,KAAKyJ,UAAL,EAAtB,GAA0C,IAAzD;;UAEI,CAAC,KAAK7F,OAAL,CAAa5D,MAAlB,EAA0B;aACnB0J,yBAAL,CAA+B,KAAKrK,QAApC,EAA8C,KAAK2J,aAAnD;;;UAGE,KAAKpF,OAAL,CAAalC,MAAjB,EAAyB;aAClBA,MAAL;;KAlFiB;;;;;;WAkGrBA,MAlGqB,qBAkGZ;UACHrH,KAAE,KAAKgF,QAAP,EAAiBkB,QAAjB,CAA0BnB,UAAUkB,IAApC,CAAJ,EAA+C;aACxCqJ,IAAL;OADF,MAEO;aACAC,IAAL;;KAtGiB;;WA0GrBA,IA1GqB,mBA0Gd;;;UACD,KAAKb,gBAAL,IACF1O,KAAE,KAAKgF,QAAP,EAAiBkB,QAAjB,CAA0BnB,UAAUkB,IAApC,CADF,EAC6C;;;;UAIzCuJ,OAAJ;UACIC,WAAJ;;UAEI,KAAKN,OAAT,EAAkB;kBACNnP,KAAE8L,SAAF,CACR9L,KAAE,KAAKmP,OAAP,EACGpM,IADH,CACQ8B,SAAS6K,OADjB,EAEGV,MAFH,qBAE2B,KAAKzF,OAAL,CAAa5D,MAFxC,SADQ,CAAV;;YAKI6J,QAAQxM,MAAR,KAAmB,CAAvB,EAA0B;oBACd,IAAV;;;;UAIAwM,OAAJ,EAAa;sBACGxP,KAAEwP,OAAF,EAAWG,GAAX,CAAe,KAAKV,SAApB,EAA+BtI,IAA/B,CAAoCnC,QAApC,CAAd;;YACIiL,eAAeA,YAAYf,gBAA/B,EAAiD;;;;;UAK7CkB,aAAa5P,KAAE8E,KAAF,CAAQA,MAAMmB,IAAd,CAAnB;WACE,KAAKjB,QAAP,EAAiB7B,OAAjB,CAAyByM,UAAzB;;UACIA,WAAWtK,kBAAX,EAAJ,EAAqC;;;;UAIjCkK,OAAJ,EAAa;iBACFhJ,gBAAT,CAA0BlG,IAA1B,CAA+BN,KAAEwP,OAAF,EAAWG,GAAX,CAAe,KAAKV,SAApB,CAA/B,EAA+D,MAA/D;;YACI,CAACQ,WAAL,EAAkB;eACdD,OAAF,EAAW7I,IAAX,CAAgBnC,QAAhB,EAA0B,IAA1B;;;;UAIEqL,YAAY,KAAKC,aAAL,EAAlB;;WAEE,KAAK9K,QAAP,EACGgB,WADH,CACejB,UAAUgL,QADzB,EAEG5C,QAFH,CAEYpI,UAAUiL,UAFtB;WAIKhL,QAAL,CAAciL,KAAd,CAAoBJ,SAApB,IAAiC,CAAjC;;UAEI,KAAKlB,aAAL,CAAmB3L,MAAnB,GAA4B,CAAhC,EAAmC;aAC/B,KAAK2L,aAAP,EACG3I,WADH,CACejB,UAAUmL,SADzB,EAEGC,IAFH,CAEQ,eAFR,EAEyB,IAFzB;;;WAKGC,gBAAL,CAAsB,IAAtB;;UAEMC,WAAW,SAAXA,QAAW,GAAM;aACnB,MAAKrL,QAAP,EACGgB,WADH,CACejB,UAAUiL,UADzB,EAEG7C,QAFH,CAEYpI,UAAUgL,QAFtB,EAGG5C,QAHH,CAGYpI,UAAUkB,IAHtB;cAKKjB,QAAL,CAAciL,KAAd,CAAoBJ,SAApB,IAAiC,EAAjC;;cAEKO,gBAAL,CAAsB,KAAtB;;aAEE,MAAKpL,QAAP,EAAiB7B,OAAjB,CAAyB2B,MAAMwL,KAA/B;OAVF;;UAaI,CAACvQ,KAAKgC,qBAAL,EAAL,EAAmC;;;;;UAK7BwO,uBAAuBV,UAAU,CAAV,EAAazL,WAAb,KAA6ByL,UAAUW,KAAV,CAAgB,CAAhB,CAA1D;UACMC,wBAAsBF,oBAA5B;WAEE,KAAKvL,QAAP,EACGvD,GADH,CACO1B,KAAK2B,cADZ,EAC4B2O,QAD5B,EAEGvO,oBAFH,CAEwB8C,mBAFxB;WAIKI,QAAL,CAAciL,KAAd,CAAoBJ,SAApB,IAAoC,KAAK7K,QAAL,CAAcyL,UAAd,CAApC;KA3LmB;;WA8LrBnB,IA9LqB,mBA8Ld;;;UACD,KAAKZ,gBAAL,IACF,CAAC1O,KAAE,KAAKgF,QAAP,EAAiBkB,QAAjB,CAA0BnB,UAAUkB,IAApC,CADH,EAC8C;;;;UAIxC2J,aAAa5P,KAAE8E,KAAF,CAAQA,MAAM4L,IAAd,CAAnB;WACE,KAAK1L,QAAP,EAAiB7B,OAAjB,CAAyByM,UAAzB;;UACIA,WAAWtK,kBAAX,EAAJ,EAAqC;;;;UAI/BuK,YAAY,KAAKC,aAAL,EAAlB;;WAEK9K,QAAL,CAAciL,KAAd,CAAoBJ,SAApB,IAAoC,KAAK7K,QAAL,CAAc2L,qBAAd,GAAsCd,SAAtC,CAApC;WAEKhC,MAAL,CAAY,KAAK7I,QAAjB;WAEE,KAAKA,QAAP,EACGmI,QADH,CACYpI,UAAUiL,UADtB,EAEGhK,WAFH,CAEejB,UAAUgL,QAFzB,EAGG/J,WAHH,CAGejB,UAAUkB,IAHzB;;UAKI,KAAK0I,aAAL,CAAmB3L,MAAnB,GAA4B,CAAhC,EAAmC;aAC5B,IAAI8L,IAAI,CAAb,EAAgBA,IAAI,KAAKH,aAAL,CAAmB3L,MAAvC,EAA+C8L,GAA/C,EAAoD;cAC5C3L,UAAU,KAAKwL,aAAL,CAAmBG,CAAnB,CAAhB;cACM5M,WAAWnC,KAAK2F,sBAAL,CAA4BvC,OAA5B,CAAjB;;cACIjB,aAAa,IAAjB,EAAuB;gBACf0O,QAAQ5Q,KAAEkC,QAAF,CAAd;;gBACI,CAAC0O,MAAM1K,QAAN,CAAenB,UAAUkB,IAAzB,CAAL,EAAqC;mBACjC9C,OAAF,EAAWgK,QAAX,CAAoBpI,UAAUmL,SAA9B,EACGC,IADH,CACQ,eADR,EACyB,KADzB;;;;;;WAOHC,gBAAL,CAAsB,IAAtB;;UAEMC,WAAW,SAAXA,QAAW,GAAM;eAChBD,gBAAL,CAAsB,KAAtB;;aACE,OAAKpL,QAAP,EACGgB,WADH,CACejB,UAAUiL,UADzB,EAEG7C,QAFH,CAEYpI,UAAUgL,QAFtB,EAGG5M,OAHH,CAGW2B,MAAM+L,MAHjB;OAFF;;WAQK7L,QAAL,CAAciL,KAAd,CAAoBJ,SAApB,IAAiC,EAAjC;;UAEI,CAAC9P,KAAKgC,qBAAL,EAAL,EAAmC;;;;;WAKjC,KAAKiD,QAAP,EACGvD,GADH,CACO1B,KAAK2B,cADZ,EAC4B2O,QAD5B,EAEGvO,oBAFH,CAEwB8C,mBAFxB;KApPmB;;WAyPrBwL,gBAzPqB,6BAyPJU,eAzPI,EAyPa;WAC3BpC,gBAAL,GAAwBoC,eAAxB;KA1PmB;;WA6PrBtL,OA7PqB,sBA6PX;WACNC,UAAF,CAAa,KAAKT,QAAlB,EAA4BR,QAA5B;WAEK+E,OAAL,GAAwB,IAAxB;WACK4F,OAAL,GAAwB,IAAxB;WACKnK,QAAL,GAAwB,IAAxB;WACK2J,aAAL,GAAwB,IAAxB;WACKD,gBAAL,GAAwB,IAAxB;KApQmB;;;WAyQrBlF,UAzQqB,uBAyQVjG,MAzQU,EAyQF;4BAEZuF,OADL,EAEKvF,MAFL;aAIO8D,MAAP,GAAgBjE,QAAQG,OAAO8D,MAAf,CAAhB,CALiB;;WAMZ8D,eAAL,CAAqB7G,IAArB,EAA2Bf,MAA3B,EAAmCwF,WAAnC;aACOxF,MAAP;KAhRmB;;WAmRrBuM,aAnRqB,4BAmRL;UACRiB,WAAW/Q,KAAE,KAAKgF,QAAP,EAAiBkB,QAAjB,CAA0BuI,UAAUuC,KAApC,CAAjB;aACOD,WAAWtC,UAAUuC,KAArB,GAA6BvC,UAAUwC,MAA9C;KArRmB;;WAwRrB7B,UAxRqB,yBAwRR;;;UACPzJ,SAAS,IAAb;;UACI5F,KAAKiE,SAAL,CAAe,KAAKuF,OAAL,CAAa5D,MAA5B,CAAJ,EAAyC;iBAC9B,KAAK4D,OAAL,CAAa5D,MAAtB,CADuC;;YAInC,OAAO,KAAK4D,OAAL,CAAa5D,MAAb,CAAoBuL,MAA3B,KAAsC,WAA1C,EAAuD;mBAC5C,KAAK3H,OAAL,CAAa5D,MAAb,CAAoB,CAApB,CAAT;;OALJ,MAOO;iBACI3F,KAAE,KAAKuJ,OAAL,CAAa5D,MAAf,EAAuB,CAAvB,CAAT;;;UAGIzD,yDACqC,KAAKqH,OAAL,CAAa5D,MADlD,QAAN;WAGEA,MAAF,EAAU5C,IAAV,CAAeb,QAAf,EAAyBuE,IAAzB,CAA8B,UAACqI,CAAD,EAAInM,OAAJ,EAAgB;eACvC0M,yBAAL,CACEb,SAAS2C,qBAAT,CAA+BxO,OAA/B,CADF,EAEE,CAACA,OAAD,CAFF;OADF;aAOOgD,MAAP;KA/SmB;;WAkTrB0J,yBAlTqB,sCAkTK1M,OAlTL,EAkTcyO,YAlTd,EAkT4B;UAC3CzO,OAAJ,EAAa;YACL0O,SAASrR,KAAE2C,OAAF,EAAWuD,QAAX,CAAoBnB,UAAUkB,IAA9B,CAAf;;YAEImL,aAAapO,MAAb,GAAsB,CAA1B,EAA6B;eACzBoO,YAAF,EACGhJ,WADH,CACerD,UAAUmL,SADzB,EACoC,CAACmB,MADrC,EAEGlB,IAFH,CAEQ,eAFR,EAEyBkB,MAFzB;;;KAvTe;;;aAgUdF,qBAhUc,kCAgUQxO,OAhUR,EAgUiB;UAC9BT,WAAWnC,KAAK2F,sBAAL,CAA4B/C,OAA5B,CAAjB;aACOT,WAAWlC,KAAEkC,QAAF,EAAY,CAAZ,CAAX,GAA4B,IAAnC;KAlUmB;;aAqUdsE,gBArUc,6BAqUGjD,MArUH,EAqUW;aACvB,KAAKkD,IAAL,CAAU,YAAY;YACrB6K,QAAUtR,KAAE,IAAF,CAAhB;YACI2G,OAAY2K,MAAM3K,IAAN,CAAWnC,QAAX,CAAhB;;YACM+E,uBACDT,OADC,EAEDwI,MAAM3K,IAAN,EAFC,EAGD,OAAOpD,MAAP,KAAkB,QAAlB,IAA8BA,MAH7B,CAAN;;YAMI,CAACoD,IAAD,IAAS4C,QAAQlC,MAAjB,IAA2B,YAAYnD,IAAZ,CAAiBX,MAAjB,CAA/B,EAAyD;kBAC/C8D,MAAR,GAAiB,KAAjB;;;YAGE,CAACV,IAAL,EAAW;iBACF,IAAI6H,QAAJ,CAAa,IAAb,EAAmBjF,OAAnB,CAAP;gBACM5C,IAAN,CAAWnC,QAAX,EAAqBmC,IAArB;;;YAGE,OAAOpD,MAAP,KAAkB,QAAtB,EAAgC;cAC1B,OAAOoD,KAAKpD,MAAL,CAAP,KAAwB,WAA5B,EAAyC;kBACjC,IAAIyK,SAAJ,wBAAkCzK,MAAlC,QAAN;;;eAEGA,MAAL;;OAtBG,CAAP;KAtUmB;;;;0BAwFA;eACZgB,OAAP;;;;0BAGmB;eACZuE,OAAP;;;;;;;;;;;;OA2QFrG,QAAF,EAAYsE,EAAZ,CAAejC,MAAMkC,cAArB,EAAqCnC,SAAS2C,WAA9C,EAA2D,UAAU7G,KAAV,EAAiB;;QAEtEA,MAAM4Q,aAAN,CAAoB3F,OAApB,KAAgC,GAApC,EAAyC;YACjC9E,cAAN;;;QAGI0K,WAAWxR,KAAE,IAAF,CAAjB;QACMkC,WAAWnC,KAAK2F,sBAAL,CAA4B,IAA5B,CAAjB;SACExD,QAAF,EAAYuE,IAAZ,CAAiB,YAAY;UACrBgL,UAAUzR,KAAE,IAAF,CAAhB;UACM2G,OAAU8K,QAAQ9K,IAAR,CAAanC,QAAb,CAAhB;UACMjB,SAAUoD,OAAO,QAAP,GAAkB6K,SAAS7K,IAAT,EAAlC;;eACSH,gBAAT,CAA0BlG,IAA1B,CAA+BmR,OAA/B,EAAwClO,MAAxC;KAJF;GARF;;;;;;;OAsBE1B,EAAF,CAAKyC,IAAL,IAAakK,SAAShI,gBAAtB;OACE3E,EAAF,CAAKyC,IAAL,EAAW4C,WAAX,GAAyBsH,QAAzB;;OACE3M,EAAF,CAAKyC,IAAL,EAAW6C,UAAX,GAAwB,YAAY;SAChCtF,EAAF,CAAKyC,IAAL,IAAaK,kBAAb;WACO6J,SAAShI,gBAAhB;GAFF;;SAKOgI,QAAP;CArYe,CAsYdxO,CAtYc,CAAjB;;ACVA;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,IAAI,SAAS,GAAG,OAAO,MAAM,KAAK,WAAW,IAAI,OAAO,QAAQ,KAAK,WAAW,CAAC;AACjF,IAAI,qBAAqB,GAAG,CAAC,MAAM,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;AAC3D,IAAI,eAAe,GAAG,CAAC,CAAC;AACxB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,qBAAqB,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;EACxD,IAAI,SAAS,IAAI,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;IAC3E,eAAe,GAAG,CAAC,CAAC;IACpB,MAAM;GACP;CACF;;AAED,SAAS,iBAAiB,CAAC,EAAE,EAAE;EAC7B,IAAI,MAAM,GAAG,KAAK,CAAC;EACnB,OAAO,YAAY;IACjB,IAAI,MAAM,EAAE;MACV,OAAO;KACR;IACD,MAAM,GAAG,IAAI,CAAC;IACd,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,YAAY;MACxC,MAAM,GAAG,KAAK,CAAC;MACf,EAAE,EAAE,CAAC;KACN,CAAC,CAAC;GACJ,CAAC;CACH;;AAED,SAAS,YAAY,CAAC,EAAE,EAAE;EACxB,IAAI,SAAS,GAAG,KAAK,CAAC;EACtB,OAAO,YAAY;IACjB,IAAI,CAAC,SAAS,EAAE;MACd,SAAS,GAAG,IAAI,CAAC;MACjB,UAAU,CAAC,YAAY;QACrB,SAAS,GAAG,KAAK,CAAC;QAClB,EAAE,EAAE,CAAC;OACN,EAAE,eAAe,CAAC,CAAC;KACrB;GACF,CAAC;CACH;;AAED,IAAI,kBAAkB,GAAG,SAAS,IAAI,MAAM,CAAC,OAAO,CAAC;;;;;;;;;;;AAWrD,IAAI,QAAQ,GAAG,kBAAkB,GAAG,iBAAiB,GAAG,YAAY,CAAC;;;;;;;;;AASrE,SAAS,UAAU,CAAC,eAAe,EAAE;EACnC,IAAI,OAAO,GAAG,EAAE,CAAC;EACjB,OAAO,eAAe,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,mBAAmB,CAAC;CAC1F;;;;;;;;;AASD,SAAS,wBAAwB,CAAC,OAAO,EAAE,QAAQ,EAAE;EACnD,IAAI,OAAO,CAAC,QAAQ,KAAK,CAAC,EAAE;IAC1B,OAAO,EAAE,CAAC;GACX;;EAED,IAAI,GAAG,GAAG,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;EAC1C,OAAO,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC,GAAG,GAAG,CAAC;CACvC;;;;;;;;;AASD,SAAS,aAAa,CAAC,OAAO,EAAE;EAC9B,IAAI,OAAO,CAAC,QAAQ,KAAK,MAAM,EAAE;IAC/B,OAAO,OAAO,CAAC;GAChB;EACD,OAAO,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;CAC3C;;;;;;;;;AASD,SAAS,eAAe,CAAC,OAAO,EAAE;;EAEhC,IAAI,CAAC,OAAO,EAAE;IACZ,OAAO,QAAQ,CAAC,IAAI,CAAC;GACtB;;EAED,QAAQ,OAAO,CAAC,QAAQ;IACtB,KAAK,MAAM,CAAC;IACZ,KAAK,MAAM;MACT,OAAO,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC;IACpC,KAAK,WAAW;MACd,OAAO,OAAO,CAAC,IAAI,CAAC;GACvB;;;;EAID,IAAI,qBAAqB,GAAG,wBAAwB,CAAC,OAAO,CAAC;MACzD,QAAQ,GAAG,qBAAqB,CAAC,QAAQ;MACzC,SAAS,GAAG,qBAAqB,CAAC,SAAS;MAC3C,SAAS,GAAG,qBAAqB,CAAC,SAAS,CAAC;;EAEhD,IAAI,eAAe,CAAC,IAAI,CAAC,QAAQ,GAAG,SAAS,GAAG,SAAS,CAAC,EAAE;IAC1D,OAAO,OAAO,CAAC;GAChB;;EAED,OAAO,eAAe,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC;CAChD;;;;;;;;;AASD,SAAS,eAAe,CAAC,OAAO,EAAE;;EAEhC,IAAI,YAAY,GAAG,OAAO,IAAI,OAAO,CAAC,YAAY,CAAC;EACnD,IAAI,QAAQ,GAAG,YAAY,IAAI,YAAY,CAAC,QAAQ,CAAC;;EAErD,IAAI,CAAC,QAAQ,IAAI,QAAQ,KAAK,MAAM,IAAI,QAAQ,KAAK,MAAM,EAAE;IAC3D,IAAI,OAAO,EAAE;MACX,OAAO,OAAO,CAAC,aAAa,CAAC,eAAe,CAAC;KAC9C;;IAED,OAAO,QAAQ,CAAC,eAAe,CAAC;GACjC;;;;EAID,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,wBAAwB,CAAC,YAAY,EAAE,UAAU,CAAC,KAAK,QAAQ,EAAE;IAC5H,OAAO,eAAe,CAAC,YAAY,CAAC,CAAC;GACtC;;EAED,OAAO,YAAY,CAAC;CACrB;;AAED,SAAS,iBAAiB,CAAC,OAAO,EAAE;EAClC,IAAI,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;;EAEhC,IAAI,QAAQ,KAAK,MAAM,EAAE;IACvB,OAAO,KAAK,CAAC;GACd;EACD,OAAO,QAAQ,KAAK,MAAM,IAAI,eAAe,CAAC,OAAO,CAAC,iBAAiB,CAAC,KAAK,OAAO,CAAC;CACtF;;;;;;;;;AASD,SAAS,OAAO,CAAC,IAAI,EAAE;EACrB,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,EAAE;IAC5B,OAAO,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;GACjC;;EAED,OAAO,IAAI,CAAC;CACb;;;;;;;;;;AAUD,SAAS,sBAAsB,CAAC,QAAQ,EAAE,QAAQ,EAAE;;EAElD,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,QAAQ,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;IACtE,OAAO,QAAQ,CAAC,eAAe,CAAC;GACjC;;;EAGD,IAAI,KAAK,GAAG,QAAQ,CAAC,uBAAuB,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,2BAA2B,CAAC;EAC1F,IAAI,KAAK,GAAG,KAAK,GAAG,QAAQ,GAAG,QAAQ,CAAC;EACxC,IAAI,GAAG,GAAG,KAAK,GAAG,QAAQ,GAAG,QAAQ,CAAC;;;EAGtC,IAAI,KAAK,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;EACnC,KAAK,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;EACzB,KAAK,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;EACrB,IAAI,uBAAuB,GAAG,KAAK,CAAC,uBAAuB,CAAC;;;;EAI5D,IAAI,QAAQ,KAAK,uBAAuB,IAAI,QAAQ,KAAK,uBAAuB,IAAI,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;IACvG,IAAI,iBAAiB,CAAC,uBAAuB,CAAC,EAAE;MAC9C,OAAO,uBAAuB,CAAC;KAChC;;IAED,OAAO,eAAe,CAAC,uBAAuB,CAAC,CAAC;GACjD;;;EAGD,IAAI,YAAY,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;EACrC,IAAI,YAAY,CAAC,IAAI,EAAE;IACrB,OAAO,sBAAsB,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;GAC5D,MAAM;IACL,OAAO,sBAAsB,CAAC,QAAQ,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC;GACjE;CACF;;;;;;;;;;AAUD,SAAS,SAAS,CAAC,OAAO,EAAE;EAC1B,IAAI,IAAI,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,KAAK,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;;EAErF,IAAI,SAAS,GAAG,IAAI,KAAK,KAAK,GAAG,WAAW,GAAG,YAAY,CAAC;EAC5D,IAAI,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;;EAEhC,IAAI,QAAQ,KAAK,MAAM,IAAI,QAAQ,KAAK,MAAM,EAAE;IAC9C,IAAI,IAAI,GAAG,OAAO,CAAC,aAAa,CAAC,eAAe,CAAC;IACjD,IAAI,gBAAgB,GAAG,OAAO,CAAC,aAAa,CAAC,gBAAgB,IAAI,IAAI,CAAC;IACtE,OAAO,gBAAgB,CAAC,SAAS,CAAC,CAAC;GACpC;;EAED,OAAO,OAAO,CAAC,SAAS,CAAC,CAAC;CAC3B;;;;;;;;;;;AAWD,SAAS,aAAa,CAAC,IAAI,EAAE,OAAO,EAAE;EACpC,IAAI,QAAQ,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,KAAK,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;;EAEzF,IAAI,SAAS,GAAG,SAAS,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;EAC1C,IAAI,UAAU,GAAG,SAAS,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;EAC5C,IAAI,QAAQ,GAAG,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;EACjC,IAAI,CAAC,GAAG,IAAI,SAAS,GAAG,QAAQ,CAAC;EACjC,IAAI,CAAC,MAAM,IAAI,SAAS,GAAG,QAAQ,CAAC;EACpC,IAAI,CAAC,IAAI,IAAI,UAAU,GAAG,QAAQ,CAAC;EACnC,IAAI,CAAC,KAAK,IAAI,UAAU,GAAG,QAAQ,CAAC;EACpC,OAAO,IAAI,CAAC;CACb;;;;;;;;;;;;AAYD,SAAS,cAAc,CAAC,MAAM,EAAE,IAAI,EAAE;EACpC,IAAI,KAAK,GAAG,IAAI,KAAK,GAAG,GAAG,MAAM,GAAG,KAAK,CAAC;EAC1C,IAAI,KAAK,GAAG,KAAK,KAAK,MAAM,GAAG,OAAO,GAAG,QAAQ,CAAC;;EAElD,OAAO,UAAU,CAAC,MAAM,CAAC,QAAQ,GAAG,KAAK,GAAG,OAAO,CAAC,EAAE,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,CAAC,QAAQ,GAAG,KAAK,GAAG,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;CAChH;;;;;;;;AAQD,IAAI,MAAM,GAAG,SAAS,CAAC;;AAEvB,IAAI,QAAQ,GAAG,YAAY;EACzB,IAAI,MAAM,KAAK,SAAS,EAAE;IACxB,MAAM,GAAG,SAAS,CAAC,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;GACzD;EACD,OAAO,MAAM,CAAC;CACf,CAAC;;AAEF,SAAS,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,aAAa,EAAE;EAChD,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,EAAE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,EAAE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,EAAE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,EAAE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,aAAa,CAAC,QAAQ,IAAI,IAAI,KAAK,QAAQ,GAAG,KAAK,GAAG,MAAM,CAAC,CAAC,GAAG,aAAa,CAAC,QAAQ,IAAI,IAAI,KAAK,QAAQ,GAAG,QAAQ,GAAG,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;CAClT;;AAED,SAAS,cAAc,GAAG;EACxB,IAAI,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;EACzB,IAAI,IAAI,GAAG,QAAQ,CAAC,eAAe,CAAC;EACpC,IAAI,aAAa,GAAG,QAAQ,EAAE,IAAI,gBAAgB,CAAC,IAAI,CAAC,CAAC;;EAEzD,OAAO;IACL,MAAM,EAAE,OAAO,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,aAAa,CAAC;IACpD,KAAK,EAAE,OAAO,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,aAAa,CAAC;GACnD,CAAC;CACH;;AAED,IAAI,cAAc,GAAG,UAAU,QAAQ,EAAE,WAAW,EAAE;EACpD,IAAI,EAAE,QAAQ,YAAY,WAAW,CAAC,EAAE;IACtC,MAAM,IAAI,SAAS,CAAC,mCAAmC,CAAC,CAAC;GAC1D;CACF,CAAC;;AAEF,IAAI,WAAW,GAAG,YAAY;EAC5B,SAAS,gBAAgB,CAAC,MAAM,EAAE,KAAK,EAAE;IACvC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;MACrC,IAAI,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;MAC1B,UAAU,CAAC,UAAU,GAAG,UAAU,CAAC,UAAU,IAAI,KAAK,CAAC;MACvD,UAAU,CAAC,YAAY,GAAG,IAAI,CAAC;MAC/B,IAAI,OAAO,IAAI,UAAU,EAAE,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC;MACtD,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,UAAU,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;KAC3D;GACF;;EAED,OAAO,UAAU,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE;IACrD,IAAI,UAAU,EAAE,gBAAgB,CAAC,WAAW,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;IACpE,IAAI,WAAW,EAAE,gBAAgB,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;IAC5D,OAAO,WAAW,CAAC;GACpB,CAAC;CACH,EAAE,CAAC;;;;;;AAMJ,IAAI,cAAc,GAAG,UAAU,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE;EAC9C,IAAI,GAAG,IAAI,GAAG,EAAE;IACd,MAAM,CAAC,cAAc,CAAC,GAAG,EAAE,GAAG,EAAE;MAC9B,KAAK,EAAE,KAAK;MACZ,UAAU,EAAE,IAAI;MAChB,YAAY,EAAE,IAAI;MAClB,QAAQ,EAAE,IAAI;KACf,CAAC,CAAC;GACJ,MAAM;IACL,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;GAClB;;EAED,OAAO,GAAG,CAAC;CACZ,CAAC;;AAEF,IAAI0R,UAAQ,GAAG,MAAM,CAAC,MAAM,IAAI,UAAU,MAAM,EAAE;EAChD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;IACzC,IAAI,MAAM,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;;IAE1B,KAAK,IAAI,GAAG,IAAI,MAAM,EAAE;MACtB,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE;QACrD,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;OAC3B;KACF;GACF;;EAED,OAAO,MAAM,CAAC;CACf,CAAC;;;;;;;;;AASF,SAAS,aAAa,CAAC,OAAO,EAAE;EAC9B,OAAOA,UAAQ,CAAC,EAAE,EAAE,OAAO,EAAE;IAC3B,KAAK,EAAE,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,KAAK;IACnC,MAAM,EAAE,OAAO,CAAC,GAAG,GAAG,OAAO,CAAC,MAAM;GACrC,CAAC,CAAC;CACJ;;;;;;;;;AASD,SAAS,qBAAqB,CAAC,OAAO,EAAE;EACtC,IAAI,IAAI,GAAG,EAAE,CAAC;;;;;EAKd,IAAI,QAAQ,EAAE,EAAE;IACd,IAAI;MACF,IAAI,GAAG,OAAO,CAAC,qBAAqB,EAAE,CAAC;MACvC,IAAI,SAAS,GAAG,SAAS,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;MAC1C,IAAI,UAAU,GAAG,SAAS,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;MAC5C,IAAI,CAAC,GAAG,IAAI,SAAS,CAAC;MACtB,IAAI,CAAC,IAAI,IAAI,UAAU,CAAC;MACxB,IAAI,CAAC,MAAM,IAAI,SAAS,CAAC;MACzB,IAAI,CAAC,KAAK,IAAI,UAAU,CAAC;KAC1B,CAAC,OAAO,GAAG,EAAE,EAAE;GACjB,MAAM;IACL,IAAI,GAAG,OAAO,CAAC,qBAAqB,EAAE,CAAC;GACxC;;EAED,IAAI,MAAM,GAAG;IACX,IAAI,EAAE,IAAI,CAAC,IAAI;IACf,GAAG,EAAE,IAAI,CAAC,GAAG;IACb,KAAK,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI;IAC7B,MAAM,EAAE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG;GAC/B,CAAC;;;EAGF,IAAI,KAAK,GAAG,OAAO,CAAC,QAAQ,KAAK,MAAM,GAAG,cAAc,EAAE,GAAG,EAAE,CAAC;EAChE,IAAI,KAAK,GAAG,KAAK,CAAC,KAAK,IAAI,OAAO,CAAC,WAAW,IAAI,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC;EAC7E,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,IAAI,OAAO,CAAC,YAAY,IAAI,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC;;EAEhF,IAAI,cAAc,GAAG,OAAO,CAAC,WAAW,GAAG,KAAK,CAAC;EACjD,IAAI,aAAa,GAAG,OAAO,CAAC,YAAY,GAAG,MAAM,CAAC;;;;EAIlD,IAAI,cAAc,IAAI,aAAa,EAAE;IACnC,IAAI,MAAM,GAAG,wBAAwB,CAAC,OAAO,CAAC,CAAC;IAC/C,cAAc,IAAI,cAAc,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IAC9C,aAAa,IAAI,cAAc,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;;IAE7C,MAAM,CAAC,KAAK,IAAI,cAAc,CAAC;IAC/B,MAAM,CAAC,MAAM,IAAI,aAAa,CAAC;GAChC;;EAED,OAAO,aAAa,CAAC,MAAM,CAAC,CAAC;CAC9B;;AAED,SAAS,oCAAoC,CAAC,QAAQ,EAAE,MAAM,EAAE;EAC9D,IAAI,MAAM,GAAG,QAAQ,EAAE,CAAC;EACxB,IAAI,MAAM,GAAG,MAAM,CAAC,QAAQ,KAAK,MAAM,CAAC;EACxC,IAAI,YAAY,GAAG,qBAAqB,CAAC,QAAQ,CAAC,CAAC;EACnD,IAAI,UAAU,GAAG,qBAAqB,CAAC,MAAM,CAAC,CAAC;EAC/C,IAAI,YAAY,GAAG,eAAe,CAAC,QAAQ,CAAC,CAAC;;EAE7C,IAAI,MAAM,GAAG,wBAAwB,CAAC,MAAM,CAAC,CAAC;EAC9C,IAAI,cAAc,GAAG,UAAU,CAAC,MAAM,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;EAC3D,IAAI,eAAe,GAAG,UAAU,CAAC,MAAM,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC;;EAE7D,IAAI,OAAO,GAAG,aAAa,CAAC;IAC1B,GAAG,EAAE,YAAY,CAAC,GAAG,GAAG,UAAU,CAAC,GAAG,GAAG,cAAc;IACvD,IAAI,EAAE,YAAY,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI,GAAG,eAAe;IAC3D,KAAK,EAAE,YAAY,CAAC,KAAK;IACzB,MAAM,EAAE,YAAY,CAAC,MAAM;GAC5B,CAAC,CAAC;EACH,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC;EACtB,OAAO,CAAC,UAAU,GAAG,CAAC,CAAC;;;;;;EAMvB,IAAI,CAAC,MAAM,IAAI,MAAM,EAAE;IACrB,IAAI,SAAS,GAAG,UAAU,CAAC,MAAM,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;IACjD,IAAI,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC;;IAEnD,OAAO,CAAC,GAAG,IAAI,cAAc,GAAG,SAAS,CAAC;IAC1C,OAAO,CAAC,MAAM,IAAI,cAAc,GAAG,SAAS,CAAC;IAC7C,OAAO,CAAC,IAAI,IAAI,eAAe,GAAG,UAAU,CAAC;IAC7C,OAAO,CAAC,KAAK,IAAI,eAAe,GAAG,UAAU,CAAC;;;IAG9C,OAAO,CAAC,SAAS,GAAG,SAAS,CAAC;IAC9B,OAAO,CAAC,UAAU,GAAG,UAAU,CAAC;GACjC;;EAED,IAAI,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,GAAG,MAAM,KAAK,YAAY,IAAI,YAAY,CAAC,QAAQ,KAAK,MAAM,EAAE;IACxG,OAAO,GAAG,aAAa,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;GAC1C;;EAED,OAAO,OAAO,CAAC;CAChB;;AAED,SAAS,6CAA6C,CAAC,OAAO,EAAE;EAC9D,IAAI,IAAI,GAAG,OAAO,CAAC,aAAa,CAAC,eAAe,CAAC;EACjD,IAAI,cAAc,GAAG,oCAAoC,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;EACzE,IAAI,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,MAAM,CAAC,UAAU,IAAI,CAAC,CAAC,CAAC;EAC/D,IAAI,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,EAAE,MAAM,CAAC,WAAW,IAAI,CAAC,CAAC,CAAC;;EAElE,IAAI,SAAS,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;EAChC,IAAI,UAAU,GAAG,SAAS,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;;EAEzC,IAAI,MAAM,GAAG;IACX,GAAG,EAAE,SAAS,GAAG,cAAc,CAAC,GAAG,GAAG,cAAc,CAAC,SAAS;IAC9D,IAAI,EAAE,UAAU,GAAG,cAAc,CAAC,IAAI,GAAG,cAAc,CAAC,UAAU;IAClE,KAAK,EAAE,KAAK;IACZ,MAAM,EAAE,MAAM;GACf,CAAC;;EAEF,OAAO,aAAa,CAAC,MAAM,CAAC,CAAC;CAC9B;;;;;;;;;;AAUD,SAAS,OAAO,CAAC,OAAO,EAAE;EACxB,IAAI,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;EAChC,IAAI,QAAQ,KAAK,MAAM,IAAI,QAAQ,KAAK,MAAM,EAAE;IAC9C,OAAO,KAAK,CAAC;GACd;EACD,IAAI,wBAAwB,CAAC,OAAO,EAAE,UAAU,CAAC,KAAK,OAAO,EAAE;IAC7D,OAAO,IAAI,CAAC;GACb;EACD,OAAO,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC;CACxC;;;;;;;;;;;;AAYD,SAAS,aAAa,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,iBAAiB,EAAE;;EAEpE,IAAI,UAAU,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC;EACrC,IAAI,YAAY,GAAG,sBAAsB,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;;;EAG7D,IAAI,iBAAiB,KAAK,UAAU,EAAE;IACpC,UAAU,GAAG,6CAA6C,CAAC,YAAY,CAAC,CAAC;GAC1E,MAAM;;IAEL,IAAI,cAAc,GAAG,KAAK,CAAC,CAAC;IAC5B,IAAI,iBAAiB,KAAK,cAAc,EAAE;MACxC,cAAc,GAAG,eAAe,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,CAAC;MAC3D,IAAI,cAAc,CAAC,QAAQ,KAAK,MAAM,EAAE;QACtC,cAAc,GAAG,MAAM,CAAC,aAAa,CAAC,eAAe,CAAC;OACvD;KACF,MAAM,IAAI,iBAAiB,KAAK,QAAQ,EAAE;MACzC,cAAc,GAAG,MAAM,CAAC,aAAa,CAAC,eAAe,CAAC;KACvD,MAAM;MACL,cAAc,GAAG,iBAAiB,CAAC;KACpC;;IAED,IAAI,OAAO,GAAG,oCAAoC,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;;;IAGjF,IAAI,cAAc,CAAC,QAAQ,KAAK,MAAM,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;MAChE,IAAI,eAAe,GAAG,cAAc,EAAE;UAClC,MAAM,GAAG,eAAe,CAAC,MAAM;UAC/B,KAAK,GAAG,eAAe,CAAC,KAAK,CAAC;;MAElC,UAAU,CAAC,GAAG,IAAI,OAAO,CAAC,GAAG,GAAG,OAAO,CAAC,SAAS,CAAC;MAClD,UAAU,CAAC,MAAM,GAAG,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC;MACzC,UAAU,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,UAAU,CAAC;MACrD,UAAU,CAAC,KAAK,GAAG,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC;KACzC,MAAM;;MAEL,UAAU,GAAG,OAAO,CAAC;KACtB;GACF;;;EAGD,UAAU,CAAC,IAAI,IAAI,OAAO,CAAC;EAC3B,UAAU,CAAC,GAAG,IAAI,OAAO,CAAC;EAC1B,UAAU,CAAC,KAAK,IAAI,OAAO,CAAC;EAC5B,UAAU,CAAC,MAAM,IAAI,OAAO,CAAC;;EAE7B,OAAO,UAAU,CAAC;CACnB;;AAED,SAAS,OAAO,CAAC,IAAI,EAAE;EACrB,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK;MAClB,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;;EAEzB,OAAO,KAAK,GAAG,MAAM,CAAC;CACvB;;;;;;;;;;;AAWD,SAAS,oBAAoB,CAAC,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,iBAAiB,EAAE;EACtF,IAAI,OAAO,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,KAAK,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;;EAEpF,IAAI,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;IACpC,OAAO,SAAS,CAAC;GAClB;;EAED,IAAI,UAAU,GAAG,aAAa,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,iBAAiB,CAAC,CAAC;;EAE9E,IAAI,KAAK,GAAG;IACV,GAAG,EAAE;MACH,KAAK,EAAE,UAAU,CAAC,KAAK;MACvB,MAAM,EAAE,OAAO,CAAC,GAAG,GAAG,UAAU,CAAC,GAAG;KACrC;IACD,KAAK,EAAE;MACL,KAAK,EAAE,UAAU,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK;MACvC,MAAM,EAAE,UAAU,CAAC,MAAM;KAC1B;IACD,MAAM,EAAE;MACN,KAAK,EAAE,UAAU,CAAC,KAAK;MACvB,MAAM,EAAE,UAAU,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM;KAC3C;IACD,IAAI,EAAE;MACJ,KAAK,EAAE,OAAO,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI;MACrC,MAAM,EAAE,UAAU,CAAC,MAAM;KAC1B;GACF,CAAC;;EAEF,IAAI,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,UAAU,GAAG,EAAE;IACtD,OAAOA,UAAQ,CAAC;MACd,GAAG,EAAE,GAAG;KACT,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE;MACb,IAAI,EAAE,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;KAC1B,CAAC,CAAC;GACJ,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE;IACtB,OAAO,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC;GACxB,CAAC,CAAC;;EAEH,IAAI,aAAa,GAAG,WAAW,CAAC,MAAM,CAAC,UAAU,KAAK,EAAE;IACtD,IAAI,KAAK,GAAG,KAAK,CAAC,KAAK;QACnB,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;IAC1B,OAAO,KAAK,IAAI,MAAM,CAAC,WAAW,IAAI,MAAM,IAAI,MAAM,CAAC,YAAY,CAAC;GACrE,CAAC,CAAC;;EAEH,IAAI,iBAAiB,GAAG,aAAa,CAAC,MAAM,GAAG,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;;EAE7F,IAAI,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;;EAExC,OAAO,iBAAiB,IAAI,SAAS,GAAG,GAAG,GAAG,SAAS,GAAG,EAAE,CAAC,CAAC;CAC/D;;;;;;;;;;;AAWD,SAAS,mBAAmB,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE;EACrD,IAAI,kBAAkB,GAAG,sBAAsB,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;EACnE,OAAO,oCAAoC,CAAC,SAAS,EAAE,kBAAkB,CAAC,CAAC;CAC5E;;;;;;;;;AASD,SAAS,aAAa,CAAC,OAAO,EAAE;EAC9B,IAAI,MAAM,GAAG,gBAAgB,CAAC,OAAO,CAAC,CAAC;EACvC,IAAI,CAAC,GAAG,UAAU,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,UAAU,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;EACvE,IAAI,CAAC,GAAG,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,UAAU,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;EACvE,IAAI,MAAM,GAAG;IACX,KAAK,EAAE,OAAO,CAAC,WAAW,GAAG,CAAC;IAC9B,MAAM,EAAE,OAAO,CAAC,YAAY,GAAG,CAAC;GACjC,CAAC;EACF,OAAO,MAAM,CAAC;CACf;;;;;;;;;AASD,SAAS,oBAAoB,CAAC,SAAS,EAAE;EACvC,IAAI,IAAI,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,QAAQ,EAAE,CAAC;EAC1E,OAAO,SAAS,CAAC,OAAO,CAAC,wBAAwB,EAAE,UAAU,OAAO,EAAE;IACpE,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;GACtB,CAAC,CAAC;CACJ;;;;;;;;;;;;AAYD,SAAS,gBAAgB,CAAC,MAAM,EAAE,gBAAgB,EAAE,SAAS,EAAE;EAC7D,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;;;EAGpC,IAAI,UAAU,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC;;;EAGvC,IAAI,aAAa,GAAG;IAClB,KAAK,EAAE,UAAU,CAAC,KAAK;IACvB,MAAM,EAAE,UAAU,CAAC,MAAM;GAC1B,CAAC;;;EAGF,IAAI,OAAO,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;EAC1D,IAAI,QAAQ,GAAG,OAAO,GAAG,KAAK,GAAG,MAAM,CAAC;EACxC,IAAI,aAAa,GAAG,OAAO,GAAG,MAAM,GAAG,KAAK,CAAC;EAC7C,IAAI,WAAW,GAAG,OAAO,GAAG,QAAQ,GAAG,OAAO,CAAC;EAC/C,IAAI,oBAAoB,GAAG,CAAC,OAAO,GAAG,QAAQ,GAAG,OAAO,CAAC;;EAEzD,aAAa,CAAC,QAAQ,CAAC,GAAG,gBAAgB,CAAC,QAAQ,CAAC,GAAG,gBAAgB,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;EACvH,IAAI,SAAS,KAAK,aAAa,EAAE;IAC/B,aAAa,CAAC,aAAa,CAAC,GAAG,gBAAgB,CAAC,aAAa,CAAC,GAAG,UAAU,CAAC,oBAAoB,CAAC,CAAC;GACnG,MAAM;IACL,aAAa,CAAC,aAAa,CAAC,GAAG,gBAAgB,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC,CAAC;GACtF;;EAED,OAAO,aAAa,CAAC;CACtB;;;;;;;;;;;AAWD,SAAS,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE;;EAExB,IAAI,KAAK,CAAC,SAAS,CAAC,IAAI,EAAE;IACxB,OAAO,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;GACxB;;;EAGD,OAAO,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;CAC7B;;;;;;;;;;;AAWD,SAAS,SAAS,CAAC,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE;;EAEnC,IAAI,KAAK,CAAC,SAAS,CAAC,SAAS,EAAE;IAC7B,OAAO,GAAG,CAAC,SAAS,CAAC,UAAU,GAAG,EAAE;MAClC,OAAO,GAAG,CAAC,IAAI,CAAC,KAAK,KAAK,CAAC;KAC5B,CAAC,CAAC;GACJ;;;EAGD,IAAI,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,UAAU,GAAG,EAAE;IACnC,OAAO,GAAG,CAAC,IAAI,CAAC,KAAK,KAAK,CAAC;GAC5B,CAAC,CAAC;EACH,OAAO,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;CAC3B;;;;;;;;;;;;AAYD,SAAS,YAAY,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE;EAC3C,IAAI,cAAc,GAAG,IAAI,KAAK,SAAS,GAAG,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,SAAS,CAAC,SAAS,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;;EAE7G,cAAc,CAAC,OAAO,CAAC,UAAU,QAAQ,EAAE;IACzC,IAAI,QAAQ,CAAC,UAAU,CAAC,EAAE;;MAExB,OAAO,CAAC,IAAI,CAAC,uDAAuD,CAAC,CAAC;KACvE;IACD,IAAI,EAAE,GAAG,QAAQ,CAAC,UAAU,CAAC,IAAI,QAAQ,CAAC,EAAE,CAAC;IAC7C,IAAI,QAAQ,CAAC,OAAO,IAAI,UAAU,CAAC,EAAE,CAAC,EAAE;;;;MAItC,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;MACzD,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;;MAE/D,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;KAC3B;GACF,CAAC,CAAC;;EAEH,OAAO,IAAI,CAAC;CACb;;;;;;;;;AASD,SAAS,MAAM,GAAG;;EAEhB,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE;IAC1B,OAAO;GACR;;EAED,IAAI,IAAI,GAAG;IACT,QAAQ,EAAE,IAAI;IACd,MAAM,EAAE,EAAE;IACV,WAAW,EAAE,EAAE;IACf,UAAU,EAAE,EAAE;IACd,OAAO,EAAE,KAAK;IACd,OAAO,EAAE,EAAE;GACZ,CAAC;;;EAGF,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,mBAAmB,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;;;;;EAKtF,IAAI,CAAC,SAAS,GAAG,oBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;;;EAGvM,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,SAAS,CAAC;;;EAGxC,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,gBAAgB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;EAC5F,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,GAAG,UAAU,CAAC;;;EAG1C,IAAI,GAAG,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;;;;EAI1C,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE;IACzB,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC;IAC5B,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;GAC7B,MAAM;IACL,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;GAC7B;CACF;;;;;;;;AAQD,SAAS,iBAAiB,CAAC,SAAS,EAAE,YAAY,EAAE;EAClD,OAAO,SAAS,CAAC,IAAI,CAAC,UAAU,IAAI,EAAE;IACpC,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI;QAChB,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;IAC3B,OAAO,OAAO,IAAI,IAAI,KAAK,YAAY,CAAC;GACzC,CAAC,CAAC;CACJ;;;;;;;;;AASD,SAAS,wBAAwB,CAAC,QAAQ,EAAE;EAC1C,IAAI,QAAQ,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;EACnD,IAAI,SAAS,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;;EAErE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;IAC5C,IAAI,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;IACzB,IAAI,OAAO,GAAG,MAAM,GAAG,EAAE,GAAG,MAAM,GAAG,SAAS,GAAG,QAAQ,CAAC;IAC1D,IAAI,OAAO,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,WAAW,EAAE;MACvD,OAAO,OAAO,CAAC;KAChB;GACF;EACD,OAAO,IAAI,CAAC;CACb;;;;;;;AAOD,SAAS,OAAO,GAAG;EACjB,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC;;;EAG9B,IAAI,iBAAiB,CAAC,IAAI,CAAC,SAAS,EAAE,YAAY,CAAC,EAAE;IACnD,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;IAC3C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,GAAG,EAAE,CAAC;IAC5B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,GAAG,EAAE,CAAC;IAChC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,EAAE,CAAC;IAC3B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wBAAwB,CAAC,WAAW,CAAC,CAAC,GAAG,EAAE,CAAC;GAC/D;;EAED,IAAI,CAAC,qBAAqB,EAAE,CAAC;;;;EAI7B,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE;IAChC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;GACjD;EACD,OAAO,IAAI,CAAC;CACb;;;;;;;AAOD,SAAS,SAAS,CAAC,OAAO,EAAE;EAC1B,IAAI,aAAa,GAAG,OAAO,CAAC,aAAa,CAAC;EAC1C,OAAO,aAAa,GAAG,aAAa,CAAC,WAAW,GAAG,MAAM,CAAC;CAC3D;;AAED,SAAS,qBAAqB,CAAC,YAAY,EAAE,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE;EAC3E,IAAI,MAAM,GAAG,YAAY,CAAC,QAAQ,KAAK,MAAM,CAAC;EAC9C,IAAI,MAAM,GAAG,MAAM,GAAG,YAAY,CAAC,aAAa,CAAC,WAAW,GAAG,YAAY,CAAC;EAC5E,MAAM,CAAC,gBAAgB,CAAC,KAAK,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;;EAE5D,IAAI,CAAC,MAAM,EAAE;IACX,qBAAqB,CAAC,eAAe,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;GAC3F;EACD,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;CAC5B;;;;;;;;AAQD,SAAS,mBAAmB,CAAC,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE,WAAW,EAAE;;EAEnE,KAAK,CAAC,WAAW,GAAG,WAAW,CAAC;EAChC,SAAS,CAAC,SAAS,CAAC,CAAC,gBAAgB,CAAC,QAAQ,EAAE,KAAK,CAAC,WAAW,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;;;EAGtF,IAAI,aAAa,GAAG,eAAe,CAAC,SAAS,CAAC,CAAC;EAC/C,qBAAqB,CAAC,aAAa,EAAE,QAAQ,EAAE,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,aAAa,CAAC,CAAC;EACvF,KAAK,CAAC,aAAa,GAAG,aAAa,CAAC;EACpC,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC;;EAE3B,OAAO,KAAK,CAAC;CACd;;;;;;;;AAQD,SAAS,oBAAoB,GAAG;EAC9B,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE;IAC7B,IAAI,CAAC,KAAK,GAAG,mBAAmB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;GACjG;CACF;;;;;;;;AAQD,SAAS,oBAAoB,CAAC,SAAS,EAAE,KAAK,EAAE;;EAE9C,SAAS,CAAC,SAAS,CAAC,CAAC,mBAAmB,CAAC,QAAQ,EAAE,KAAK,CAAC,WAAW,CAAC,CAAC;;;EAGtE,KAAK,CAAC,aAAa,CAAC,OAAO,CAAC,UAAU,MAAM,EAAE;IAC5C,MAAM,CAAC,mBAAmB,CAAC,QAAQ,EAAE,KAAK,CAAC,WAAW,CAAC,CAAC;GACzD,CAAC,CAAC;;;EAGH,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC;EACzB,KAAK,CAAC,aAAa,GAAG,EAAE,CAAC;EACzB,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC;EAC3B,KAAK,CAAC,aAAa,GAAG,KAAK,CAAC;EAC5B,OAAO,KAAK,CAAC;CACd;;;;;;;;;AASD,SAAS,qBAAqB,GAAG;EAC/B,IAAI,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE;IAC5B,oBAAoB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IAC1C,IAAI,CAAC,KAAK,GAAG,oBAAoB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;GAC/D;CACF;;;;;;;;;AASD,SAAS,SAAS,CAAC,CAAC,EAAE;EACpB,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC;CACzD;;;;;;;;;;AAUD,SAAS,SAAS,CAAC,OAAO,EAAE,MAAM,EAAE;EAClC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;IAC1C,IAAI,IAAI,GAAG,EAAE,CAAC;;IAEd,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE;MACzG,IAAI,GAAG,IAAI,CAAC;KACb;IACD,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;GAC3C,CAAC,CAAC;CACJ;;;;;;;;;;AAUD,SAAS,aAAa,CAAC,OAAO,EAAE,UAAU,EAAE;EAC1C,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;IAC9C,IAAI,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;IAC7B,IAAI,KAAK,KAAK,KAAK,EAAE;MACnB,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;KAC9C,MAAM;MACL,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;KAC/B;GACF,CAAC,CAAC;CACJ;;;;;;;;;;;AAWD,SAAS,UAAU,CAAC,IAAI,EAAE;;;;;EAKxB,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;;;;EAI7C,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;;;EAGrD,IAAI,IAAI,CAAC,YAAY,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,MAAM,EAAE;IAC7D,SAAS,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;GAChD;;EAED,OAAO,IAAI,CAAC;CACb;;;;;;;;;;;;AAYD,SAAS,gBAAgB,CAAC,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,eAAe,EAAE,KAAK,EAAE;;EAE5E,IAAI,gBAAgB,GAAG,mBAAmB,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;;;;;EAKrE,IAAI,SAAS,GAAG,oBAAoB,CAAC,OAAO,CAAC,SAAS,EAAE,gBAAgB,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,iBAAiB,EAAE,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;;EAEvK,MAAM,CAAC,YAAY,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;;;;EAI9C,SAAS,CAAC,MAAM,EAAE,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC,CAAC;;EAE5C,OAAO,OAAO,CAAC;CAChB;;;;;;;;;AASD,SAAS,YAAY,CAAC,IAAI,EAAE,OAAO,EAAE;EACnC,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC;MACb,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC;EAClB,IAAI,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;;;;EAIjC,IAAI,2BAA2B,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,UAAU,QAAQ,EAAE;IAClF,OAAO,QAAQ,CAAC,IAAI,KAAK,YAAY,CAAC;GACvC,CAAC,CAAC,eAAe,CAAC;EACnB,IAAI,2BAA2B,KAAK,SAAS,EAAE;IAC7C,OAAO,CAAC,IAAI,CAAC,+HAA+H,CAAC,CAAC;GAC/I;EACD,IAAI,eAAe,GAAG,2BAA2B,KAAK,SAAS,GAAG,2BAA2B,GAAG,OAAO,CAAC,eAAe,CAAC;;EAExH,IAAI,YAAY,GAAG,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;EACzD,IAAI,gBAAgB,GAAG,qBAAqB,CAAC,YAAY,CAAC,CAAC;;;EAG3D,IAAI,MAAM,GAAG;IACX,QAAQ,EAAE,MAAM,CAAC,QAAQ;GAC1B,CAAC;;;EAGF,IAAI,OAAO,GAAG;IACZ,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC;IAC7B,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC;IAC3B,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC;IACjC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC;GAChC,CAAC;;EAEF,IAAI,KAAK,GAAG,CAAC,KAAK,QAAQ,GAAG,KAAK,GAAG,QAAQ,CAAC;EAC9C,IAAI,KAAK,GAAG,CAAC,KAAK,OAAO,GAAG,MAAM,GAAG,OAAO,CAAC;;;;;EAK7C,IAAI,gBAAgB,GAAG,wBAAwB,CAAC,WAAW,CAAC,CAAC;;;;;;;;;;;EAW7D,IAAI,IAAI,GAAG,KAAK,CAAC;MACb,GAAG,GAAG,KAAK,CAAC,CAAC;EACjB,IAAI,KAAK,KAAK,QAAQ,EAAE;IACtB,GAAG,GAAG,CAAC,gBAAgB,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;GACjD,MAAM;IACL,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;GACnB;EACD,IAAI,KAAK,KAAK,OAAO,EAAE;IACrB,IAAI,GAAG,CAAC,gBAAgB,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;GAChD,MAAM;IACL,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;GACrB;EACD,IAAI,eAAe,IAAI,gBAAgB,EAAE;IACvC,MAAM,CAAC,gBAAgB,CAAC,GAAG,cAAc,GAAG,IAAI,GAAG,MAAM,GAAG,GAAG,GAAG,QAAQ,CAAC;IAC3E,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAClB,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAClB,MAAM,CAAC,UAAU,GAAG,WAAW,CAAC;GACjC,MAAM;;IAEL,IAAI,SAAS,GAAG,KAAK,KAAK,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;IAC5C,IAAI,UAAU,GAAG,KAAK,KAAK,OAAO,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;IAC5C,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,SAAS,CAAC;IAChC,MAAM,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,UAAU,CAAC;IAClC,MAAM,CAAC,UAAU,GAAG,KAAK,GAAG,IAAI,GAAG,KAAK,CAAC;GAC1C;;;EAGD,IAAI,UAAU,GAAG;IACf,aAAa,EAAE,IAAI,CAAC,SAAS;GAC9B,CAAC;;;EAGF,IAAI,CAAC,UAAU,GAAGA,UAAQ,CAAC,EAAE,EAAE,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;EAC5D,IAAI,CAAC,MAAM,GAAGA,UAAQ,CAAC,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;EAChD,IAAI,CAAC,WAAW,GAAGA,UAAQ,CAAC,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;;EAEtE,OAAO,IAAI,CAAC;CACb;;;;;;;;;;;;AAYD,SAAS,kBAAkB,CAAC,SAAS,EAAE,cAAc,EAAE,aAAa,EAAE;EACpE,IAAI,UAAU,GAAG,IAAI,CAAC,SAAS,EAAE,UAAU,IAAI,EAAE;IAC/C,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;IACrB,OAAO,IAAI,KAAK,cAAc,CAAC;GAChC,CAAC,CAAC;;EAEH,IAAI,UAAU,GAAG,CAAC,CAAC,UAAU,IAAI,SAAS,CAAC,IAAI,CAAC,UAAU,QAAQ,EAAE;IAClE,OAAO,QAAQ,CAAC,IAAI,KAAK,aAAa,IAAI,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC;GACjG,CAAC,CAAC;;EAEH,IAAI,CAAC,UAAU,EAAE;IACf,IAAI,WAAW,GAAG,GAAG,GAAG,cAAc,GAAG,GAAG,CAAC;IAC7C,IAAI,SAAS,GAAG,GAAG,GAAG,aAAa,GAAG,GAAG,CAAC;IAC1C,OAAO,CAAC,IAAI,CAAC,SAAS,GAAG,2BAA2B,GAAG,WAAW,GAAG,2DAA2D,GAAG,WAAW,GAAG,GAAG,CAAC,CAAC;GACvJ;EACD,OAAO,UAAU,CAAC;CACnB;;;;;;;;;AASD,SAAS,KAAK,CAAC,IAAI,EAAE,OAAO,EAAE;EAC5B,IAAI,mBAAmB,CAAC;;;EAGxB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,OAAO,EAAE,cAAc,CAAC,EAAE;IACzE,OAAO,IAAI,CAAC;GACb;;EAED,IAAI,YAAY,GAAG,OAAO,CAAC,OAAO,CAAC;;;EAGnC,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE;IACpC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;;;IAGhE,IAAI,CAAC,YAAY,EAAE;MACjB,OAAO,IAAI,CAAC;KACb;GACF,MAAM;;;IAGL,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE;MAChD,OAAO,CAAC,IAAI,CAAC,+DAA+D,CAAC,CAAC;MAC9E,OAAO,IAAI,CAAC;KACb;GACF;;EAED,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7C,IAAI,aAAa,GAAG,IAAI,CAAC,OAAO;MAC5B,MAAM,GAAG,aAAa,CAAC,MAAM;MAC7B,SAAS,GAAG,aAAa,CAAC,SAAS,CAAC;;EAExC,IAAI,UAAU,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;;EAE7D,IAAI,GAAG,GAAG,UAAU,GAAG,QAAQ,GAAG,OAAO,CAAC;EAC1C,IAAI,eAAe,GAAG,UAAU,GAAG,KAAK,GAAG,MAAM,CAAC;EAClD,IAAI,IAAI,GAAG,eAAe,CAAC,WAAW,EAAE,CAAC;EACzC,IAAI,OAAO,GAAG,UAAU,GAAG,MAAM,GAAG,KAAK,CAAC;EAC1C,IAAI,MAAM,GAAG,UAAU,GAAG,QAAQ,GAAG,OAAO,CAAC;EAC7C,IAAI,gBAAgB,GAAG,aAAa,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,CAAC;;;;;;;;EAQxD,IAAI,SAAS,CAAC,MAAM,CAAC,GAAG,gBAAgB,GAAG,MAAM,CAAC,IAAI,CAAC,EAAE;IACvD,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,SAAS,CAAC,MAAM,CAAC,GAAG,gBAAgB,CAAC,CAAC;GACpF;;EAED,IAAI,SAAS,CAAC,IAAI,CAAC,GAAG,gBAAgB,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE;IACvD,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,SAAS,CAAC,IAAI,CAAC,GAAG,gBAAgB,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;GAClF;EACD,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;;;EAGzD,IAAI,MAAM,GAAG,SAAS,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,gBAAgB,GAAG,CAAC,CAAC;;;;EAIzE,IAAI,GAAG,GAAG,wBAAwB,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;EACzD,IAAI,gBAAgB,GAAG,UAAU,CAAC,GAAG,CAAC,QAAQ,GAAG,eAAe,CAAC,EAAE,EAAE,CAAC,CAAC;EACvE,IAAI,gBAAgB,GAAG,UAAU,CAAC,GAAG,CAAC,QAAQ,GAAG,eAAe,GAAG,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;EACjF,IAAI,SAAS,GAAG,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,gBAAgB,GAAG,gBAAgB,CAAC;;;EAGzF,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,gBAAgB,EAAE,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;;EAE7E,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;EACjC,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,mBAAmB,GAAG,EAAE,EAAE,cAAc,CAAC,mBAAmB,EAAE,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,cAAc,CAAC,mBAAmB,EAAE,OAAO,EAAE,EAAE,CAAC,EAAE,mBAAmB,CAAC,CAAC;;EAEzL,OAAO,IAAI,CAAC;CACb;;;;;;;;;AASD,SAAS,oBAAoB,CAAC,SAAS,EAAE;EACvC,IAAI,SAAS,KAAK,KAAK,EAAE;IACvB,OAAO,OAAO,CAAC;GAChB,MAAM,IAAI,SAAS,KAAK,OAAO,EAAE;IAChC,OAAO,KAAK,CAAC;GACd;EACD,OAAO,SAAS,CAAC;CAClB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCD,IAAI,UAAU,GAAG,CAAC,YAAY,EAAE,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,KAAK,EAAE,SAAS,EAAE,aAAa,EAAE,OAAO,EAAE,WAAW,EAAE,YAAY,EAAE,QAAQ,EAAE,cAAc,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC;;;AAGlM,IAAI,eAAe,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;;;;;;;;;;;;AAY1C,SAAS,SAAS,CAAC,SAAS,EAAE;EAC5B,IAAI,OAAO,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,KAAK,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;;EAExF,IAAI,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;EAC/C,IAAI,GAAG,GAAG,eAAe,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;EACnF,OAAO,OAAO,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,GAAG,CAAC;CACtC;;AAED,IAAI,SAAS,GAAG;EACd,IAAI,EAAE,MAAM;EACZ,SAAS,EAAE,WAAW;EACtB,gBAAgB,EAAE,kBAAkB;CACrC,CAAC;;;;;;;;;AASF,SAAS,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE;;EAE3B,IAAI,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,OAAO,CAAC,EAAE;IACvD,OAAO,IAAI,CAAC;GACb;;EAED,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,iBAAiB,EAAE;;IAE7D,OAAO,IAAI,CAAC;GACb;;EAED,IAAI,UAAU,GAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,iBAAiB,CAAC,CAAC;;EAE1H,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7C,IAAI,iBAAiB,GAAG,oBAAoB,CAAC,SAAS,CAAC,CAAC;EACxD,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;;EAEnD,IAAI,SAAS,GAAG,EAAE,CAAC;;EAEnB,QAAQ,OAAO,CAAC,QAAQ;IACtB,KAAK,SAAS,CAAC,IAAI;MACjB,SAAS,GAAG,CAAC,SAAS,EAAE,iBAAiB,CAAC,CAAC;MAC3C,MAAM;IACR,KAAK,SAAS,CAAC,SAAS;MACtB,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC,CAAC;MACjC,MAAM;IACR,KAAK,SAAS,CAAC,gBAAgB;MAC7B,SAAS,GAAG,SAAS,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;MACvC,MAAM;IACR;MACE,SAAS,GAAG,OAAO,CAAC,QAAQ,CAAC;GAChC;;EAED,SAAS,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE,KAAK,EAAE;IACvC,IAAI,SAAS,KAAK,IAAI,IAAI,SAAS,CAAC,MAAM,KAAK,KAAK,GAAG,CAAC,EAAE;MACxD,OAAO,IAAI,CAAC;KACb;;IAED,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACzC,iBAAiB,GAAG,oBAAoB,CAAC,SAAS,CAAC,CAAC;;IAEpD,IAAI,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;IACxC,IAAI,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;;;IAGxC,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;IACvB,IAAI,WAAW,GAAG,SAAS,KAAK,MAAM,IAAI,KAAK,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,SAAS,KAAK,OAAO,IAAI,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,SAAS,KAAK,KAAK,IAAI,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,SAAS,KAAK,QAAQ,IAAI,KAAK,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;;IAE7U,IAAI,aAAa,GAAG,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IACvE,IAAI,cAAc,GAAG,KAAK,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;IAC1E,IAAI,YAAY,GAAG,KAAK,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;IACpE,IAAI,eAAe,GAAG,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;;IAE7E,IAAI,mBAAmB,GAAG,SAAS,KAAK,MAAM,IAAI,aAAa,IAAI,SAAS,KAAK,OAAO,IAAI,cAAc,IAAI,SAAS,KAAK,KAAK,IAAI,YAAY,IAAI,SAAS,KAAK,QAAQ,IAAI,eAAe,CAAC;;;IAG/L,IAAI,UAAU,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;IAC7D,IAAI,gBAAgB,GAAG,CAAC,CAAC,OAAO,CAAC,cAAc,KAAK,UAAU,IAAI,SAAS,KAAK,OAAO,IAAI,aAAa,IAAI,UAAU,IAAI,SAAS,KAAK,KAAK,IAAI,cAAc,IAAI,CAAC,UAAU,IAAI,SAAS,KAAK,OAAO,IAAI,YAAY,IAAI,CAAC,UAAU,IAAI,SAAS,KAAK,KAAK,IAAI,eAAe,CAAC,CAAC;;IAElR,IAAI,WAAW,IAAI,mBAAmB,IAAI,gBAAgB,EAAE;;MAE1D,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;;MAEpB,IAAI,WAAW,IAAI,mBAAmB,EAAE;QACtC,SAAS,GAAG,SAAS,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;OAClC;;MAED,IAAI,gBAAgB,EAAE;QACpB,SAAS,GAAG,oBAAoB,CAAC,SAAS,CAAC,CAAC;OAC7C;;MAED,IAAI,CAAC,SAAS,GAAG,SAAS,IAAI,SAAS,GAAG,GAAG,GAAG,SAAS,GAAG,EAAE,CAAC,CAAC;;;;MAIhE,IAAI,CAAC,OAAO,CAAC,MAAM,GAAGA,UAAQ,CAAC,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;;MAExI,IAAI,GAAG,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;KAC5D;GACF,CAAC,CAAC;EACH,OAAO,IAAI,CAAC;CACb;;;;;;;;;AASD,SAAS,YAAY,CAAC,IAAI,EAAE;EAC1B,IAAI,aAAa,GAAG,IAAI,CAAC,OAAO;MAC5B,MAAM,GAAG,aAAa,CAAC,MAAM;MAC7B,SAAS,GAAG,aAAa,CAAC,SAAS,CAAC;;EAExC,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7C,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;EACvB,IAAI,UAAU,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;EAC7D,IAAI,IAAI,GAAG,UAAU,GAAG,OAAO,GAAG,QAAQ,CAAC;EAC3C,IAAI,MAAM,GAAG,UAAU,GAAG,MAAM,GAAG,KAAK,CAAC;EACzC,IAAI,WAAW,GAAG,UAAU,GAAG,OAAO,GAAG,QAAQ,CAAC;;EAElD,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE;IAC3C,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC;GAC9E;EACD,IAAI,MAAM,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE;IAC3C,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;GACtD;;EAED,OAAO,IAAI,CAAC;CACb;;;;;;;;;;;;;;AAcD,SAAS,OAAO,CAAC,GAAG,EAAE,WAAW,EAAE,aAAa,EAAE,gBAAgB,EAAE;;EAElE,IAAI,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAC;EACnD,IAAI,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;EACtB,IAAI,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;;;EAGpB,IAAI,CAAC,KAAK,EAAE;IACV,OAAO,GAAG,CAAC;GACZ;;EAED,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;IAC3B,IAAI,OAAO,GAAG,KAAK,CAAC,CAAC;IACrB,QAAQ,IAAI;MACV,KAAK,IAAI;QACP,OAAO,GAAG,aAAa,CAAC;QACxB,MAAM;MACR,KAAK,GAAG,CAAC;MACT,KAAK,IAAI,CAAC;MACV;QACE,OAAO,GAAG,gBAAgB,CAAC;KAC9B;;IAED,IAAI,IAAI,GAAG,aAAa,CAAC,OAAO,CAAC,CAAC;IAClC,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC;GACxC,MAAM,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,EAAE;;IAEzC,IAAI,IAAI,GAAG,KAAK,CAAC,CAAC;IAClB,IAAI,IAAI,KAAK,IAAI,EAAE;MACjB,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,eAAe,CAAC,YAAY,EAAE,MAAM,CAAC,WAAW,IAAI,CAAC,CAAC,CAAC;KACjF,MAAM;MACL,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,eAAe,CAAC,WAAW,EAAE,MAAM,CAAC,UAAU,IAAI,CAAC,CAAC,CAAC;KAC/E;IACD,OAAO,IAAI,GAAG,GAAG,GAAG,KAAK,CAAC;GAC3B,MAAM;;;IAGL,OAAO,KAAK,CAAC;GACd;CACF;;;;;;;;;;;;;AAaD,SAAS,WAAW,CAAC,MAAM,EAAE,aAAa,EAAE,gBAAgB,EAAE,aAAa,EAAE;EAC3E,IAAI,OAAO,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;;;;EAKrB,IAAI,SAAS,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;;;;EAIhE,IAAI,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,UAAU,IAAI,EAAE;IAC1D,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC;GACpB,CAAC,CAAC;;;;EAIH,IAAI,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,UAAU,IAAI,EAAE;IAC9D,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;GACnC,CAAC,CAAC,CAAC;;EAEJ,IAAI,SAAS,CAAC,OAAO,CAAC,IAAI,SAAS,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;IAChE,OAAO,CAAC,IAAI,CAAC,8EAA8E,CAAC,CAAC;GAC9F;;;;EAID,IAAI,UAAU,GAAG,aAAa,CAAC;EAC/B,IAAI,GAAG,GAAG,OAAO,KAAK,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;;;EAGzM,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE;;IAEjC,IAAI,WAAW,GAAG,CAAC,KAAK,KAAK,CAAC,GAAG,CAAC,SAAS,GAAG,SAAS,IAAI,QAAQ,GAAG,OAAO,CAAC;IAC9E,IAAI,iBAAiB,GAAG,KAAK,CAAC;IAC9B,OAAO,EAAE;;;KAGR,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE;MACtB,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;QAC1D,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;QACpB,iBAAiB,GAAG,IAAI,CAAC;QACzB,OAAO,CAAC,CAAC;OACV,MAAM,IAAI,iBAAiB,EAAE;QAC5B,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;QACrB,iBAAiB,GAAG,KAAK,CAAC;QAC1B,OAAO,CAAC,CAAC;OACV,MAAM;QACL,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;OACpB;KACF,EAAE,EAAE,CAAC;;KAEL,GAAG,CAAC,UAAU,GAAG,EAAE;MAClB,OAAO,OAAO,CAAC,GAAG,EAAE,WAAW,EAAE,aAAa,EAAE,gBAAgB,CAAC,CAAC;KACnE,CAAC,CAAC;GACJ,CAAC,CAAC;;;EAGH,GAAG,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE;IAC/B,EAAE,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE,MAAM,EAAE;MACjC,IAAI,SAAS,CAAC,IAAI,CAAC,EAAE;QACnB,OAAO,CAAC,KAAK,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;OAC5D;KACF,CAAC,CAAC;GACJ,CAAC,CAAC;EACH,OAAO,OAAO,CAAC;CAChB;;;;;;;;;;;AAWD,SAAS,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE;EAC1B,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;EACzB,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS;MAC1B,aAAa,GAAG,IAAI,CAAC,OAAO;MAC5B,MAAM,GAAG,aAAa,CAAC,MAAM;MAC7B,SAAS,GAAG,aAAa,CAAC,SAAS,CAAC;;EAExC,IAAI,aAAa,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;;EAE5C,IAAI,OAAO,GAAG,KAAK,CAAC,CAAC;EACrB,IAAI,SAAS,CAAC,CAAC,MAAM,CAAC,EAAE;IACtB,OAAO,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;GACxB,MAAM;IACL,OAAO,GAAG,WAAW,CAAC,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;GACjE;;EAED,IAAI,aAAa,KAAK,MAAM,EAAE;IAC5B,MAAM,CAAC,GAAG,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;IACzB,MAAM,CAAC,IAAI,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;GAC3B,MAAM,IAAI,aAAa,KAAK,OAAO,EAAE;IACpC,MAAM,CAAC,GAAG,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;IACzB,MAAM,CAAC,IAAI,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;GAC3B,MAAM,IAAI,aAAa,KAAK,KAAK,EAAE;IAClC,MAAM,CAAC,IAAI,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;IAC1B,MAAM,CAAC,GAAG,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;GAC1B,MAAM,IAAI,aAAa,KAAK,QAAQ,EAAE;IACrC,MAAM,CAAC,IAAI,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;IAC1B,MAAM,CAAC,GAAG,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;GAC1B;;EAED,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;EACrB,OAAO,IAAI,CAAC;CACb;;;;;;;;;AASD,SAAS,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE;EACtC,IAAI,iBAAiB,GAAG,OAAO,CAAC,iBAAiB,IAAI,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;;;;;EAK3F,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,KAAK,iBAAiB,EAAE;IACjD,iBAAiB,GAAG,eAAe,CAAC,iBAAiB,CAAC,CAAC;GACxD;;EAED,IAAI,UAAU,GAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,OAAO,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAAC;EAClH,OAAO,CAAC,UAAU,GAAG,UAAU,CAAC;;EAEhC,IAAI,KAAK,GAAG,OAAO,CAAC,QAAQ,CAAC;EAC7B,IAAI,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;;EAEjC,IAAI,KAAK,GAAG;IACV,OAAO,EAAE,SAAS,OAAO,CAAC,SAAS,EAAE;MACnC,IAAI,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;MAC9B,IAAI,MAAM,CAAC,SAAS,CAAC,GAAG,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE;QAC7E,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC;OAC5D;MACD,OAAO,cAAc,CAAC,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;KAC7C;IACD,SAAS,EAAE,SAAS,SAAS,CAAC,SAAS,EAAE;MACvC,IAAI,QAAQ,GAAG,SAAS,KAAK,OAAO,GAAG,MAAM,GAAG,KAAK,CAAC;MACtD,IAAI,KAAK,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;MAC7B,IAAI,MAAM,CAAC,SAAS,CAAC,GAAG,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE;QAC7E,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,UAAU,CAAC,SAAS,CAAC,IAAI,SAAS,KAAK,OAAO,GAAG,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;OACpH;MACD,OAAO,cAAc,CAAC,EAAE,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;KAC5C;GACF,CAAC;;EAEF,KAAK,CAAC,OAAO,CAAC,UAAU,SAAS,EAAE;IACjC,IAAI,IAAI,GAAG,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,GAAG,SAAS,GAAG,WAAW,CAAC;IAC/E,MAAM,GAAGA,UAAQ,CAAC,EAAE,EAAE,MAAM,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;GACvD,CAAC,CAAC;;EAEH,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;;EAE7B,OAAO,IAAI,CAAC;CACb;;;;;;;;;AASD,SAAS,KAAK,CAAC,IAAI,EAAE;EACnB,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;EAC/B,IAAI,aAAa,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5C,IAAI,cAAc,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;;;EAG7C,IAAI,cAAc,EAAE;IAClB,IAAI,aAAa,GAAG,IAAI,CAAC,OAAO;QAC5B,SAAS,GAAG,aAAa,CAAC,SAAS;QACnC,MAAM,GAAG,aAAa,CAAC,MAAM,CAAC;;IAElC,IAAI,UAAU,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;IACjE,IAAI,IAAI,GAAG,UAAU,GAAG,MAAM,GAAG,KAAK,CAAC;IACvC,IAAI,WAAW,GAAG,UAAU,GAAG,OAAO,GAAG,QAAQ,CAAC;;IAElD,IAAI,YAAY,GAAG;MACjB,KAAK,EAAE,cAAc,CAAC,EAAE,EAAE,IAAI,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC;MAChD,GAAG,EAAE,cAAc,CAAC,EAAE,EAAE,IAAI,EAAE,SAAS,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,WAAW,CAAC,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC;KAC9F,CAAC;;IAEF,IAAI,CAAC,OAAO,CAAC,MAAM,GAAGA,UAAQ,CAAC,EAAE,EAAE,MAAM,EAAE,YAAY,CAAC,cAAc,CAAC,CAAC,CAAC;GAC1E;;EAED,OAAO,IAAI,CAAC;CACb;;;;;;;;;AASD,SAAS,IAAI,CAAC,IAAI,EAAE;EAClB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,MAAM,EAAE,iBAAiB,CAAC,EAAE;IAC3E,OAAO,IAAI,CAAC;GACb;;EAED,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;EACrC,IAAI,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,UAAU,QAAQ,EAAE;IAC5D,OAAO,QAAQ,CAAC,IAAI,KAAK,iBAAiB,CAAC;GAC5C,CAAC,CAAC,UAAU,CAAC;;EAEd,IAAI,OAAO,CAAC,MAAM,GAAG,KAAK,CAAC,GAAG,IAAI,OAAO,CAAC,IAAI,GAAG,KAAK,CAAC,KAAK,IAAI,OAAO,CAAC,GAAG,GAAG,KAAK,CAAC,MAAM,IAAI,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,EAAE;;IAExH,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,EAAE;MACtB,OAAO,IAAI,CAAC;KACb;;IAED,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACjB,IAAI,CAAC,UAAU,CAAC,qBAAqB,CAAC,GAAG,EAAE,CAAC;GAC7C,MAAM;;IAEL,IAAI,IAAI,CAAC,IAAI,KAAK,KAAK,EAAE;MACvB,OAAO,IAAI,CAAC;KACb;;IAED,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;IAClB,IAAI,CAAC,UAAU,CAAC,qBAAqB,CAAC,GAAG,KAAK,CAAC;GAChD;;EAED,OAAO,IAAI,CAAC;CACb;;;;;;;;;AASD,SAAS,KAAK,CAAC,IAAI,EAAE;EACnB,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;EAC/B,IAAI,aAAa,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5C,IAAI,aAAa,GAAG,IAAI,CAAC,OAAO;MAC5B,MAAM,GAAG,aAAa,CAAC,MAAM;MAC7B,SAAS,GAAG,aAAa,CAAC,SAAS,CAAC;;EAExC,IAAI,OAAO,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;;EAE9D,IAAI,cAAc,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;;EAEnE,MAAM,CAAC,OAAO,GAAG,MAAM,GAAG,KAAK,CAAC,GAAG,SAAS,CAAC,aAAa,CAAC,IAAI,cAAc,GAAG,MAAM,CAAC,OAAO,GAAG,OAAO,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;;EAE1H,IAAI,CAAC,SAAS,GAAG,oBAAoB,CAAC,SAAS,CAAC,CAAC;EACjD,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC;;EAE5C,OAAO,IAAI,CAAC;CACb;;;;;;;;;;;;;;;;;;;;;;;AAuBD,IAAI,SAAS,GAAG;;;;;;;;;EASd,KAAK,EAAE;;IAEL,KAAK,EAAE,GAAG;;IAEV,OAAO,EAAE,IAAI;;IAEb,EAAE,EAAE,KAAK;GACV;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAwCD,MAAM,EAAE;;IAEN,KAAK,EAAE,GAAG;;IAEV,OAAO,EAAE,IAAI;;IAEb,EAAE,EAAE,MAAM;;;;IAIV,MAAM,EAAE,CAAC;GACV;;;;;;;;;;;;;;;;;;;EAmBD,eAAe,EAAE;;IAEf,KAAK,EAAE,GAAG;;IAEV,OAAO,EAAE,IAAI;;IAEb,EAAE,EAAE,eAAe;;;;;;IAMnB,QAAQ,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,CAAC;;;;;;;IAO5C,OAAO,EAAE,CAAC;;;;;;IAMV,iBAAiB,EAAE,cAAc;GAClC;;;;;;;;;;;EAWD,YAAY,EAAE;;IAEZ,KAAK,EAAE,GAAG;;IAEV,OAAO,EAAE,IAAI;;IAEb,EAAE,EAAE,YAAY;GACjB;;;;;;;;;;;;EAYD,KAAK,EAAE;;IAEL,KAAK,EAAE,GAAG;;IAEV,OAAO,EAAE,IAAI;;IAEb,EAAE,EAAE,KAAK;;IAET,OAAO,EAAE,WAAW;GACrB;;;;;;;;;;;;;EAaD,IAAI,EAAE;;IAEJ,KAAK,EAAE,GAAG;;IAEV,OAAO,EAAE,IAAI;;IAEb,EAAE,EAAE,IAAI;;;;;;;IAOR,QAAQ,EAAE,MAAM;;;;;IAKhB,OAAO,EAAE,CAAC;;;;;;;IAOV,iBAAiB,EAAE,UAAU;GAC9B;;;;;;;;;EASD,KAAK,EAAE;;IAEL,KAAK,EAAE,GAAG;;IAEV,OAAO,EAAE,KAAK;;IAEd,EAAE,EAAE,KAAK;GACV;;;;;;;;;;;;EAYD,IAAI,EAAE;;IAEJ,KAAK,EAAE,GAAG;;IAEV,OAAO,EAAE,IAAI;;IAEb,EAAE,EAAE,IAAI;GACT;;;;;;;;;;;;;;;;;EAiBD,YAAY,EAAE;;IAEZ,KAAK,EAAE,GAAG;;IAEV,OAAO,EAAE,IAAI;;IAEb,EAAE,EAAE,YAAY;;;;;;IAMhB,eAAe,EAAE,IAAI;;;;;;IAMrB,CAAC,EAAE,QAAQ;;;;;;IAMX,CAAC,EAAE,OAAO;GACX;;;;;;;;;;;;;;;;;EAiBD,UAAU,EAAE;;IAEV,KAAK,EAAE,GAAG;;IAEV,OAAO,EAAE,IAAI;;IAEb,EAAE,EAAE,UAAU;;IAEd,MAAM,EAAE,gBAAgB;;;;;;;IAOxB,eAAe,EAAE,SAAS;GAC3B;CACF,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqCF,IAAI,QAAQ,GAAG;;;;;EAKb,SAAS,EAAE,QAAQ;;;;;;EAMnB,aAAa,EAAE,IAAI;;;;;;;EAOnB,eAAe,EAAE,KAAK;;;;;;;;EAQtB,QAAQ,EAAE,SAAS,QAAQ,GAAG,EAAE;;;;;;;;;;EAUhC,QAAQ,EAAE,SAAS,QAAQ,GAAG,EAAE;;;;;;;EAOhC,SAAS,EAAE,SAAS;CACrB,CAAC;;;;;;;;;;;;;;AAcF,IAAI,MAAM,GAAG,YAAY;;;;;;;;;EASvB,SAAS,MAAM,CAAC,SAAS,EAAE,MAAM,EAAE;IACjC,IAAI,KAAK,GAAG,IAAI,CAAC;;IAEjB,IAAI,OAAO,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,KAAK,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;IACrF,cAAc,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;;IAE7B,IAAI,CAAC,cAAc,GAAG,YAAY;MAChC,OAAO,qBAAqB,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;KAC5C,CAAC;;;IAGF,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;;;IAG/C,IAAI,CAAC,OAAO,GAAGA,UAAQ,CAAC,EAAE,EAAE,MAAM,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;;;IAGtD,IAAI,CAAC,KAAK,GAAG;MACX,WAAW,EAAE,KAAK;MAClB,SAAS,EAAE,KAAK;MAChB,aAAa,EAAE,EAAE;KAClB,CAAC;;;IAGF,IAAI,CAAC,SAAS,GAAG,SAAS,IAAI,SAAS,CAAC,MAAM,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC;IAC1E,IAAI,CAAC,MAAM,GAAG,MAAM,IAAI,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC;;;IAG3D,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,EAAE,CAAC;IAC5B,MAAM,CAAC,IAAI,CAACA,UAAQ,CAAC,EAAE,EAAE,MAAM,CAAC,QAAQ,CAAC,SAAS,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;MAC9F,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,GAAGA,UAAQ,CAAC,EAAE,EAAE,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;KACvI,CAAC,CAAC;;;IAGH,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,UAAU,IAAI,EAAE;MACvE,OAAOA,UAAQ,CAAC;QACd,IAAI,EAAE,IAAI;OACX,EAAE,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;KACnC,CAAC;;KAED,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE;MACpB,OAAO,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;KAC1B,CAAC,CAAC;;;;;;IAMH,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,eAAe,EAAE;MAChD,IAAI,eAAe,CAAC,OAAO,IAAI,UAAU,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE;QACjE,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,OAAO,EAAE,eAAe,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;OACpG;KACF,CAAC,CAAC;;;IAGH,IAAI,CAAC,MAAM,EAAE,CAAC;;IAEd,IAAI,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC;IAC/C,IAAI,aAAa,EAAE;;MAEjB,IAAI,CAAC,oBAAoB,EAAE,CAAC;KAC7B;;IAED,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,aAAa,CAAC;GAC1C;;;;;;EAMD,WAAW,CAAC,MAAM,EAAE,CAAC;IACnB,GAAG,EAAE,QAAQ;IACb,KAAK,EAAE,SAAS,SAAS,GAAG;MAC1B,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KAC1B;GACF,EAAE;IACD,GAAG,EAAE,SAAS;IACd,KAAK,EAAE,SAAS,UAAU,GAAG;MAC3B,OAAO,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KAC3B;GACF,EAAE;IACD,GAAG,EAAE,sBAAsB;IAC3B,KAAK,EAAE,SAAS,uBAAuB,GAAG;MACxC,OAAO,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KACxC;GACF,EAAE;IACD,GAAG,EAAE,uBAAuB;IAC5B,KAAK,EAAE,SAAS,wBAAwB,GAAG;MACzC,OAAO,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KACzC;;;;;;;;;;;;;;;;;;;;;;;;;;GA0BF,CAAC,CAAC,CAAC;EACJ,OAAO,MAAM,CAAC;CACf,EAAE,CAAC;;;;;;;;;;;;;;;;;;;;;;;AAuBJ,MAAM,CAAC,KAAK,GAAG,CAAC,OAAO,MAAM,KAAK,WAAW,GAAG,MAAM,GAAG,MAAM,EAAE,WAAW,CAAC;AAC7E,MAAM,CAAC,UAAU,GAAG,UAAU,CAAC;AAC/B,MAAM,CAAC,QAAQ,GAAG,QAAQ,CAAC;;AC73E3B;;;;;;;AAOA,IAAMC,WAAY,UAAC3R,IAAD,EAAO;;;;;;MAOjBsE,OAA2B,UAAjC;MACMC,UAA2B,OAAjC;MACMC,WAA2B,aAAjC;MACMC,kBAA+BD,QAArC;MACME,eAA2B,WAAjC;MACMC,qBAA2B3E,KAAE6B,EAAF,CAAKyC,IAAL,CAAjC;MACMsN,iBAA2B,EAAjC,CAbuB;;MAcjBC,gBAA2B,EAAjC,CAduB;;MAejBC,cAA2B,CAAjC,CAfuB;;MAgBjBC,mBAA2B,EAAjC,CAhBuB;;MAiBjBC,qBAA2B,EAAjC,CAjBuB;;MAkBjBC,2BAA2B,CAAjC,CAlBuB;;MAmBjBC,iBAA2B,IAAIjO,MAAJ,CAAc8N,gBAAd,SAAkCC,kBAAlC,SAAwDJ,cAAxD,CAAjC;MAEM9M,QAAQ;mBACcL,SADd;uBAEgBA,SAFhB;mBAGcA,SAHd;qBAIeA,SAJf;qBAKeA,SALf;8BAMeA,SAA3B,GAAuCC,YAN3B;kCAOiBD,SAA7B,GAAyCC,YAP7B;8BAQeD,SAA3B,GAAuCC;GARzC;MAWMK,YAAY;cACJ,UADI;UAEJ,MAFI;YAGJ,QAHI;eAIJ,WAJI;cAKJ,UALI;eAMJ,qBANI;cAOJ,oBAPI;qBAQE;GARpB;MAWMF,WAAW;iBACC,0BADD;gBAEC,gBAFD;UAGC,gBAHD;gBAIC,aAJD;mBAKC;GALlB;MAQMsN,gBAAgB;SACR,WADQ;YAER,SAFQ;YAGR,cAHQ;eAIR,YAJQ;WAKR,aALQ;cAMR,WANQ;UAOR,YAPQ;aAQR;GARd;MAWMrJ,UAAU;YACA,CADA;UAEA,IAFA;cAGA;GAHhB;MAMMC,cAAc;YACJ,0BADI;UAEJ,SAFI;cAGJ;;;;;;;GAHhB;;MAYM4I,QAhFiB;;;sBAiFThP,OAAZ,EAAqBY,MAArB,EAA6B;WACtByB,QAAL,GAAiBrC,OAAjB;WACKyP,OAAL,GAAiB,IAAjB;WACK7I,OAAL,GAAiB,KAAKC,UAAL,CAAgBjG,MAAhB,CAAjB;WACK8O,KAAL,GAAiB,KAAKC,eAAL,EAAjB;WACKC,SAAL,GAAiB,KAAKC,aAAL,EAAjB;;WAEK7I,kBAAL;KAxFmB;;;;;;WA2GrBtC,MA3GqB,qBA2GZ;UACH,KAAKrC,QAAL,CAAcyN,QAAd,IAA0BzS,KAAE,KAAKgF,QAAP,EAAiBkB,QAAjB,CAA0BnB,UAAU2N,QAApC,CAA9B,EAA6E;;;;UAIvE/M,SAAWgM,SAASgB,qBAAT,CAA+B,KAAK3N,QAApC,CAAjB;;UACM4N,WAAW5S,KAAE,KAAKqS,KAAP,EAAcnM,QAAd,CAAuBnB,UAAUkB,IAAjC,CAAjB;;eAES4M,WAAT;;UAEID,QAAJ,EAAc;;;;UAIRlG,gBAAgB;uBACL,KAAK1H;OADtB;UAGM8N,YAAY9S,KAAE8E,KAAF,CAAQA,MAAMmB,IAAd,EAAoByG,aAApB,CAAlB;WAEE/G,MAAF,EAAUxC,OAAV,CAAkB2P,SAAlB;;UAEIA,UAAUxN,kBAAV,EAAJ,EAAoC;;OArB7B;;;UA0BH,CAAC,KAAKiN,SAAV,EAAqB;;;;;YAKf,OAAOQ,MAAP,KAAkB,WAAtB,EAAmC;gBAC3B,IAAI/E,SAAJ,CAAc,8DAAd,CAAN;;;YAEErL,UAAU,KAAKqC,QAAnB,CARmB;;YAUfhF,KAAE2F,MAAF,EAAUO,QAAV,CAAmBnB,UAAUiO,MAA7B,CAAJ,EAA0C;cACpChT,KAAE,KAAKqS,KAAP,EAAcnM,QAAd,CAAuBnB,UAAUkO,QAAjC,KAA8CjT,KAAE,KAAKqS,KAAP,EAAcnM,QAAd,CAAuBnB,UAAUmO,SAAjC,CAAlD,EAA+F;sBACnFvN,MAAV;;SAZe;;;;;YAkBf,KAAK4D,OAAL,CAAa4J,QAAb,KAA0B,cAA9B,EAA8C;eAC1CxN,MAAF,EAAUwH,QAAV,CAAmBpI,UAAUqO,eAA7B;;;aAEGhB,OAAL,GAAe,IAAIW,MAAJ,CAAWpQ,OAAX,EAAoB,KAAK0P,KAAzB,EAAgC,KAAKgB,gBAAL,EAAhC,CAAf;OA/CK;;;;;;UAsDH,kBAAkB5Q,SAASgJ,eAA3B,IACDzL,KAAE2F,MAAF,EAAUC,OAAV,CAAkBf,SAASyO,UAA3B,EAAuCtQ,MAAvC,KAAkD,CADrD,EACwD;aACpD,MAAF,EAAUkK,QAAV,GAAqBnG,EAArB,CAAwB,WAAxB,EAAqC,IAArC,EAA2C/G,KAAEuT,IAA7C;;;WAGGvO,QAAL,CAAckD,KAAd;;WACKlD,QAAL,CAAcmD,YAAd,CAA2B,eAA3B,EAA4C,IAA5C;;WAEE,KAAKkK,KAAP,EAAcjK,WAAd,CAA0BrD,UAAUkB,IAApC;WACEN,MAAF,EACGyC,WADH,CACerD,UAAUkB,IADzB,EAEG9C,OAFH,CAEWnD,KAAE8E,KAAF,CAAQA,MAAMwL,KAAd,EAAqB5D,aAArB,CAFX;KA1KmB;;WA+KrBlH,OA/KqB,sBA+KX;WACNC,UAAF,CAAa,KAAKT,QAAlB,EAA4BR,QAA5B;WACE,KAAKQ,QAAP,EAAiBkG,GAAjB,CAAqBzG,SAArB;WACKO,QAAL,GAAgB,IAAhB;WACKqN,KAAL,GAAa,IAAb;;UACI,KAAKD,OAAL,KAAiB,IAArB,EAA2B;aACpBA,OAAL,CAAaoB,OAAb;;aACKpB,OAAL,GAAe,IAAf;;KAtLiB;;WA0LrBqB,MA1LqB,qBA0LZ;WACFlB,SAAL,GAAiB,KAAKC,aAAL,EAAjB;;UACI,KAAKJ,OAAL,KAAiB,IAArB,EAA2B;aACpBA,OAAL,CAAasB,cAAb;;KA7LiB;;;WAmMrB/J,kBAnMqB,iCAmMA;;;WACjB,KAAK3E,QAAP,EAAiB+B,EAAjB,CAAoBjC,MAAM6O,KAA1B,EAAiC,UAAChT,KAAD,EAAW;cACpCmG,cAAN;cACM8M,eAAN;;cACKvM,MAAL;OAHF;KApMmB;;WA2MrBmC,UA3MqB,uBA2MVjG,MA3MU,EA2MF;4BAEZ,KAAKsQ,WAAL,CAAiB/K,OADtB,EAEK9I,KAAE,KAAKgF,QAAP,EAAiB2B,IAAjB,EAFL,EAGKpD,MAHL;WAMK4H,eAAL,CACE7G,IADF,EAEEf,MAFF,EAGE,KAAKsQ,WAAL,CAAiB9K,WAHnB;aAMOxF,MAAP;KAxNmB;;WA2NrB+O,eA3NqB,8BA2NH;UACZ,CAAC,KAAKD,KAAV,EAAiB;YACT1M,SAASgM,SAASgB,qBAAT,CAA+B,KAAK3N,QAApC,CAAf;;aACKqN,KAAL,GAAarS,KAAE2F,MAAF,EAAU5C,IAAV,CAAe8B,SAASiP,IAAxB,EAA8B,CAA9B,CAAb;;;aAEK,KAAKzB,KAAZ;KAhOmB;;WAmOrB0B,aAnOqB,4BAmOL;UACRC,kBAAkBhU,KAAE,KAAKgF,QAAP,EAAiBW,MAAjB,EAAxB;UACIsO,YAAY9B,cAAc+B,MAA9B,CAFc;;UAKVF,gBAAgB9N,QAAhB,CAAyBnB,UAAUiO,MAAnC,CAAJ,EAAgD;oBAClCb,cAAcgC,GAA1B;;YACInU,KAAE,KAAKqS,KAAP,EAAcnM,QAAd,CAAuBnB,UAAUmO,SAAjC,CAAJ,EAAiD;sBACnCf,cAAciC,MAA1B;;OAHJ,MAKO,IAAIJ,gBAAgB9N,QAAhB,CAAyBnB,UAAUsP,SAAnC,CAAJ,EAAmD;oBAC5ClC,cAAcxE,KAA1B;OADK,MAEA,IAAIqG,gBAAgB9N,QAAhB,CAAyBnB,UAAUuP,QAAnC,CAAJ,EAAkD;oBAC3CnC,cAAczE,IAA1B;OADK,MAEA,IAAI1N,KAAE,KAAKqS,KAAP,EAAcnM,QAAd,CAAuBnB,UAAUmO,SAAjC,CAAJ,EAAiD;oBAC1Cf,cAAcoC,SAA1B;;;aAEKN,SAAP;KApPmB;;WAuPrBzB,aAvPqB,4BAuPL;aACPxS,KAAE,KAAKgF,QAAP,EAAiBY,OAAjB,CAAyB,SAAzB,EAAoC5C,MAApC,GAA6C,CAApD;KAxPmB;;WA2PrBqQ,gBA3PqB,+BA2PF;;;UACXmB,aAAa,EAAnB;;UACI,OAAO,KAAKjL,OAAL,CAAakL,MAApB,KAA+B,UAAnC,EAA+C;mBAClC5S,EAAX,GAAgB,UAAC8E,IAAD,EAAU;eACnB+N,OAAL,gBACK/N,KAAK+N,OADV,EAEK,OAAKnL,OAAL,CAAakL,MAAb,CAAoB9N,KAAK+N,OAAzB,KAAqC,EAF1C;iBAIO/N,IAAP;SALF;OADF,MAQO;mBACM8N,MAAX,GAAoB,KAAKlL,OAAL,CAAakL,MAAjC;;;UAEIE,eAAe;mBACR,KAAKZ,aAAL,EADQ;mBAER;kBACDS,UADC;gBAEH;qBACK,KAAKjL,OAAL,CAAaqL;WAHf;2BAKQ;+BACI,KAAKrL,OAAL,CAAa4J;;;OARtC;aAaOwB,YAAP;KArRmB;;;aA0RdnO,gBA1Rc,6BA0RGjD,MA1RH,EA0RW;aACvB,KAAKkD,IAAL,CAAU,YAAY;YACvBE,OAAO3G,KAAE,IAAF,EAAQ2G,IAAR,CAAanC,QAAb,CAAX;;YACM+E,UAAU,OAAOhG,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC,IAAtD;;YAEI,CAACoD,IAAL,EAAW;iBACF,IAAIgL,QAAJ,CAAa,IAAb,EAAmBpI,OAAnB,CAAP;eACE,IAAF,EAAQ5C,IAAR,CAAanC,QAAb,EAAuBmC,IAAvB;;;YAGE,OAAOpD,MAAP,KAAkB,QAAtB,EAAgC;cAC1B,OAAOoD,KAAKpD,MAAL,CAAP,KAAwB,WAA5B,EAAyC;kBACjC,IAAIyK,SAAJ,wBAAkCzK,MAAlC,QAAN;;;eAEGA,MAAL;;OAbG,CAAP;KA3RmB;;aA6SdsP,WA7Sc,wBA6SFlS,KA7SE,EA6SK;UACpBA,UAAUA,MAAMkL,KAAN,KAAgBoG,wBAAhB,IACZtR,MAAMgH,IAAN,KAAe,OAAf,IAA0BhH,MAAMkL,KAAN,KAAgBiG,WADxC,CAAJ,EAC0D;;;;UAIpD+C,UAAU7U,KAAE8L,SAAF,CAAY9L,KAAE6E,SAAS2C,WAAX,CAAZ,CAAhB;;WACK,IAAIsH,IAAI,CAAb,EAAgBA,IAAI+F,QAAQ7R,MAA5B,EAAoC8L,GAApC,EAAyC;YACjCnJ,SAASgM,SAASgB,qBAAT,CAA+BkC,QAAQ/F,CAAR,CAA/B,CAAf;;YACMgG,UAAU9U,KAAE6U,QAAQ/F,CAAR,CAAF,EAAcnI,IAAd,CAAmBnC,QAAnB,CAAhB;YACMkI,gBAAgB;yBACLmI,QAAQ/F,CAAR;SADjB;;YAII,CAACgG,OAAL,EAAc;;;;YAIRC,eAAeD,QAAQzC,KAA7B;;YACI,CAACrS,KAAE2F,MAAF,EAAUO,QAAV,CAAmBnB,UAAUkB,IAA7B,CAAL,EAAyC;;;;YAIrCtF,UAAUA,MAAMgH,IAAN,KAAe,OAAf,IACV,kBAAkBzD,IAAlB,CAAuBvD,MAAMC,MAAN,CAAagL,OAApC,CADU,IACsCjL,MAAMgH,IAAN,KAAe,OAAf,IAA0BhH,MAAMkL,KAAN,KAAgBiG,WAD1F,KAEA9R,KAAEiI,QAAF,CAAWtC,MAAX,EAAmBhF,MAAMC,MAAzB,CAFJ,EAEsC;;;;YAIhCoU,YAAYhV,KAAE8E,KAAF,CAAQA,MAAM4L,IAAd,EAAoBhE,aAApB,CAAlB;aACE/G,MAAF,EAAUxC,OAAV,CAAkB6R,SAAlB;;YACIA,UAAU1P,kBAAV,EAAJ,EAAoC;;SAxBG;;;;YA8BnC,kBAAkB7C,SAASgJ,eAA/B,EAAgD;eAC5C,MAAF,EAAUyB,QAAV,GAAqBhC,GAArB,CAAyB,WAAzB,EAAsC,IAAtC,EAA4ClL,KAAEuT,IAA9C;;;gBAGMzE,CAAR,EAAW3G,YAAX,CAAwB,eAAxB,EAAyC,OAAzC;aAEE4M,YAAF,EAAgB/O,WAAhB,CAA4BjB,UAAUkB,IAAtC;aACEN,MAAF,EACGK,WADH,CACejB,UAAUkB,IADzB,EAEG9C,OAFH,CAEWnD,KAAE8E,KAAF,CAAQA,MAAM+L,MAAd,EAAsBnE,aAAtB,CAFX;;KAzViB;;aA+VdiG,qBA/Vc,kCA+VQhQ,OA/VR,EA+ViB;UAChCgD,MAAJ;UACMzD,WAAWnC,KAAK2F,sBAAL,CAA4B/C,OAA5B,CAAjB;;UAEIT,QAAJ,EAAc;iBACHlC,KAAEkC,QAAF,EAAY,CAAZ,CAAT;;;aAGKyD,UAAUhD,QAAQsS,UAAzB;KAvWmB;;;aA2WdC,sBA3Wc,mCA2WSvU,KA3WT,EA2WgB;;;;;;;;UAQ/B,kBAAkBuD,IAAlB,CAAuBvD,MAAMC,MAAN,CAAagL,OAApC,IACAjL,MAAMkL,KAAN,KAAgBgG,aAAhB,IAAiClR,MAAMkL,KAAN,KAAgB+F,cAAhB,KAClCjR,MAAMkL,KAAN,KAAgBmG,kBAAhB,IAAsCrR,MAAMkL,KAAN,KAAgBkG,gBAAtD,IACC/R,KAAEW,MAAMC,MAAR,EAAgBgF,OAAhB,CAAwBf,SAASiP,IAAjC,EAAuC9Q,MAFN,CADjC,GAGiD,CAACkP,eAAehO,IAAf,CAAoBvD,MAAMkL,KAA1B,CAHtD,EAGwF;;;;YAIlF/E,cAAN;YACM8M,eAAN;;UAEI,KAAKnB,QAAL,IAAiBzS,KAAE,IAAF,EAAQkG,QAAR,CAAiBnB,UAAU2N,QAA3B,CAArB,EAA2D;;;;UAIrD/M,SAAWgM,SAASgB,qBAAT,CAA+B,IAA/B,CAAjB;;UACMC,WAAW5S,KAAE2F,MAAF,EAAUO,QAAV,CAAmBnB,UAAUkB,IAA7B,CAAjB;;UAEI,CAAC2M,QAAD,KAAcjS,MAAMkL,KAAN,KAAgB+F,cAAhB,IAAkCjR,MAAMkL,KAAN,KAAgBgG,aAAhE,KACCe,aAAajS,MAAMkL,KAAN,KAAgB+F,cAAhB,IAAkCjR,MAAMkL,KAAN,KAAgBgG,aAA/D,CADL,EACoF;YAC9ElR,MAAMkL,KAAN,KAAgB+F,cAApB,EAAoC;cAC5BvK,SAASrH,KAAE2F,MAAF,EAAU5C,IAAV,CAAe8B,SAAS2C,WAAxB,EAAqC,CAArC,CAAf;eACEH,MAAF,EAAUlE,OAAV,CAAkB,OAAlB;;;aAGA,IAAF,EAAQA,OAAR,CAAgB,OAAhB;;;;UAIIgS,QAAQnV,KAAE2F,MAAF,EAAU5C,IAAV,CAAe8B,SAASuQ,aAAxB,EAAuCC,GAAvC,EAAd;;UAEIF,MAAMnS,MAAN,KAAiB,CAArB,EAAwB;;;;UAIpB4H,QAAQuK,MAAMnJ,OAAN,CAAcrL,MAAMC,MAApB,CAAZ;;UAEID,MAAMkL,KAAN,KAAgBkG,gBAAhB,IAAoCnH,QAAQ,CAAhD,EAAmD;;;;;UAI/CjK,MAAMkL,KAAN,KAAgBmG,kBAAhB,IAAsCpH,QAAQuK,MAAMnS,MAAN,GAAe,CAAjE,EAAoE;;;;;UAIhE4H,QAAQ,CAAZ,EAAe;gBACL,CAAR;;;YAGIA,KAAN,EAAa1C,KAAb;KAnamB;;;;0BA6FA;eACZ3D,OAAP;;;;0BAGmB;eACZuE,OAAP;;;;0BAGuB;eAChBC,WAAP;;;;;;;;;;;;OAuUFtG,QAAF,EACGsE,EADH,CACMjC,MAAMwQ,gBADZ,EAC8BzQ,SAAS2C,WADvC,EACoDmK,SAASuD,sBAD7D,EAEGnO,EAFH,CAEMjC,MAAMwQ,gBAFZ,EAE8BzQ,SAASiP,IAFvC,EAE6CnC,SAASuD,sBAFtD,EAGGnO,EAHH,CAGSjC,MAAMkC,cAHf,SAGiClC,MAAMyQ,cAHvC,EAGyD5D,SAASkB,WAHlE,EAIG9L,EAJH,CAIMjC,MAAMkC,cAJZ,EAI4BnC,SAAS2C,WAJrC,EAIkD,UAAU7G,KAAV,EAAiB;UACzDmG,cAAN;UACM8M,eAAN;;aACSpN,gBAAT,CAA0BlG,IAA1B,CAA+BN,KAAE,IAAF,CAA/B,EAAwC,QAAxC;GAPJ,EASG+G,EATH,CASMjC,MAAMkC,cATZ,EAS4BnC,SAAS2Q,UATrC,EASiD,UAACC,CAAD,EAAO;MAClD7B,eAAF;GAVJ;;;;;;;OAmBE/R,EAAF,CAAKyC,IAAL,IAAaqN,SAASnL,gBAAtB;OACE3E,EAAF,CAAKyC,IAAL,EAAW4C,WAAX,GAAyByK,QAAzB;;OACE9P,EAAF,CAAKyC,IAAL,EAAW6C,UAAX,GAAwB,YAAY;SAChCtF,EAAF,CAAKyC,IAAL,IAAaK,kBAAb;WACOgN,SAASnL,gBAAhB;GAFF;;SAKOmL,QAAP;CAvce,CAwcd3R,CAxcc,EAwcX+S,MAxcW,CAAjB;;ACRA;;;;;;;AAOA,IAAM2C,QAAS,UAAC1V,IAAD,EAAO;;;;;;MAOdsE,OAA+B,OAArC;MACMC,UAA+B,OAArC;MACMC,WAA+B,UAArC;MACMC,kBAAmCD,QAAzC;MACME,eAA+B,WAArC;MACMC,qBAA+B3E,KAAE6B,EAAF,CAAKyC,IAAL,CAArC;MACMM,sBAA+B,GAArC;MACM+Q,+BAA+B,GAArC;MACM/D,iBAA+B,EAArC,CAfoB;;MAiBd9I,UAAU;cACH,IADG;cAEH,IAFG;WAGH,IAHG;UAIH;GAJb;MAOMC,cAAc;cACP,kBADO;cAEP,SAFO;WAGP,SAHO;UAIP;GAJb;MAOMjE,QAAQ;mBACeL,SADf;uBAEiBA,SAFjB;mBAGeA,SAHf;qBAIgBA,SAJhB;yBAKkBA,SALlB;uBAMiBA,SANjB;qCAOwBA,SAPxB;yCAQ0BA,SAR1B;yCAS0BA,SAT1B;6CAU4BA,SAV5B;8BAWgBA,SAA5B,GAAwCC;GAX1C;MAcMK,YAAY;wBACK,yBADL;cAEK,gBAFL;UAGK,YAHL;UAIK,MAJL;UAKK;GALvB;MAQMF,WAAW;YACM,eADN;iBAEM,uBAFN;kBAGM,wBAHN;mBAIM,mDAJN;oBAKM,aALN;oBAMM;;;;;;;GANvB;;MAeM6Q,KApEc;;;mBAqEN/S,OAAZ,EAAqBY,MAArB,EAA6B;WACtBgG,OAAL,GAA4B,KAAKC,UAAL,CAAgBjG,MAAhB,CAA5B;WACKyB,QAAL,GAA4BrC,OAA5B;WACKiT,OAAL,GAA4B5V,KAAE2C,OAAF,EAAWI,IAAX,CAAgB8B,SAASgR,MAAzB,EAAiC,CAAjC,CAA5B;WACKC,SAAL,GAA4B,IAA5B;WACKC,QAAL,GAA4B,KAA5B;WACKC,kBAAL,GAA4B,KAA5B;WACKC,oBAAL,GAA4B,KAA5B;WACKC,oBAAL,GAA4B,CAA5B;WACKC,eAAL,GAA4B,CAA5B;KA9EgB;;;;;;WA6FlB9O,MA7FkB,mBA6FXqF,aA7FW,EA6FI;aACb,KAAKqJ,QAAL,GAAgB,KAAKzG,IAAL,EAAhB,GAA8B,KAAKC,IAAL,CAAU7C,aAAV,CAArC;KA9FgB;;WAiGlB6C,IAjGkB,iBAiGb7C,aAjGa,EAiGE;;;UACd,KAAKgC,gBAAL,IAAyB,KAAKqH,QAAlC,EAA4C;;;;UAIxChW,KAAKgC,qBAAL,MAAgC/B,KAAE,KAAKgF,QAAP,EAAiBkB,QAAjB,CAA0BnB,UAAUoB,IAApC,CAApC,EAA+E;aACxEuI,gBAAL,GAAwB,IAAxB;;;UAGIoE,YAAY9S,KAAE8E,KAAF,CAAQA,MAAMmB,IAAd,EAAoB;;OAApB,CAAlB;WAIE,KAAKjB,QAAP,EAAiB7B,OAAjB,CAAyB2P,SAAzB;;UAEI,KAAKiD,QAAL,IAAiBjD,UAAUxN,kBAAV,EAArB,EAAqD;;;;WAIhDyQ,QAAL,GAAgB,IAAhB;;WAEKK,eAAL;;WACKC,aAAL;;WAEKC,aAAL;;WAEE7T,SAAS8T,IAAX,EAAiBpJ,QAAjB,CAA0BpI,UAAUyR,IAApC;;WAEKC,eAAL;;WACKC,eAAL;;WAEE,KAAK1R,QAAP,EAAiB+B,EAAjB,CACEjC,MAAM6R,aADR,EAEE9R,SAAS+R,YAFX,EAGE,UAACjW,KAAD;eAAW,MAAK2O,IAAL,CAAU3O,KAAV,CAAX;OAHF;WAME,KAAKiV,OAAP,EAAgB7O,EAAhB,CAAmBjC,MAAM+R,iBAAzB,EAA4C,YAAM;aAC9C,MAAK7R,QAAP,EAAiBvD,GAAjB,CAAqBqD,MAAMgS,eAA3B,EAA4C,UAACnW,KAAD,EAAW;cACjDX,KAAEW,MAAMC,MAAR,EAAgBC,EAAhB,CAAmB,MAAKmE,QAAxB,CAAJ,EAAuC;kBAChCiR,oBAAL,GAA4B,IAA5B;;SAFJ;OADF;;WAQKc,aAAL,CAAmB;eAAM,MAAKC,YAAL,CAAkBtK,aAAlB,CAAN;OAAnB;KA9IgB;;WAiJlB4C,IAjJkB,iBAiJb3O,KAjJa,EAiJN;;;UACNA,KAAJ,EAAW;cACHmG,cAAN;;;UAGE,KAAK4H,gBAAL,IAAyB,CAAC,KAAKqH,QAAnC,EAA6C;;;;UAIvCf,YAAYhV,KAAE8E,KAAF,CAAQA,MAAM4L,IAAd,CAAlB;WAEE,KAAK1L,QAAP,EAAiB7B,OAAjB,CAAyB6R,SAAzB;;UAEI,CAAC,KAAKe,QAAN,IAAkBf,UAAU1P,kBAAV,EAAtB,EAAsD;;;;WAIjDyQ,QAAL,GAAgB,KAAhB;UAEM9V,aAAaF,KAAKgC,qBAAL,MAAgC/B,KAAE,KAAKgF,QAAP,EAAiBkB,QAAjB,CAA0BnB,UAAUoB,IAApC,CAAnD;;UAEIlG,UAAJ,EAAgB;aACTyO,gBAAL,GAAwB,IAAxB;;;WAGG+H,eAAL;;WACKC,eAAL;;WAEEjU,QAAF,EAAYyI,GAAZ,CAAgBpG,MAAMmS,OAAtB;WAEE,KAAKjS,QAAP,EAAiBgB,WAAjB,CAA6BjB,UAAUkB,IAAvC;WAEE,KAAKjB,QAAP,EAAiBkG,GAAjB,CAAqBpG,MAAM6R,aAA3B;WACE,KAAKf,OAAP,EAAgB1K,GAAhB,CAAoBpG,MAAM+R,iBAA1B;;UAEI5W,UAAJ,EAAgB;aACZ,KAAK+E,QAAP,EACGvD,GADH,CACO1B,KAAK2B,cADZ,EAC4B,UAACf,KAAD;iBAAW,OAAKuW,UAAL,CAAgBvW,KAAhB,CAAX;SAD5B,EAEGmB,oBAFH,CAEwB8C,mBAFxB;OADF,MAIO;aACAsS,UAAL;;KAzLc;;WA6LlB1R,OA7LkB,sBA6LR;WACNC,UAAF,CAAa,KAAKT,QAAlB,EAA4BR,QAA5B;WAEEpD,MAAF,EAAUqB,QAAV,EAAoB,KAAKuC,QAAzB,EAAmC,KAAK8Q,SAAxC,EAAmD5K,GAAnD,CAAuDzG,SAAvD;WAEK8E,OAAL,GAA4B,IAA5B;WACKvE,QAAL,GAA4B,IAA5B;WACK4Q,OAAL,GAA4B,IAA5B;WACKE,SAAL,GAA4B,IAA5B;WACKC,QAAL,GAA4B,IAA5B;WACKC,kBAAL,GAA4B,IAA5B;WACKC,oBAAL,GAA4B,IAA5B;WACKE,eAAL,GAA4B,IAA5B;KAzMgB;;WA4MlBgB,YA5MkB,2BA4MH;WACRb,aAAL;KA7MgB;;;WAkNlB9M,UAlNkB,uBAkNPjG,MAlNO,EAkNC;4BAEZuF,OADL,EAEKvF,MAFL;WAIK4H,eAAL,CAAqB7G,IAArB,EAA2Bf,MAA3B,EAAmCwF,WAAnC;aACOxF,MAAP;KAxNgB;;WA2NlByT,YA3NkB,yBA2NLtK,aA3NK,EA2NU;;;UACpBzM,aAAaF,KAAKgC,qBAAL,MACjB/B,KAAE,KAAKgF,QAAP,EAAiBkB,QAAjB,CAA0BnB,UAAUoB,IAApC,CADF;;UAGI,CAAC,KAAKnB,QAAL,CAAciQ,UAAf,IACD,KAAKjQ,QAAL,CAAciQ,UAAd,CAAyB5R,QAAzB,KAAsC+T,KAAKC,YAD9C,EAC4D;;iBAEjDd,IAAT,CAAce,WAAd,CAA0B,KAAKtS,QAA/B;;;WAGGA,QAAL,CAAciL,KAAd,CAAoBsH,OAApB,GAA8B,OAA9B;;WACKvS,QAAL,CAAcwS,eAAd,CAA8B,aAA9B;;WACKxS,QAAL,CAAcyS,SAAd,GAA0B,CAA1B;;UAEIxX,UAAJ,EAAgB;aACT4N,MAAL,CAAY,KAAK7I,QAAjB;;;WAGA,KAAKA,QAAP,EAAiBmI,QAAjB,CAA0BpI,UAAUkB,IAApC;;UAEI,KAAKsD,OAAL,CAAarB,KAAjB,EAAwB;aACjBwP,aAAL;;;UAGIC,aAAa3X,KAAE8E,KAAF,CAAQA,MAAMwL,KAAd,EAAqB;;OAArB,CAAnB;;UAIMsH,qBAAqB,SAArBA,kBAAqB,GAAM;YAC3B,OAAKrO,OAAL,CAAarB,KAAjB,EAAwB;iBACjBlD,QAAL,CAAckD,KAAd;;;eAEGwG,gBAAL,GAAwB,KAAxB;aACE,OAAK1J,QAAP,EAAiB7B,OAAjB,CAAyBwU,UAAzB;OALF;;UAQI1X,UAAJ,EAAgB;aACZ,KAAK2V,OAAP,EACGnU,GADH,CACO1B,KAAK2B,cADZ,EAC4BkW,kBAD5B,EAEG9V,oBAFH,CAEwB8C,mBAFxB;OADF,MAIO;;;KAnQS;;WAwQlB8S,aAxQkB,4BAwQF;;;WACZjV,QAAF,EACGyI,GADH,CACOpG,MAAMmS,OADb;OAEGlQ,EAFH,CAEMjC,MAAMmS,OAFZ,EAEqB,UAACtW,KAAD,EAAW;YACxB8B,aAAa9B,MAAMC,MAAnB,IACA,OAAKoE,QAAL,KAAkBrE,MAAMC,MADxB,IAEAZ,KAAE,OAAKgF,QAAP,EAAiB6S,GAAjB,CAAqBlX,MAAMC,MAA3B,EAAmCoC,MAAnC,KAA8C,CAFlD,EAEqD;iBAC9CgC,QAAL,CAAckD,KAAd;;OANN;KAzQgB;;WAoRlBuO,eApRkB,8BAoRA;;;UACZ,KAAKV,QAAL,IAAiB,KAAKxM,OAAL,CAAa6B,QAAlC,EAA4C;aACxC,KAAKpG,QAAP,EAAiB+B,EAAjB,CAAoBjC,MAAMgT,eAA1B,EAA2C,UAACnX,KAAD,EAAW;cAChDA,MAAMkL,KAAN,KAAgB+F,cAApB,EAAoC;kBAC5B9K,cAAN;;mBACKwI,IAAL;;SAHJ;OADF,MAOO,IAAI,CAAC,KAAKyG,QAAV,EAAoB;aACvB,KAAK/Q,QAAP,EAAiBkG,GAAjB,CAAqBpG,MAAMgT,eAA3B;;KA7Rc;;WAiSlBpB,eAjSkB,8BAiSA;;;UACZ,KAAKX,QAAT,EAAmB;aACf3U,MAAF,EAAU2F,EAAV,CAAajC,MAAMiT,MAAnB,EAA2B,UAACpX,KAAD;iBAAW,OAAKwW,YAAL,CAAkBxW,KAAlB,CAAX;SAA3B;OADF,MAEO;aACHS,MAAF,EAAU8J,GAAV,CAAcpG,MAAMiT,MAApB;;KArSc;;WAySlBb,UAzSkB,yBAySL;;;WACNlS,QAAL,CAAciL,KAAd,CAAoBsH,OAApB,GAA8B,MAA9B;;WACKvS,QAAL,CAAcmD,YAAd,CAA2B,aAA3B,EAA0C,IAA1C;;WACKuG,gBAAL,GAAwB,KAAxB;;WACKqI,aAAL,CAAmB,YAAM;aACrBtU,SAAS8T,IAAX,EAAiBvQ,WAAjB,CAA6BjB,UAAUyR,IAAvC;;eACKwB,iBAAL;;eACKC,eAAL;;aACE,OAAKjT,QAAP,EAAiB7B,OAAjB,CAAyB2B,MAAM+L,MAA/B;OAJF;KA7SgB;;WAqTlBqH,eArTkB,8BAqTA;UACZ,KAAKpC,SAAT,EAAoB;aAChB,KAAKA,SAAP,EAAkBvP,MAAlB;aACKuP,SAAL,GAAiB,IAAjB;;KAxTc;;WA4TlBiB,aA5TkB,0BA4TJoB,QA5TI,EA4TM;;;UAChBC,UAAUpY,KAAE,KAAKgF,QAAP,EAAiBkB,QAAjB,CAA0BnB,UAAUoB,IAApC,IACZpB,UAAUoB,IADE,GACK,EADrB;;UAGI,KAAK4P,QAAL,IAAiB,KAAKxM,OAAL,CAAa8O,QAAlC,EAA4C;YACpCC,YAAYvY,KAAKgC,qBAAL,MAAgCqW,OAAlD;aAEKtC,SAAL,GAAiBrT,SAAS8V,aAAT,CAAuB,KAAvB,CAAjB;aACKzC,SAAL,CAAe0C,SAAf,GAA2BzT,UAAU0T,QAArC;;YAEIL,OAAJ,EAAa;eACT,KAAKtC,SAAP,EAAkB3I,QAAlB,CAA2BiL,OAA3B;;;aAGA,KAAKtC,SAAP,EAAkB4C,QAAlB,CAA2BjW,SAAS8T,IAApC;aAEE,KAAKvR,QAAP,EAAiB+B,EAAjB,CAAoBjC,MAAM6R,aAA1B,EAAyC,UAAChW,KAAD,EAAW;cAC9C,OAAKsV,oBAAT,EAA+B;mBACxBA,oBAAL,GAA4B,KAA5B;;;;cAGEtV,MAAMC,MAAN,KAAiBD,MAAM4Q,aAA3B,EAA0C;;;;cAGtC,OAAKhI,OAAL,CAAa8O,QAAb,KAA0B,QAA9B,EAAwC;mBACjCrT,QAAL,CAAckD,KAAd;WADF,MAEO;mBACAoH,IAAL;;SAXJ;;YAeIgJ,SAAJ,EAAe;eACRzK,MAAL,CAAY,KAAKiI,SAAjB;;;aAGA,KAAKA,SAAP,EAAkB3I,QAAlB,CAA2BpI,UAAUkB,IAArC;;YAEI,CAACkS,QAAL,EAAe;;;;YAIX,CAACG,SAAL,EAAgB;;;;;aAKd,KAAKxC,SAAP,EACGrU,GADH,CACO1B,KAAK2B,cADZ,EAC4ByW,QAD5B,EAEGrW,oBAFH,CAEwB6T,4BAFxB;OA1CF,MA6CO,IAAI,CAAC,KAAKI,QAAN,IAAkB,KAAKD,SAA3B,EAAsC;aACzC,KAAKA,SAAP,EAAkB9P,WAAlB,CAA8BjB,UAAUkB,IAAxC;;YAEM0S,iBAAiB,SAAjBA,cAAiB,GAAM;iBACtBT,eAAL;;cACIC,QAAJ,EAAc;;;SAFhB;;YAOIpY,KAAKgC,qBAAL,MACD/B,KAAE,KAAKgF,QAAP,EAAiBkB,QAAjB,CAA0BnB,UAAUoB,IAApC,CADH,EAC8C;eAC1C,KAAK2P,SAAP,EACGrU,GADH,CACO1B,KAAK2B,cADZ,EAC4BiX,cAD5B,EAEG7W,oBAFH,CAEwB6T,4BAFxB;SAFF,MAKO;;;OAfF,MAkBA,IAAIwC,QAAJ,EAAc;;;KA/XL;;;;;;WAyYlB7B,aAzYkB,4BAyYF;UACRsC,qBACJ,KAAK5T,QAAL,CAAc6T,YAAd,GAA6BpW,SAASgJ,eAAT,CAAyBqN,YADxD;;UAGI,CAAC,KAAK9C,kBAAN,IAA4B4C,kBAAhC,EAAoD;aAC7C5T,QAAL,CAAciL,KAAd,CAAoB8I,WAApB,GAAqC,KAAK5C,eAA1C;;;UAGE,KAAKH,kBAAL,IAA2B,CAAC4C,kBAAhC,EAAoD;aAC7C5T,QAAL,CAAciL,KAAd,CAAoB+I,YAApB,GAAsC,KAAK7C,eAA3C;;KAlZc;;WAsZlB6B,iBAtZkB,gCAsZE;WACbhT,QAAL,CAAciL,KAAd,CAAoB8I,WAApB,GAAkC,EAAlC;WACK/T,QAAL,CAAciL,KAAd,CAAoB+I,YAApB,GAAmC,EAAnC;KAxZgB;;WA2ZlB5C,eA3ZkB,8BA2ZA;UACV6C,OAAOxW,SAAS8T,IAAT,CAAc5F,qBAAd,EAAb;WACKqF,kBAAL,GAA0BiD,KAAKC,IAAL,GAAYD,KAAKE,KAAjB,GAAyB/X,OAAOgY,UAA1D;WACKjD,eAAL,GAAuB,KAAKkD,kBAAL,EAAvB;KA9ZgB;;WAialBhD,aAjakB,4BAiaF;;;UACV,KAAKL,kBAAT,EAA6B;;;;aAKzBnR,SAASyU,aAAX,EAA0B7S,IAA1B,CAA+B,UAACmE,KAAD,EAAQjI,OAAR,EAAoB;cAC3C4W,gBAAgBvZ,KAAE2C,OAAF,EAAW,CAAX,EAAcsN,KAAd,CAAoB+I,YAA1C;cACMQ,oBAAoBxZ,KAAE2C,OAAF,EAAWsH,GAAX,CAAe,eAAf,CAA1B;eACEtH,OAAF,EAAWgE,IAAX,CAAgB,eAAhB,EAAiC4S,aAAjC,EAAgDtP,GAAhD,CAAoD,eAApD,EAAwEwP,WAAWD,iBAAX,IAAgC,OAAKrD,eAA7G;SAHF,EAL2B;;aAYzBtR,SAAS6U,cAAX,EAA2BjT,IAA3B,CAAgC,UAACmE,KAAD,EAAQjI,OAAR,EAAoB;cAC5CgX,eAAe3Z,KAAE2C,OAAF,EAAW,CAAX,EAAcsN,KAAd,CAAoB2J,WAAzC;cACMC,mBAAmB7Z,KAAE2C,OAAF,EAAWsH,GAAX,CAAe,cAAf,CAAzB;eACEtH,OAAF,EAAWgE,IAAX,CAAgB,cAAhB,EAAgCgT,YAAhC,EAA8C1P,GAA9C,CAAkD,cAAlD,EAAqEwP,WAAWI,gBAAX,IAA+B,OAAK1D,eAAzG;SAHF,EAZ2B;;aAmBzBtR,SAASiV,cAAX,EAA2BrT,IAA3B,CAAgC,UAACmE,KAAD,EAAQjI,OAAR,EAAoB;cAC5CgX,eAAe3Z,KAAE2C,OAAF,EAAW,CAAX,EAAcsN,KAAd,CAAoB2J,WAAzC;cACMC,mBAAmB7Z,KAAE2C,OAAF,EAAWsH,GAAX,CAAe,cAAf,CAAzB;eACEtH,OAAF,EAAWgE,IAAX,CAAgB,cAAhB,EAAgCgT,YAAhC,EAA8C1P,GAA9C,CAAkD,cAAlD,EAAqEwP,WAAWI,gBAAX,IAA+B,OAAK1D,eAAzG;SAHF,EAnB2B;;YA0BrBoD,gBAAgB9W,SAAS8T,IAAT,CAActG,KAAd,CAAoB+I,YAA1C;YACMQ,oBAAoBxZ,KAAE,MAAF,EAAUiK,GAAV,CAAc,eAAd,CAA1B;aACE,MAAF,EAAUtD,IAAV,CAAe,eAAf,EAAgC4S,aAAhC,EAA+CtP,GAA/C,CAAmD,eAAnD,EAAuEwP,WAAWD,iBAAX,IAAgC,KAAKrD,eAA5G;;KA9bc;;WAkclB8B,eAlckB,8BAkcA;;WAEdpT,SAASyU,aAAX,EAA0B7S,IAA1B,CAA+B,UAACmE,KAAD,EAAQjI,OAAR,EAAoB;YAC3CoX,UAAU/Z,KAAE2C,OAAF,EAAWgE,IAAX,CAAgB,eAAhB,CAAhB;;YACI,OAAOoT,OAAP,KAAmB,WAAvB,EAAoC;eAChCpX,OAAF,EAAWsH,GAAX,CAAe,eAAf,EAAgC8P,OAAhC,EAAyCtU,UAAzC,CAAoD,eAApD;;OAHJ,EAFgB;;WAUXZ,SAAS6U,cAAd,UAAiC7U,SAASiV,cAA1C,EAA4DrT,IAA5D,CAAiE,UAACmE,KAAD,EAAQjI,OAAR,EAAoB;YAC7EqX,SAASha,KAAE2C,OAAF,EAAWgE,IAAX,CAAgB,cAAhB,CAAf;;YACI,OAAOqT,MAAP,KAAkB,WAAtB,EAAmC;eAC/BrX,OAAF,EAAWsH,GAAX,CAAe,cAAf,EAA+B+P,MAA/B,EAAuCvU,UAAvC,CAAkD,cAAlD;;OAHJ,EAVgB;;UAkBVsU,UAAU/Z,KAAE,MAAF,EAAU2G,IAAV,CAAe,eAAf,CAAhB;;UACI,OAAOoT,OAAP,KAAmB,WAAvB,EAAoC;aAChC,MAAF,EAAU9P,GAAV,CAAc,eAAd,EAA+B8P,OAA/B,EAAwCtU,UAAxC,CAAmD,eAAnD;;KAtdc;;WA0dlB4T,kBA1dkB,iCA0dG;;UACbY,YAAYxX,SAAS8V,aAAT,CAAuB,KAAvB,CAAlB;gBACUC,SAAV,GAAsBzT,UAAUmV,kBAAhC;eACS3D,IAAT,CAAce,WAAd,CAA0B2C,SAA1B;UACME,iBAAiBF,UAAUtJ,qBAAV,GAAkCyJ,KAAlC,GAA0CH,UAAUI,WAA3E;eACS9D,IAAT,CAAc+D,WAAd,CAA0BL,SAA1B;aACOE,cAAP;KAhegB;;;UAqeX3T,gBAreW,6BAqeMjD,MAreN,EAqecmJ,aAred,EAqe6B;aACtC,KAAKjG,IAAL,CAAU,YAAY;YACvBE,OAAO3G,KAAE,IAAF,EAAQ2G,IAAR,CAAanC,QAAb,CAAX;;YACM+E,uBACDmM,MAAM5M,OADL,EAED9I,KAAE,IAAF,EAAQ2G,IAAR,EAFC,EAGD,OAAOpD,MAAP,KAAkB,QAAlB,IAA8BA,MAH7B,CAAN;;YAMI,CAACoD,IAAL,EAAW;iBACF,IAAI+O,KAAJ,CAAU,IAAV,EAAgBnM,OAAhB,CAAP;eACE,IAAF,EAAQ5C,IAAR,CAAanC,QAAb,EAAuBmC,IAAvB;;;YAGE,OAAOpD,MAAP,KAAkB,QAAtB,EAAgC;cAC1B,OAAOoD,KAAKpD,MAAL,CAAP,KAAwB,WAA5B,EAAyC;kBACjC,IAAIyK,SAAJ,wBAAkCzK,MAAlC,QAAN;;;eAEGA,MAAL,EAAamJ,aAAb;SAJF,MAKO,IAAInD,QAAQgG,IAAZ,EAAkB;eAClBA,IAAL,CAAU7C,aAAV;;OAnBG,CAAP;KAtegB;;;;0BAmFG;eACZnI,OAAP;;;;0BAGmB;eACZuE,OAAP;;;;;;;;;;;;OA6aFrG,QAAF,EAAYsE,EAAZ,CAAejC,MAAMkC,cAArB,EAAqCnC,SAAS2C,WAA9C,EAA2D,UAAU7G,KAAV,EAAiB;;;QACtEC,MAAJ;QACMsB,WAAWnC,KAAK2F,sBAAL,CAA4B,IAA5B,CAAjB;;QAEIxD,QAAJ,EAAc;eACHlC,KAAEkC,QAAF,EAAY,CAAZ,CAAT;;;QAGIqB,SAASvD,KAAEY,MAAF,EAAU+F,IAAV,CAAenC,QAAf,IACX,QADW,gBAERxE,KAAEY,MAAF,EAAU+F,IAAV,EAFQ,EAGR3G,KAAE,IAAF,EAAQ2G,IAAR,EAHQ,CAAf;;QAMI,KAAKiF,OAAL,KAAiB,GAAjB,IAAwB,KAAKA,OAAL,KAAiB,MAA7C,EAAqD;YAC7C9E,cAAN;;;QAGI2K,UAAUzR,KAAEY,MAAF,EAAUa,GAAV,CAAcqD,MAAMmB,IAApB,EAA0B,UAAC6M,SAAD,EAAe;UACnDA,UAAUxN,kBAAV,EAAJ,EAAoC;;;;;cAK5B7D,GAAR,CAAYqD,MAAM+L,MAAlB,EAA0B,YAAM;YAC1B7Q,cAAQa,EAAR,CAAW,UAAX,CAAJ,EAA4B;kBACrBqH,KAAL;;OAFJ;KANc,CAAhB;;UAaM1B,gBAAN,CAAuBlG,IAAvB,CAA4BN,KAAEY,MAAF,CAA5B,EAAuC2C,MAAvC,EAA+C,IAA/C;GA/BF;;;;;;;OAwCE1B,EAAF,CAAKyC,IAAL,IAAaoR,MAAMlP,gBAAnB;OACE3E,EAAF,CAAKyC,IAAL,EAAW4C,WAAX,GAAyBwO,KAAzB;;OACE7T,EAAF,CAAKyC,IAAL,EAAW6C,UAAX,GAAwB,YAAY;SAChCtF,EAAF,CAAKyC,IAAL,IAAaK,kBAAb;WACO+Q,MAAMlP,gBAAb;GAFF;;SAKOkP,KAAP;CApjBY,CAqjBX1V,CArjBW,CAAd;;ACNA;;;;;;;AAOA,IAAMua,UAAW,UAACva,IAAD,EAAO;;;;;;MAOhBsE,OAAsB,SAA5B;MACMC,UAAsB,OAA5B;MACMC,WAAsB,YAA5B;MACMC,kBAA0BD,QAAhC;MACMG,qBAAsB3E,KAAE6B,EAAF,CAAKyC,IAAL,CAA5B;MACMM,sBAAsB,GAA5B;MACM4V,eAAsB,YAA5B;MACMC,qBAAqB,IAAIxW,MAAJ,aAAqBuW,YAArB,WAAyC,GAAzC,CAA3B;MAEMzR,cAAc;eACI,SADJ;cAEI,QAFJ;WAGI,2BAHJ;aAII,QAJJ;WAKI,iBALJ;UAMI,SANJ;cAOI,kBAPJ;eAQI,mBARJ;YASI,iBATJ;eAUI,0BAVJ;uBAWI,gBAXJ;cAYI;GAZxB;MAeMoJ,gBAAgB;UACX,MADW;SAEX,KAFW;WAGX,OAHW;YAIX,QAJW;UAKX;GALX;MAQMrJ,UAAU;eACQ,IADR;cAEQ,yCACF,2BADE,GAEF,yCAJN;aAKQ,aALR;WAMQ,EANR;WAOQ,CAPR;UAQQ,KARR;cASQ,KATR;eAUQ,KAVR;YAWQ,CAXR;eAYQ,KAZR;uBAaQ,MAbR;cAcQ;GAdxB;MAiBM4R,aAAa;UACV,MADU;SAEV;GAFT;MAKM5V,QAAQ;mBACQL,SADR;uBAEUA,SAFV;mBAGQA,SAHR;qBAISA,SAJT;2BAKYA,SALZ;qBAMSA,SANT;yBAOWA,SAPX;2BAQYA,SARZ;+BAScA,SATd;+BAUcA;GAV5B;MAaMM,YAAY;UACT,MADS;UAET;GAFT;MAKMF,WAAW;aACC,UADD;mBAEC,gBAFD;WAGC;GAHlB;MAMM8V,UAAU;WACL,OADK;WAEL,OAFK;WAGL,OAHK;YAIL;;;;;;;GAJX;;MAcMJ,OAnGgB;;;qBAoGR5X,OAAZ,EAAqBY,MAArB,EAA6B;;;;;UAKvB,OAAOwP,MAAP,KAAkB,WAAtB,EAAmC;cAC3B,IAAI/E,SAAJ,CAAc,8DAAd,CAAN;OANyB;;;WAUtB4M,UAAL,GAAsB,IAAtB;WACKC,QAAL,GAAsB,CAAtB;WACKC,WAAL,GAAsB,EAAtB;WACKC,cAAL,GAAsB,EAAtB;WACK3I,OAAL,GAAsB,IAAtB,CAd2B;;WAiBtBzP,OAAL,GAAeA,OAAf;WACKY,MAAL,GAAe,KAAKiG,UAAL,CAAgBjG,MAAhB,CAAf;WACKyX,GAAL,GAAe,IAAf;;WAEKC,aAAL;KAzHkB;;;;;;WA4JpBC,MA5JoB,qBA4JX;WACFN,UAAL,GAAkB,IAAlB;KA7JkB;;WAgKpBO,OAhKoB,sBAgKV;WACHP,UAAL,GAAkB,KAAlB;KAjKkB;;WAoKpBQ,aApKoB,4BAoKJ;WACTR,UAAL,GAAkB,CAAC,KAAKA,UAAxB;KArKkB;;WAwKpBvT,MAxKoB,mBAwKb1G,KAxKa,EAwKN;UACR,CAAC,KAAKia,UAAV,EAAsB;;;;UAIlBja,KAAJ,EAAW;YACH0a,UAAU,KAAKxH,WAAL,CAAiBrP,QAAjC;YACIsQ,UAAU9U,KAAEW,MAAM4Q,aAAR,EAAuB5K,IAAvB,CAA4B0U,OAA5B,CAAd;;YAEI,CAACvG,OAAL,EAAc;oBACF,IAAI,KAAKjB,WAAT,CACRlT,MAAM4Q,aADE,EAER,KAAK+J,kBAAL,EAFQ,CAAV;eAIE3a,MAAM4Q,aAAR,EAAuB5K,IAAvB,CAA4B0U,OAA5B,EAAqCvG,OAArC;;;gBAGMiG,cAAR,CAAuBQ,KAAvB,GAA+B,CAACzG,QAAQiG,cAAR,CAAuBQ,KAAvD;;YAEIzG,QAAQ0G,oBAAR,EAAJ,EAAoC;kBAC1BC,MAAR,CAAe,IAAf,EAAqB3G,OAArB;SADF,MAEO;kBACG4G,MAAR,CAAe,IAAf,EAAqB5G,OAArB;;OAjBJ,MAmBO;YACD9U,KAAE,KAAK2b,aAAL,EAAF,EAAwBzV,QAAxB,CAAiCnB,UAAUkB,IAA3C,CAAJ,EAAsD;eAC/CyV,MAAL,CAAY,IAAZ,EAAkB,IAAlB;;;;;aAIGD,MAAL,CAAY,IAAZ,EAAkB,IAAlB;;KAtMgB;;WA0MpBjW,OA1MoB,sBA0MV;mBACK,KAAKqV,QAAlB;WAEEpV,UAAF,CAAa,KAAK9C,OAAlB,EAA2B,KAAKkR,WAAL,CAAiBrP,QAA5C;WAEE,KAAK7B,OAAP,EAAgBuI,GAAhB,CAAoB,KAAK2I,WAAL,CAAiBpP,SAArC;WACE,KAAK9B,OAAP,EAAgBiD,OAAhB,CAAwB,QAAxB,EAAkCsF,GAAlC,CAAsC,eAAtC;;UAEI,KAAK8P,GAAT,EAAc;aACV,KAAKA,GAAP,EAAYzU,MAAZ;;;WAGGqU,UAAL,GAAsB,IAAtB;WACKC,QAAL,GAAsB,IAAtB;WACKC,WAAL,GAAsB,IAAtB;WACKC,cAAL,GAAsB,IAAtB;;UACI,KAAK3I,OAAL,KAAiB,IAArB,EAA2B;aACpBA,OAAL,CAAaoB,OAAb;;;WAGGpB,OAAL,GAAe,IAAf;WACKzP,OAAL,GAAe,IAAf;WACKY,MAAL,GAAe,IAAf;WACKyX,GAAL,GAAe,IAAf;KAjOkB;;WAoOpBzL,IApOoB,mBAoOb;;;UACDvP,KAAE,KAAK2C,OAAP,EAAgBsH,GAAhB,CAAoB,SAApB,MAAmC,MAAvC,EAA+C;cACvC,IAAI9F,KAAJ,CAAU,qCAAV,CAAN;;;UAGI2O,YAAY9S,KAAE8E,KAAF,CAAQ,KAAK+O,WAAL,CAAiB/O,KAAjB,CAAuBmB,IAA/B,CAAlB;;UACI,KAAK2V,aAAL,MAAwB,KAAKhB,UAAjC,EAA6C;aACzC,KAAKjY,OAAP,EAAgBQ,OAAhB,CAAwB2P,SAAxB;YAEM+I,aAAa7b,KAAEiI,QAAF,CACjB,KAAKtF,OAAL,CAAamZ,aAAb,CAA2BrQ,eADV,EAEjB,KAAK9I,OAFY,CAAnB;;YAKImQ,UAAUxN,kBAAV,MAAkC,CAACuW,UAAvC,EAAmD;;;;YAI7Cb,MAAQ,KAAKW,aAAL,EAAd;YACMI,QAAQhc,KAAKic,MAAL,CAAY,KAAKnI,WAAL,CAAiBvP,IAA7B,CAAd;YAEI6D,YAAJ,CAAiB,IAAjB,EAAuB4T,KAAvB;aACKpZ,OAAL,CAAawF,YAAb,CAA0B,kBAA1B,EAA8C4T,KAA9C;aAEKE,UAAL;;YAEI,KAAK1Y,MAAL,CAAY2Y,SAAhB,EAA2B;eACvBlB,GAAF,EAAO7N,QAAP,CAAgBpI,UAAUoB,IAA1B;;;YAGI8N,YAAa,OAAO,KAAK1Q,MAAL,CAAY0Q,SAAnB,KAAiC,UAAjC,GACf,KAAK1Q,MAAL,CAAY0Q,SAAZ,CAAsB3T,IAAtB,CAA2B,IAA3B,EAAiC0a,GAAjC,EAAsC,KAAKrY,OAA3C,CADe,GAEf,KAAKY,MAAL,CAAY0Q,SAFhB;;YAIMkI,aAAa,KAAKC,cAAL,CAAoBnI,SAApB,CAAnB;;aACKoI,kBAAL,CAAwBF,UAAxB;YAEMG,YAAY,KAAK/Y,MAAL,CAAY+Y,SAAZ,KAA0B,KAA1B,GAAkC7Z,SAAS8T,IAA3C,GAAkDvW,KAAE,KAAKuD,MAAL,CAAY+Y,SAAd,CAApE;aAEEtB,GAAF,EAAOrU,IAAP,CAAY,KAAKkN,WAAL,CAAiBrP,QAA7B,EAAuC,IAAvC;;YAEI,CAACxE,KAAEiI,QAAF,CAAW,KAAKtF,OAAL,CAAamZ,aAAb,CAA2BrQ,eAAtC,EAAuD,KAAKuP,GAA5D,CAAL,EAAuE;eACnEA,GAAF,EAAOtC,QAAP,CAAgB4D,SAAhB;;;aAGA,KAAK3Z,OAAP,EAAgBQ,OAAhB,CAAwB,KAAK0Q,WAAL,CAAiB/O,KAAjB,CAAuByX,QAA/C;aAEKnK,OAAL,GAAe,IAAIW,MAAJ,CAAW,KAAKpQ,OAAhB,EAAyBqY,GAAzB,EAA8B;qBAChCmB,UADgC;qBAEhC;oBACD;sBACE,KAAK5Y,MAAL,CAAYkR;aAFb;kBAIH;wBACM,KAAKlR,MAAL,CAAYiZ;aALf;mBAOF;uBACI3X,SAAS4X;aARX;6BAUQ;iCACI,KAAKlZ,MAAL,CAAY4P;;WAbQ;oBAgBjC,kBAACxM,IAAD,EAAU;gBACdA,KAAK+V,iBAAL,KAA2B/V,KAAKsN,SAApC,EAA+C;oBACxC0I,4BAAL,CAAkChW,IAAlC;;WAlBuC;oBAqBjC,kBAACA,IAAD,EAAU;kBACbgW,4BAAL,CAAkChW,IAAlC;;SAtBW,CAAf;aA0BEqU,GAAF,EAAO7N,QAAP,CAAgBpI,UAAUkB,IAA1B,EAnE2C;;;;;YAyEvC,kBAAkBxD,SAASgJ,eAA/B,EAAgD;eAC5C,MAAF,EAAUyB,QAAV,GAAqBnG,EAArB,CAAwB,WAAxB,EAAqC,IAArC,EAA2C/G,KAAEuT,IAA7C;;;YAGIlD,WAAW,SAAXA,QAAW,GAAM;cACjB,MAAK9M,MAAL,CAAY2Y,SAAhB,EAA2B;kBACpBU,cAAL;;;cAEIC,iBAAiB,MAAK/B,WAA5B;gBACKA,WAAL,GAAuB,IAAvB;eAEE,MAAKnY,OAAP,EAAgBQ,OAAhB,CAAwB,MAAK0Q,WAAL,CAAiB/O,KAAjB,CAAuBwL,KAA/C;;cAEIuM,mBAAmBnC,WAAWoC,GAAlC,EAAuC;kBAChCpB,MAAL,CAAY,IAAZ;;SAVJ;;YAcI3b,KAAKgC,qBAAL,MAAgC/B,KAAE,KAAKgb,GAAP,EAAY9U,QAAZ,CAAqBnB,UAAUoB,IAA/B,CAApC,EAA0E;eACtE,KAAK6U,GAAP,EACGvZ,GADH,CACO1B,KAAK2B,cADZ,EAC4B2O,QAD5B,EAEGvO,oBAFH,CAEwByY,QAAQwC,oBAFhC;SADF,MAIO;;;;KAzUS;;WA+UpBzN,IA/UoB,iBA+Uf6I,QA/Ue,EA+UL;;;UACP6C,MAAY,KAAKW,aAAL,EAAlB;UACM3G,YAAYhV,KAAE8E,KAAF,CAAQ,KAAK+O,WAAL,CAAiB/O,KAAjB,CAAuB4L,IAA/B,CAAlB;;UACML,WAAW,SAAXA,QAAW,GAAM;YACjB,OAAKyK,WAAL,KAAqBJ,WAAWzU,IAAhC,IAAwC+U,IAAI/F,UAAhD,EAA4D;cACtDA,UAAJ,CAAeqF,WAAf,CAA2BU,GAA3B;;;eAGGgC,cAAL;;eACKra,OAAL,CAAa6U,eAAb,CAA6B,kBAA7B;;aACE,OAAK7U,OAAP,EAAgBQ,OAAhB,CAAwB,OAAK0Q,WAAL,CAAiB/O,KAAjB,CAAuB+L,MAA/C;;YACI,OAAKuB,OAAL,KAAiB,IAArB,EAA2B;iBACpBA,OAAL,CAAaoB,OAAb;;;YAGE2E,QAAJ,EAAc;;;OAZhB;;WAiBE,KAAKxV,OAAP,EAAgBQ,OAAhB,CAAwB6R,SAAxB;;UAEIA,UAAU1P,kBAAV,EAAJ,EAAoC;;;;WAIlC0V,GAAF,EAAOhV,WAAP,CAAmBjB,UAAUkB,IAA7B,EA1Ba;;;UA8BT,kBAAkBxD,SAASgJ,eAA/B,EAAgD;aAC5C,MAAF,EAAUyB,QAAV,GAAqBhC,GAArB,CAAyB,WAAzB,EAAsC,IAAtC,EAA4ClL,KAAEuT,IAA9C;;;WAGGwH,cAAL,CAAoBJ,QAAQhH,KAA5B,IAAqC,KAArC;WACKoH,cAAL,CAAoBJ,QAAQlS,KAA5B,IAAqC,KAArC;WACKsS,cAAL,CAAoBJ,QAAQsC,KAA5B,IAAqC,KAArC;;UAEIld,KAAKgC,qBAAL,MACA/B,KAAE,KAAKgb,GAAP,EAAY9U,QAAZ,CAAqBnB,UAAUoB,IAA/B,CADJ,EAC0C;aACtC6U,GAAF,EACGvZ,GADH,CACO1B,KAAK2B,cADZ,EAC4B2O,QAD5B,EAEGvO,oBAFH,CAEwB8C,mBAFxB;OAFF,MAKO;;;;WAIFkW,WAAL,GAAmB,EAAnB;KA9XkB;;WAiYpBrH,MAjYoB,qBAiYX;UACH,KAAKrB,OAAL,KAAiB,IAArB,EAA2B;aACpBA,OAAL,CAAasB,cAAb;;KAnYgB;;;WAyYpBkI,aAzYoB,4BAyYJ;aACPxY,QAAQ,KAAK8Z,QAAL,EAAR,CAAP;KA1YkB;;WA6YpBb,kBA7YoB,+BA6YDF,UA7YC,EA6YW;WAC3B,KAAKR,aAAL,EAAF,EAAwBxO,QAAxB,CAAoCqN,YAApC,SAAoD2B,UAApD;KA9YkB;;WAiZpBR,aAjZoB,4BAiZJ;WACTX,GAAL,GAAW,KAAKA,GAAL,IAAYhb,KAAE,KAAKuD,MAAL,CAAY4Z,QAAd,EAAwB,CAAxB,CAAvB;aACO,KAAKnC,GAAZ;KAnZkB;;WAsZpBiB,UAtZoB,yBAsZP;UACLmB,OAAOpd,KAAE,KAAK2b,aAAL,EAAF,CAAb;WACK0B,iBAAL,CAAuBD,KAAKra,IAAL,CAAU8B,SAASyY,aAAnB,CAAvB,EAA0D,KAAKJ,QAAL,EAA1D;WACKlX,WAAL,CAAoBjB,UAAUoB,IAA9B,SAAsCpB,UAAUkB,IAAhD;KAzZkB;;WA4ZpBoX,iBA5ZoB,8BA4ZF3W,QA5ZE,EA4ZQ6W,OA5ZR,EA4ZiB;UAC7BC,OAAO,KAAKja,MAAL,CAAYia,IAAzB;;UACI,OAAOD,OAAP,KAAmB,QAAnB,KAAgCA,QAAQla,QAAR,IAAoBka,QAAQrM,MAA5D,CAAJ,EAAyE;;YAEnEsM,IAAJ,EAAU;cACJ,CAACxd,KAAEud,OAAF,EAAW5X,MAAX,GAAoB9E,EAApB,CAAuB6F,QAAvB,CAAL,EAAuC;qBAC5B+W,KAAT,GAAiBC,MAAjB,CAAwBH,OAAxB;;SAFJ,MAIO;mBACII,IAAT,CAAc3d,KAAEud,OAAF,EAAWI,IAAX,EAAd;;OAPJ,MASO;iBACIH,OAAO,MAAP,GAAgB,MAAzB,EAAiCD,OAAjC;;KAxagB;;WA4apBL,QA5aoB,uBA4aT;UACLU,QAAQ,KAAKjb,OAAL,CAAaC,YAAb,CAA0B,qBAA1B,CAAZ;;UAEI,CAACgb,KAAL,EAAY;gBACF,OAAO,KAAKra,MAAL,CAAYqa,KAAnB,KAA6B,UAA7B,GACJ,KAAKra,MAAL,CAAYqa,KAAZ,CAAkBtd,IAAlB,CAAuB,KAAKqC,OAA5B,CADI,GAEJ,KAAKY,MAAL,CAAYqa,KAFhB;;;aAKKA,KAAP;KArbkB;;;WA0bpBxB,cA1boB,2BA0bLnI,SA1bK,EA0bM;aACjB9B,cAAc8B,UAAU7P,WAAV,EAAd,CAAP;KA3bkB;;WA8bpB6W,aA9boB,4BA8bJ;;;UACR4C,WAAW,KAAKta,MAAL,CAAYJ,OAAZ,CAAoB2a,KAApB,CAA0B,GAA1B,CAAjB;eAESC,OAAT,CAAiB,UAAC5a,OAAD,EAAa;YACxBA,YAAY,OAAhB,EAAyB;eACrB,OAAKR,OAAP,EAAgBoE,EAAhB,CACE,OAAK8M,WAAL,CAAiB/O,KAAjB,CAAuB6O,KADzB,EAEE,OAAKpQ,MAAL,CAAYrB,QAFd,EAGE,UAACvB,KAAD;mBAAW,OAAK0G,MAAL,CAAY1G,KAAZ,CAAX;WAHF;SADF,MAMO,IAAIwC,YAAYwX,QAAQqD,MAAxB,EAAgC;cAC/BC,UAAU9a,YAAYwX,QAAQsC,KAApB,GACZ,OAAKpJ,WAAL,CAAiB/O,KAAjB,CAAuByG,UADX,GAEZ,OAAKsI,WAAL,CAAiB/O,KAAjB,CAAuBmS,OAF3B;cAGMiH,WAAW/a,YAAYwX,QAAQsC,KAApB,GACb,OAAKpJ,WAAL,CAAiB/O,KAAjB,CAAuB0G,UADV,GAEb,OAAKqI,WAAL,CAAiB/O,KAAjB,CAAuBqZ,QAF3B;eAIE,OAAKxb,OAAP,EACGoE,EADH,CAEIkX,OAFJ,EAGI,OAAK1a,MAAL,CAAYrB,QAHhB,EAII,UAACvB,KAAD;mBAAW,OAAK8a,MAAL,CAAY9a,KAAZ,CAAX;WAJJ,EAMGoG,EANH,CAOImX,QAPJ,EAQI,OAAK3a,MAAL,CAAYrB,QARhB,EASI,UAACvB,KAAD;mBAAW,OAAK+a,MAAL,CAAY/a,KAAZ,CAAX;WATJ;;;aAaA,OAAKgC,OAAP,EAAgBiD,OAAhB,CAAwB,QAAxB,EAAkCmB,EAAlC,CACE,eADF,EAEE;iBAAM,OAAKuI,IAAL,EAAN;SAFF;OA5BF;;UAkCI,KAAK/L,MAAL,CAAYrB,QAAhB,EAA0B;aACnBqB,MAAL,gBACK,KAAKA,MADV;mBAEW,QAFX;oBAGY;;OAJd,MAMO;aACA6a,SAAL;;KA1egB;;WA8epBA,SA9eoB,wBA8eR;UACJC,YAAY,OAAO,KAAK1b,OAAL,CAAaC,YAAb,CAA0B,qBAA1B,CAAzB;;UACI,KAAKD,OAAL,CAAaC,YAAb,CAA0B,OAA1B,KACDyb,cAAc,QADjB,EAC2B;aACpB1b,OAAL,CAAawF,YAAb,CACE,qBADF,EAEE,KAAKxF,OAAL,CAAaC,YAAb,CAA0B,OAA1B,KAAsC,EAFxC;aAIKD,OAAL,CAAawF,YAAb,CAA0B,OAA1B,EAAmC,EAAnC;;KAtfgB;;WA0fpBsT,MA1foB,mBA0fb9a,KA1fa,EA0fNmU,OA1fM,EA0fG;UACfuG,UAAU,KAAKxH,WAAL,CAAiBrP,QAAjC;gBAEUsQ,WAAW9U,KAAEW,MAAM4Q,aAAR,EAAuB5K,IAAvB,CAA4B0U,OAA5B,CAArB;;UAEI,CAACvG,OAAL,EAAc;kBACF,IAAI,KAAKjB,WAAT,CACRlT,MAAM4Q,aADE,EAER,KAAK+J,kBAAL,EAFQ,CAAV;aAIE3a,MAAM4Q,aAAR,EAAuB5K,IAAvB,CAA4B0U,OAA5B,EAAqCvG,OAArC;;;UAGEnU,KAAJ,EAAW;gBACDoa,cAAR,CACEpa,MAAMgH,IAAN,KAAe,SAAf,GAA2BgT,QAAQlS,KAAnC,GAA2CkS,QAAQsC,KADrD,IAEI,IAFJ;;;UAKEjd,KAAE8U,QAAQ6G,aAAR,EAAF,EAA2BzV,QAA3B,CAAoCnB,UAAUkB,IAA9C,KACD6O,QAAQgG,WAAR,KAAwBJ,WAAWzU,IADtC,EAC4C;gBAClC6U,WAAR,GAAsBJ,WAAWzU,IAAjC;;;;mBAIW6O,QAAQ+F,QAArB;cAEQC,WAAR,GAAsBJ,WAAWzU,IAAjC;;UAEI,CAAC6O,QAAQvR,MAAR,CAAe+a,KAAhB,IAAyB,CAACxJ,QAAQvR,MAAR,CAAe+a,KAAf,CAAqB/O,IAAnD,EAAyD;gBAC/CA,IAAR;;;;cAIMsL,QAAR,GAAmBlP,WAAW,YAAM;YAC9BmJ,QAAQgG,WAAR,KAAwBJ,WAAWzU,IAAvC,EAA6C;kBACnCsJ,IAAR;;OAFe,EAIhBuF,QAAQvR,MAAR,CAAe+a,KAAf,CAAqB/O,IAJL,CAAnB;KA5hBkB;;WAmiBpBmM,MAniBoB,mBAmiBb/a,KAniBa,EAmiBNmU,OAniBM,EAmiBG;UACfuG,UAAU,KAAKxH,WAAL,CAAiBrP,QAAjC;gBAEUsQ,WAAW9U,KAAEW,MAAM4Q,aAAR,EAAuB5K,IAAvB,CAA4B0U,OAA5B,CAArB;;UAEI,CAACvG,OAAL,EAAc;kBACF,IAAI,KAAKjB,WAAT,CACRlT,MAAM4Q,aADE,EAER,KAAK+J,kBAAL,EAFQ,CAAV;aAIE3a,MAAM4Q,aAAR,EAAuB5K,IAAvB,CAA4B0U,OAA5B,EAAqCvG,OAArC;;;UAGEnU,KAAJ,EAAW;gBACDoa,cAAR,CACEpa,MAAMgH,IAAN,KAAe,UAAf,GAA4BgT,QAAQlS,KAApC,GAA4CkS,QAAQsC,KADtD,IAEI,KAFJ;;;UAKEnI,QAAQ0G,oBAAR,EAAJ,EAAoC;;;;mBAIvB1G,QAAQ+F,QAArB;cAEQC,WAAR,GAAsBJ,WAAWoC,GAAjC;;UAEI,CAAChI,QAAQvR,MAAR,CAAe+a,KAAhB,IAAyB,CAACxJ,QAAQvR,MAAR,CAAe+a,KAAf,CAAqBhP,IAAnD,EAAyD;gBAC/CA,IAAR;;;;cAIMuL,QAAR,GAAmBlP,WAAW,YAAM;YAC9BmJ,QAAQgG,WAAR,KAAwBJ,WAAWoC,GAAvC,EAA4C;kBAClCxN,IAAR;;OAFe,EAIhBwF,QAAQvR,MAAR,CAAe+a,KAAf,CAAqBhP,IAJL,CAAnB;KAnkBkB;;WA0kBpBkM,oBA1kBoB,mCA0kBG;WAChB,IAAMrY,OAAX,IAAsB,KAAK4X,cAA3B,EAA2C;YACrC,KAAKA,cAAL,CAAoB5X,OAApB,CAAJ,EAAkC;iBACzB,IAAP;;;;aAIG,KAAP;KAjlBkB;;WAolBpBqG,UAplBoB,uBAolBTjG,MAplBS,EAolBD;4BAEZ,KAAKsQ,WAAL,CAAiB/K,OADtB,EAEK9I,KAAE,KAAK2C,OAAP,EAAgBgE,IAAhB,EAFL,EAGKpD,MAHL;;UAMI,OAAOA,OAAO+a,KAAd,KAAwB,QAA5B,EAAsC;eAC7BA,KAAP,GAAe;gBACP/a,OAAO+a,KADA;gBAEP/a,OAAO+a;SAFf;;;UAME,OAAO/a,OAAOqa,KAAd,KAAwB,QAA5B,EAAsC;eAC7BA,KAAP,GAAera,OAAOqa,KAAP,CAAavd,QAAb,EAAf;;;UAGE,OAAOkD,OAAOga,OAAd,KAA0B,QAA9B,EAAwC;eAC/BA,OAAP,GAAiBha,OAAOga,OAAP,CAAeld,QAAf,EAAjB;;;WAGG8K,eAAL,CACE7G,IADF,EAEEf,MAFF,EAGE,KAAKsQ,WAAL,CAAiB9K,WAHnB;aAMOxF,MAAP;KAhnBkB;;WAmnBpB+X,kBAnnBoB,iCAmnBC;UACb/X,SAAS,EAAf;;UAEI,KAAKA,MAAT,EAAiB;aACV,IAAMgb,GAAX,IAAkB,KAAKhb,MAAvB,EAA+B;cACzB,KAAKsQ,WAAL,CAAiB/K,OAAjB,CAAyByV,GAAzB,MAAkC,KAAKhb,MAAL,CAAYgb,GAAZ,CAAtC,EAAwD;mBAC/CA,GAAP,IAAc,KAAKhb,MAAL,CAAYgb,GAAZ,CAAd;;;;;aAKChb,MAAP;KA9nBkB;;WAioBpByZ,cAjoBoB,6BAioBH;UACTI,OAAOpd,KAAE,KAAK2b,aAAL,EAAF,CAAb;UACM6C,WAAWpB,KAAKjN,IAAL,CAAU,OAAV,EAAmB5P,KAAnB,CAAyBka,kBAAzB,CAAjB;;UACI+D,aAAa,IAAb,IAAqBA,SAASxb,MAAT,GAAkB,CAA3C,EAA8C;aACvCgD,WAAL,CAAiBwY,SAASC,IAAT,CAAc,EAAd,CAAjB;;KAroBgB;;WAyoBpB9B,4BAzoBoB,yCAyoBShW,IAzoBT,EAyoBe;WAC5BqW,cAAL;;WACKX,kBAAL,CAAwB,KAAKD,cAAL,CAAoBzV,KAAKsN,SAAzB,CAAxB;KA3oBkB;;WA8oBpB2I,cA9oBoB,6BA8oBH;UACT5B,MAAM,KAAKW,aAAL,EAAZ;UACM+C,sBAAsB,KAAKnb,MAAL,CAAY2Y,SAAxC;;UACIlB,IAAIpY,YAAJ,CAAiB,aAAjB,MAAoC,IAAxC,EAA8C;;;;WAG5CoY,GAAF,EAAOhV,WAAP,CAAmBjB,UAAUoB,IAA7B;WACK5C,MAAL,CAAY2Y,SAAZ,GAAwB,KAAxB;WACK5M,IAAL;WACKC,IAAL;WACKhM,MAAL,CAAY2Y,SAAZ,GAAwBwC,mBAAxB;KAxpBkB;;;YA6pBblY,gBA7pBa,6BA6pBIjD,MA7pBJ,EA6pBY;aACvB,KAAKkD,IAAL,CAAU,YAAY;YACvBE,OAAO3G,KAAE,IAAF,EAAQ2G,IAAR,CAAanC,QAAb,CAAX;;YACM+E,UAAU,OAAOhG,MAAP,KAAkB,QAAlB,IAA8BA,MAA9C;;YAEI,CAACoD,IAAD,IAAS,eAAezC,IAAf,CAAoBX,MAApB,CAAb,EAA0C;;;;YAItC,CAACoD,IAAL,EAAW;iBACF,IAAI4T,OAAJ,CAAY,IAAZ,EAAkBhR,OAAlB,CAAP;eACE,IAAF,EAAQ5C,IAAR,CAAanC,QAAb,EAAuBmC,IAAvB;;;YAGE,OAAOpD,MAAP,KAAkB,QAAtB,EAAgC;cAC1B,OAAOoD,KAAKpD,MAAL,CAAP,KAAwB,WAA5B,EAAyC;kBACjC,IAAIyK,SAAJ,wBAAkCzK,MAAlC,QAAN;;;eAEGA,MAAL;;OAjBG,CAAP;KA9pBkB;;;;0BA8HC;eACZgB,OAAP;;;;0BAGmB;eACZuE,OAAP;;;;0BAGgB;eACTxE,IAAP;;;;0BAGoB;eACbE,QAAP;;;;0BAGiB;eACVM,KAAP;;;;0BAGqB;eACdL,SAAP;;;;0BAGuB;eAChBsE,WAAP;;;;;;;;;;;;OAoiBFlH,EAAF,CAAKyC,IAAL,IAAaiW,QAAQ/T,gBAArB;OACE3E,EAAF,CAAKyC,IAAL,EAAW4C,WAAX,GAAyBqT,OAAzB;;OACE1Y,EAAF,CAAKyC,IAAL,EAAW6C,UAAX,GAAwB,YAAY;SAChCtF,EAAF,CAAKyC,IAAL,IAAaK,kBAAb;WACO4V,QAAQ/T,gBAAf;GAFF;;SAKO+T,OAAP;CAlsBc,CAmsBbva,CAnsBa,EAmsBV+S,MAnsBU,CAAhB;;ACRA;;;;;;;AAOA,IAAM4L,UAAW,UAAC3e,IAAD,EAAO;;;;;;MAOhBsE,OAAsB,SAA5B;MACMC,UAAsB,OAA5B;MACMC,WAAsB,YAA5B;MACMC,kBAA0BD,QAAhC;MACMG,qBAAsB3E,KAAE6B,EAAF,CAAKyC,IAAL,CAA5B;MACMkW,eAAsB,YAA5B;MACMC,qBAAsB,IAAIxW,MAAJ,aAAqBuW,YAArB,WAAyC,GAAzC,CAA5B;MAEM1R,uBACDyR,QAAQzR,OADP;eAEQ,OAFR;aAGQ,OAHR;aAIQ,EAJR;cAKQ,yCACA,2BADA,GAEA,kCAFA,GAGA;IARd;MAWMC,2BACDwR,QAAQxR,WADP;aAEM;IAFZ;MAKMhE,YAAY;UACT,MADS;UAET;GAFT;MAKMF,WAAW;WACL,iBADK;aAEL;GAFZ;MAKMC,QAAQ;mBACQL,SADR;uBAEUA,SAFV;mBAGQA,SAHR;qBAISA,SAJT;2BAKYA,SALZ;qBAMSA,SANT;yBAOWA,SAPX;2BAQYA,SARZ;+BAScA,SATd;+BAUcA;;;;;;;GAV5B;;MAmBMka,OA5DgB;;;;;;;;;;;;WA6FpB/C,aA7FoB,4BA6FJ;aACP,KAAKsB,QAAL,MAAmB,KAAK0B,WAAL,EAA1B;KA9FkB;;WAiGpBvC,kBAjGoB,+BAiGDF,UAjGC,EAiGW;WAC3B,KAAKR,aAAL,EAAF,EAAwBxO,QAAxB,CAAoCqN,YAApC,SAAoD2B,UAApD;KAlGkB;;WAqGpBR,aArGoB,4BAqGJ;WACTX,GAAL,GAAW,KAAKA,GAAL,IAAYhb,KAAE,KAAKuD,MAAL,CAAY4Z,QAAd,EAAwB,CAAxB,CAAvB;aACO,KAAKnC,GAAZ;KAvGkB;;WA0GpBiB,UA1GoB,yBA0GP;UACLmB,OAAOpd,KAAE,KAAK2b,aAAL,EAAF,CAAb,CADW;;WAIN0B,iBAAL,CAAuBD,KAAKra,IAAL,CAAU8B,SAASga,KAAnB,CAAvB,EAAkD,KAAK3B,QAAL,EAAlD;;UACIK,UAAU,KAAKqB,WAAL,EAAd;;UACI,OAAOrB,OAAP,KAAmB,UAAvB,EAAmC;kBACvBA,QAAQjd,IAAR,CAAa,KAAKqC,OAAlB,CAAV;;;WAEG0a,iBAAL,CAAuBD,KAAKra,IAAL,CAAU8B,SAASia,OAAnB,CAAvB,EAAoDvB,OAApD;WAEKvX,WAAL,CAAoBjB,UAAUoB,IAA9B,SAAsCpB,UAAUkB,IAAhD;KArHkB;;;WA0HpB2Y,WA1HoB,0BA0HN;aACL,KAAKjc,OAAL,CAAaC,YAAb,CAA0B,cAA1B,KACL,KAAKW,MAAL,CAAYga,OADd;KA3HkB;;WA+HpBP,cA/HoB,6BA+HH;UACTI,OAAOpd,KAAE,KAAK2b,aAAL,EAAF,CAAb;UACM6C,WAAWpB,KAAKjN,IAAL,CAAU,OAAV,EAAmB5P,KAAnB,CAAyBka,kBAAzB,CAAjB;;UACI+D,aAAa,IAAb,IAAqBA,SAASxb,MAAT,GAAkB,CAA3C,EAA8C;aACvCgD,WAAL,CAAiBwY,SAASC,IAAT,CAAc,EAAd,CAAjB;;KAnIgB;;;YAyIbjY,gBAzIa,6BAyIIjD,MAzIJ,EAyIY;aACvB,KAAKkD,IAAL,CAAU,YAAY;YACvBE,OAAO3G,KAAE,IAAF,EAAQ2G,IAAR,CAAanC,QAAb,CAAX;;YACM+E,UAAU,OAAOhG,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC,IAAtD;;YAEI,CAACoD,IAAD,IAAS,eAAezC,IAAf,CAAoBX,MAApB,CAAb,EAA0C;;;;YAItC,CAACoD,IAAL,EAAW;iBACF,IAAIgY,OAAJ,CAAY,IAAZ,EAAkBpV,OAAlB,CAAP;eACE,IAAF,EAAQ5C,IAAR,CAAanC,QAAb,EAAuBmC,IAAvB;;;YAGE,OAAOpD,MAAP,KAAkB,QAAtB,EAAgC;cAC1B,OAAOoD,KAAKpD,MAAL,CAAP,KAAwB,WAA5B,EAAyC;kBACjC,IAAIyK,SAAJ,wBAAkCzK,MAAlC,QAAN;;;eAEGA,MAAL;;OAjBG,CAAP;KA1IkB;;;;;0BA+DC;eACZgB,OAAP;;;;0BAGmB;eACZuE,OAAP;;;;0BAGgB;eACTxE,IAAP;;;;0BAGoB;eACbE,QAAP;;;;0BAGiB;eACVM,KAAP;;;;0BAGqB;eACdL,SAAP;;;;0BAGuB;eAChBsE,WAAP;;;;IA5BkBwR,OA5DA;;;;;;;;OAuKpB1Y,EAAF,CAAKyC,IAAL,IAAaqa,QAAQnY,gBAArB;OACE3E,EAAF,CAAKyC,IAAL,EAAW4C,WAAX,GAAyByX,OAAzB;;OACE9c,EAAF,CAAKyC,IAAL,EAAW6C,UAAX,GAAwB,YAAY;SAChCtF,EAAF,CAAKyC,IAAL,IAAaK,kBAAb;WACOga,QAAQnY,gBAAf;GAFF;;SAKOmY,OAAP;CA9Kc,CA+Kb3e,CA/Ka,CAAhB;;ACPA;;;;;;;AAOA,IAAM+e,YAAa,UAAC/e,IAAD,EAAO;;;;;;MAOlBsE,OAAqB,WAA3B;MACMC,UAAqB,OAA3B;MACMC,WAAqB,cAA3B;MACMC,kBAAyBD,QAA/B;MACME,eAAqB,WAA3B;MACMC,qBAAqB3E,KAAE6B,EAAF,CAAKyC,IAAL,CAA3B;MAEMwE,UAAU;YACL,EADK;YAEL,MAFK;YAGL;GAHX;MAMMC,cAAc;YACT,QADS;YAET,QAFS;YAGT;GAHX;MAMMjE,QAAQ;2BACeL,SADf;uBAEaA,SAFb;4BAGWA,SAAvB,GAAmCC;GAHrC;MAMMK,YAAY;mBACA,eADA;mBAEA,eAFA;YAGA;GAHlB;MAMMF,WAAW;cACG,qBADH;YAEG,SAFH;oBAGG,mBAHH;eAIG,WAJH;eAKG,WALH;gBAMG,kBANH;cAOG,WAPH;oBAQG,gBARH;qBASG;GATpB;MAYMma,eAAe;YACR,QADQ;cAER;;;;;;;GAFb;;MAWMD,SA7DkB;;;uBA8DVpc,OAAZ,EAAqBY,MAArB,EAA6B;;;WACtByB,QAAL,GAAsBrC,OAAtB;WACKsc,cAAL,GAAsBtc,QAAQiJ,OAAR,KAAoB,MAApB,GAA6BxK,MAA7B,GAAsCuB,OAA5D;WACK4G,OAAL,GAAsB,KAAKC,UAAL,CAAgBjG,MAAhB,CAAtB;WACK0L,SAAL,GAAyB,KAAK1F,OAAL,CAAa3I,MAAhB,SAA0BiE,SAASqa,SAAnC,UACG,KAAK3V,OAAL,CAAa3I,MADhB,SAC0BiE,SAASsa,UADnC,WAEG,KAAK5V,OAAL,CAAa3I,MAFhB,SAE0BiE,SAASua,cAFnC,CAAtB;WAGKC,QAAL,GAAsB,EAAtB;WACKC,QAAL,GAAsB,EAAtB;WACKC,aAAL,GAAsB,IAAtB;WACKC,aAAL,GAAsB,CAAtB;WAEE,KAAKP,cAAP,EAAuBlY,EAAvB,CAA0BjC,MAAM2a,MAAhC,EAAwC,UAAC9e,KAAD;eAAW,MAAK+e,QAAL,CAAc/e,KAAd,CAAX;OAAxC;WAEKgf,OAAL;;WACKD,QAAL;KA7EoB;;;;;;WA4FtBC,OA5FsB,sBA4FZ;;;UACFC,aAAa,KAAKX,cAAL,KAAwB,KAAKA,cAAL,CAAoB7d,MAA5C,GACf4d,aAAaa,MADE,GACOb,aAAac,QADvC;UAGMC,eAAe,KAAKxW,OAAL,CAAayW,MAAb,KAAwB,MAAxB,GACjBJ,UADiB,GACJ,KAAKrW,OAAL,CAAayW,MAD9B;UAGMC,aAAaF,iBAAiBf,aAAac,QAA9B,GACf,KAAKI,aAAL,EADe,GACQ,CAD3B;WAGKb,QAAL,GAAgB,EAAhB;WACKC,QAAL,GAAgB,EAAhB;WAEKE,aAAL,GAAqB,KAAKW,gBAAL,EAArB;UAEMC,UAAUpgB,KAAE8L,SAAF,CAAY9L,KAAE,KAAKiP,SAAP,CAAZ,CAAhB;cAGGoR,GADH,CACO,UAAC1d,OAAD,EAAa;YACZ/B,MAAJ;YACM0f,iBAAiBvgB,KAAK2F,sBAAL,CAA4B/C,OAA5B,CAAvB;;YAEI2d,cAAJ,EAAoB;mBACTtgB,KAAEsgB,cAAF,EAAkB,CAAlB,CAAT;;;YAGE1f,MAAJ,EAAY;cACJ2f,YAAY3f,OAAO+P,qBAAP,EAAlB;;cACI4P,UAAUnG,KAAV,IAAmBmG,UAAUC,MAAjC,EAAyC;;mBAEhC,CACLxgB,KAAEY,MAAF,EAAUmf,YAAV,IAA0BU,GAA1B,GAAgCR,UAD3B,EAELK,cAFK,CAAP;;;;eAMG,IAAP;OAnBJ,EAqBGtR,MArBH,CAqBU,UAAC0R,IAAD;eAAUA,IAAV;OArBV,EAsBGC,IAtBH,CAsBQ,UAACC,CAAD,EAAIC,CAAJ;eAAUD,EAAE,CAAF,IAAOC,EAAE,CAAF,CAAjB;OAtBR,EAuBG9C,OAvBH,CAuBW,UAAC2C,IAAD,EAAU;eACZrB,QAAL,CAAcnQ,IAAd,CAAmBwR,KAAK,CAAL,CAAnB;;eACKpB,QAAL,CAAcpQ,IAAd,CAAmBwR,KAAK,CAAL,CAAnB;OAzBJ;KA7GoB;;WA0ItBlb,OA1IsB,sBA0IZ;WACNC,UAAF,CAAa,KAAKT,QAAlB,EAA4BR,QAA5B;WACE,KAAKya,cAAP,EAAuB/T,GAAvB,CAA2BzG,SAA3B;WAEKO,QAAL,GAAsB,IAAtB;WACKia,cAAL,GAAsB,IAAtB;WACK1V,OAAL,GAAsB,IAAtB;WACK0F,SAAL,GAAsB,IAAtB;WACKoQ,QAAL,GAAsB,IAAtB;WACKC,QAAL,GAAsB,IAAtB;WACKC,aAAL,GAAsB,IAAtB;WACKC,aAAL,GAAsB,IAAtB;KArJoB;;;WA0JtBhW,UA1JsB,uBA0JXjG,MA1JW,EA0JH;4BAEZuF,OADL,EAEKvF,MAFL;;UAKI,OAAOA,OAAO3C,MAAd,KAAyB,QAA7B,EAAuC;YACjCgO,KAAK5O,KAAEuD,OAAO3C,MAAT,EAAiBuP,IAAjB,CAAsB,IAAtB,CAAT;;YACI,CAACvB,EAAL,EAAS;eACF7O,KAAKic,MAAL,CAAY1X,IAAZ,CAAL;eACEf,OAAO3C,MAAT,EAAiBuP,IAAjB,CAAsB,IAAtB,EAA4BvB,EAA5B;;;eAEKhO,MAAP,SAAoBgO,EAApB;;;WAGGzD,eAAL,CAAqB7G,IAArB,EAA2Bf,MAA3B,EAAmCwF,WAAnC;aAEOxF,MAAP;KA3KoB;;WA8KtB2c,aA9KsB,4BA8KN;aACP,KAAKjB,cAAL,KAAwB7d,MAAxB,GACH,KAAK6d,cAAL,CAAoB6B,WADjB,GAC+B,KAAK7B,cAAL,CAAoBxH,SAD1D;KA/KoB;;WAmLtB0I,gBAnLsB,+BAmLH;aACV,KAAKlB,cAAL,CAAoBpG,YAApB,IAAoCtW,KAAKwe,GAAL,CACzCte,SAAS8T,IAAT,CAAcsC,YAD2B,EAEzCpW,SAASgJ,eAAT,CAAyBoN,YAFgB,CAA3C;KApLoB;;WA0LtBmI,gBA1LsB,+BA0LH;aACV,KAAK/B,cAAL,KAAwB7d,MAAxB,GACHA,OAAO6f,WADJ,GACkB,KAAKhC,cAAL,CAAoBtO,qBAApB,GAA4C6P,MADrE;KA3LoB;;WA+LtBd,QA/LsB,uBA+LX;UACHjI,YAAe,KAAKyI,aAAL,KAAuB,KAAK3W,OAAL,CAAakL,MAAzD;;UACMoE,eAAe,KAAKsH,gBAAL,EAArB;;UACMe,YAAe,KAAK3X,OAAL,CAAakL,MAAb,GACnBoE,YADmB,GAEnB,KAAKmI,gBAAL,EAFF;;UAII,KAAKxB,aAAL,KAAuB3G,YAA3B,EAAyC;aAClC8G,OAAL;;;UAGElI,aAAayJ,SAAjB,EAA4B;YACpBtgB,SAAS,KAAK0e,QAAL,CAAc,KAAKA,QAAL,CAActc,MAAd,GAAuB,CAArC,CAAf;;YAEI,KAAKuc,aAAL,KAAuB3e,MAA3B,EAAmC;eAC5BugB,SAAL,CAAevgB,MAAf;;;;;;UAKA,KAAK2e,aAAL,IAAsB9H,YAAY,KAAK4H,QAAL,CAAc,CAAd,CAAlC,IAAsD,KAAKA,QAAL,CAAc,CAAd,IAAmB,CAA7E,EAAgF;aACzEE,aAAL,GAAqB,IAArB;;aACK6B,MAAL;;;;;WAIG,IAAItS,IAAI,KAAKuQ,QAAL,CAAcrc,MAA3B,EAAmC8L,GAAnC,GAAyC;YACjCuS,iBAAiB,KAAK9B,aAAL,KAAuB,KAAKD,QAAL,CAAcxQ,CAAd,CAAvB,IACnB2I,aAAa,KAAK4H,QAAL,CAAcvQ,CAAd,CADM,KAElB,OAAO,KAAKuQ,QAAL,CAAcvQ,IAAI,CAAlB,CAAP,KAAgC,WAAhC,IACG2I,YAAY,KAAK4H,QAAL,CAAcvQ,IAAI,CAAlB,CAHG,CAAvB;;YAKIuS,cAAJ,EAAoB;eACbF,SAAL,CAAe,KAAK7B,QAAL,CAAcxQ,CAAd,CAAf;;;KAhOgB;;WAqOtBqS,SArOsB,sBAqOZvgB,MArOY,EAqOJ;WACX2e,aAAL,GAAqB3e,MAArB;;WAEKwgB,MAAL;;UAEIE,UAAU,KAAKrS,SAAL,CAAe6O,KAAf,CAAqB,GAArB,CAAd,CALgB;;;gBAONwD,QAAQjB,GAAR,CAAY,UAACne,QAAD,EAAc;eACxBA,QAAH,uBAA4BtB,MAA5B,aACGsB,QADH,gBACqBtB,MADrB,SAAP;OADQ,CAAV;UAKM2gB,QAAQvhB,KAAEshB,QAAQ7C,IAAR,CAAa,GAAb,CAAF,CAAd;;UAEI8C,MAAMrb,QAAN,CAAenB,UAAUyc,aAAzB,CAAJ,EAA6C;cACrC5b,OAAN,CAAcf,SAAS4c,QAAvB,EAAiC1e,IAAjC,CAAsC8B,SAAS6c,eAA/C,EAAgEvU,QAAhE,CAAyEpI,UAAU8C,MAAnF;cACMsF,QAAN,CAAepI,UAAU8C,MAAzB;OAFF,MAGO;;cAECsF,QAAN,CAAepI,UAAU8C,MAAzB,EAFK;;;cAKC8Z,OAAN,CAAc9c,SAAS+c,cAAvB,EAAuC1X,IAAvC,CAA+CrF,SAASqa,SAAxD,UAAsEra,SAASsa,UAA/E,EAA6FhS,QAA7F,CAAsGpI,UAAU8C,MAAhH,EALK;;cAOC8Z,OAAN,CAAc9c,SAAS+c,cAAvB,EAAuC1X,IAAvC,CAA4CrF,SAASgd,SAArD,EAAgE3U,QAAhE,CAAyErI,SAASqa,SAAlF,EAA6F/R,QAA7F,CAAsGpI,UAAU8C,MAAhH;;;WAGA,KAAKoX,cAAP,EAAuB9b,OAAvB,CAA+B2B,MAAMgd,QAArC,EAA+C;uBAC9BlhB;OADjB;KAhQoB;;WAqQtBwgB,MArQsB,qBAqQb;WACL,KAAKnS,SAAP,EAAkBD,MAAlB,CAAyBnK,SAASgD,MAAlC,EAA0C7B,WAA1C,CAAsDjB,UAAU8C,MAAhE;KAtQoB;;;cA2QfrB,gBA3Qe,6BA2QEjD,MA3QF,EA2QU;aACvB,KAAKkD,IAAL,CAAU,YAAY;YACvBE,OAAO3G,KAAE,IAAF,EAAQ2G,IAAR,CAAanC,QAAb,CAAX;;YACM+E,UAAU,OAAOhG,MAAP,KAAkB,QAAlB,IAA8BA,MAA9C;;YAEI,CAACoD,IAAL,EAAW;iBACF,IAAIoY,SAAJ,CAAc,IAAd,EAAoBxV,OAApB,CAAP;eACE,IAAF,EAAQ5C,IAAR,CAAanC,QAAb,EAAuBmC,IAAvB;;;YAGE,OAAOpD,MAAP,KAAkB,QAAtB,EAAgC;cAC1B,OAAOoD,KAAKpD,MAAL,CAAP,KAAwB,WAA5B,EAAyC;kBACjC,IAAIyK,SAAJ,wBAAkCzK,MAAlC,QAAN;;;eAEGA,MAAL;;OAbG,CAAP;KA5QoB;;;;0BAkFD;eACZgB,OAAP;;;;0BAGmB;eACZuE,OAAP;;;;;;;;;;;;OA8MF1H,MAAF,EAAU2F,EAAV,CAAajC,MAAMuJ,aAAnB,EAAkC,YAAM;QAChC0T,aAAa/hB,KAAE8L,SAAF,CAAY9L,KAAE6E,SAASmd,QAAX,CAAZ,CAAnB;;SAEK,IAAIlT,IAAIiT,WAAW/e,MAAxB,EAAgC8L,GAAhC,GAAsC;UAC9BmT,OAAOjiB,KAAE+hB,WAAWjT,CAAX,CAAF,CAAb;;gBACUtI,gBAAV,CAA2BlG,IAA3B,CAAgC2hB,IAAhC,EAAsCA,KAAKtb,IAAL,EAAtC;;GALJ;;;;;;;OAeE9E,EAAF,CAAKyC,IAAL,IAAaya,UAAUvY,gBAAvB;OACE3E,EAAF,CAAKyC,IAAL,EAAW4C,WAAX,GAAyB6X,SAAzB;;OACEld,EAAF,CAAKyC,IAAL,EAAW6C,UAAX,GAAwB,YAAY;SAChCtF,EAAF,CAAKyC,IAAL,IAAaK,kBAAb;WACOoa,UAAUvY,gBAAjB;GAFF;;SAKOuY,SAAP;CA3TgB,CA4Tf/e,CA5Te,CAAlB;;ACPA;;;;;;;AAOA,IAAMkiB,MAAO,UAACliB,IAAD,EAAO;;;;;;MAOZsE,OAAsB,KAA5B;MACMC,UAAsB,OAA5B;MACMC,WAAsB,QAA5B;MACMC,kBAA0BD,QAAhC;MACME,eAAsB,WAA5B;MACMC,qBAAsB3E,KAAE6B,EAAF,CAAKyC,IAAL,CAA5B;MACMM,sBAAsB,GAA5B;MAEME,QAAQ;mBACYL,SADZ;uBAEcA,SAFd;mBAGYA,SAHZ;qBAIaA,SAJb;8BAKaA,SAAzB,GAAqCC;GALvC;MAQMK,YAAY;mBACA,eADA;YAEA,QAFA;cAGA,UAHA;UAIA,MAJA;UAKA;GALlB;MAQMF,WAAW;cACS,WADT;oBAES,mBAFT;YAGS,SAHT;eAIS,gBAJT;iBAKS,iEALT;qBAMS,kBANT;2BAOS;;;;;;;GAP1B;;MAgBMqd,GA/CY;;;iBAgDJvf,OAAZ,EAAqB;WACdqC,QAAL,GAAgBrC,OAAhB;KAjDc;;;;;;WA4DhB4M,IA5DgB,mBA4DT;;;UACD,KAAKvK,QAAL,CAAciQ,UAAd,IACA,KAAKjQ,QAAL,CAAciQ,UAAd,CAAyB5R,QAAzB,KAAsC+T,KAAKC,YAD3C,IAEArX,KAAE,KAAKgF,QAAP,EAAiBkB,QAAjB,CAA0BnB,UAAU8C,MAApC,CAFA,IAGA7H,KAAE,KAAKgF,QAAP,EAAiBkB,QAAjB,CAA0BnB,UAAU2N,QAApC,CAHJ,EAGmD;;;;UAI/C9R,MAAJ;UACIuhB,QAAJ;UACMC,cAAcpiB,KAAE,KAAKgF,QAAP,EAAiBY,OAAjB,CAAyBf,SAAS+c,cAAlC,EAAkD,CAAlD,CAApB;UACM1f,WAAWnC,KAAK2F,sBAAL,CAA4B,KAAKV,QAAjC,CAAjB;;UAEIod,WAAJ,EAAiB;YACTC,eAAeD,YAAYE,QAAZ,KAAyB,IAAzB,GAAgCzd,SAAS0d,SAAzC,GAAqD1d,SAASgD,MAAnF;mBACW7H,KAAE8L,SAAF,CAAY9L,KAAEoiB,WAAF,EAAerf,IAAf,CAAoBsf,YAApB,CAAZ,CAAX;mBACWF,SAASA,SAASnf,MAAT,GAAkB,CAA3B,CAAX;;;UAGIgS,YAAYhV,KAAE8E,KAAF,CAAQA,MAAM4L,IAAd,EAAoB;uBACrB,KAAK1L;OADJ,CAAlB;UAIM8N,YAAY9S,KAAE8E,KAAF,CAAQA,MAAMmB,IAAd,EAAoB;uBACrBkc;OADC,CAAlB;;UAIIA,QAAJ,EAAc;aACVA,QAAF,EAAYhf,OAAZ,CAAoB6R,SAApB;;;WAGA,KAAKhQ,QAAP,EAAiB7B,OAAjB,CAAyB2P,SAAzB;;UAEIA,UAAUxN,kBAAV,MACD0P,UAAU1P,kBAAV,EADH,EACmC;;;;UAI/BpD,QAAJ,EAAc;iBACHlC,KAAEkC,QAAF,EAAY,CAAZ,CAAT;;;WAGGif,SAAL,CACE,KAAKnc,QADP,EAEEod,WAFF;;UAKM/R,WAAW,SAAXA,QAAW,GAAM;YACfmS,cAAcxiB,KAAE8E,KAAF,CAAQA,MAAM+L,MAAd,EAAsB;yBACzB,MAAK7L;SADF,CAApB;YAIM2S,aAAa3X,KAAE8E,KAAF,CAAQA,MAAMwL,KAAd,EAAqB;yBACvB6R;SADE,CAAnB;aAIEA,QAAF,EAAYhf,OAAZ,CAAoBqf,WAApB;aACE,MAAKxd,QAAP,EAAiB7B,OAAjB,CAAyBwU,UAAzB;OAVF;;UAaI/W,MAAJ,EAAY;aACLugB,SAAL,CAAevgB,MAAf,EAAuBA,OAAOqU,UAA9B,EAA0C5E,QAA1C;OADF,MAEO;;;KA1HO;;WA+HhB7K,OA/HgB,sBA+HN;WACNC,UAAF,CAAa,KAAKT,QAAlB,EAA4BR,QAA5B;WACKQ,QAAL,GAAgB,IAAhB;KAjIc;;;WAsIhBmc,SAtIgB,sBAsINxe,OAtIM,EAsIG2Z,SAtIH,EAsIcnE,QAtId,EAsIwB;;;UAClCsK,cAAJ;;UACInG,UAAUgG,QAAV,KAAuB,IAA3B,EAAiC;yBACdtiB,KAAEsc,SAAF,EAAavZ,IAAb,CAAkB8B,SAAS0d,SAA3B,CAAjB;OADF,MAEO;yBACYviB,KAAEsc,SAAF,EAAapP,QAAb,CAAsBrI,SAASgD,MAA/B,CAAjB;;;UAGI6a,SAASD,eAAe,CAAf,CAAf;UACM3R,kBAAkBqH,YACtBpY,KAAKgC,qBAAL,EADsB,IAErB2gB,UAAU1iB,KAAE0iB,MAAF,EAAUxc,QAAV,CAAmBnB,UAAUoB,IAA7B,CAFb;;UAIMkK,WAAW,SAAXA,QAAW;eAAM,OAAKsS,mBAAL,CACrBhgB,OADqB,EAErB+f,MAFqB,EAGrBvK,QAHqB,CAAN;OAAjB;;UAMIuK,UAAU5R,eAAd,EAA+B;aAC3B4R,MAAF,EACGjhB,GADH,CACO1B,KAAK2B,cADZ,EAC4B2O,QAD5B,EAEGvO,oBAFH,CAEwB8C,mBAFxB;OADF,MAIO;;;KA7JO;;WAkKhB+d,mBAlKgB,gCAkKIhgB,OAlKJ,EAkKa+f,MAlKb,EAkKqBvK,QAlKrB,EAkK+B;UACzCuK,MAAJ,EAAY;aACRA,MAAF,EAAU1c,WAAV,CAAyBjB,UAAUkB,IAAnC,SAA2ClB,UAAU8C,MAArD;YAEM+a,gBAAgB5iB,KAAE0iB,OAAOzN,UAAT,EAAqBlS,IAArB,CACpB8B,SAASge,qBADW,EAEpB,CAFoB,CAAtB;;YAIID,aAAJ,EAAmB;eACfA,aAAF,EAAiB5c,WAAjB,CAA6BjB,UAAU8C,MAAvC;;;YAGE6a,OAAO9f,YAAP,CAAoB,MAApB,MAAgC,KAApC,EAA2C;iBAClCuF,YAAP,CAAoB,eAApB,EAAqC,KAArC;;;;WAIFxF,OAAF,EAAWwK,QAAX,CAAoBpI,UAAU8C,MAA9B;;UACIlF,QAAQC,YAAR,CAAqB,MAArB,MAAiC,KAArC,EAA4C;gBAClCuF,YAAR,CAAqB,eAArB,EAAsC,IAAtC;;;WAGG0F,MAAL,CAAYlL,OAAZ;WACEA,OAAF,EAAWwK,QAAX,CAAoBpI,UAAUkB,IAA9B;;UAEItD,QAAQsS,UAAR,IACAjV,KAAE2C,QAAQsS,UAAV,EAAsB/O,QAAtB,CAA+BnB,UAAU+d,aAAzC,CADJ,EAC6D;YACrDC,kBAAkB/iB,KAAE2C,OAAF,EAAWiD,OAAX,CAAmBf,SAAS4c,QAA5B,EAAsC,CAAtC,CAAxB;;YACIsB,eAAJ,EAAqB;eACjBA,eAAF,EAAmBhgB,IAAnB,CAAwB8B,SAAS6c,eAAjC,EAAkDvU,QAAlD,CAA2DpI,UAAU8C,MAArE;;;gBAGMM,YAAR,CAAqB,eAArB,EAAsC,IAAtC;;;UAGEgQ,QAAJ,EAAc;;;KArMA;;;QA4MT3R,gBA5MS,6BA4MQjD,MA5MR,EA4MgB;aACvB,KAAKkD,IAAL,CAAU,YAAY;YACrB6K,QAAQtR,KAAE,IAAF,CAAd;YACI2G,OAAO2K,MAAM3K,IAAN,CAAWnC,QAAX,CAAX;;YAEI,CAACmC,IAAL,EAAW;iBACF,IAAIub,GAAJ,CAAQ,IAAR,CAAP;gBACMvb,IAAN,CAAWnC,QAAX,EAAqBmC,IAArB;;;YAGE,OAAOpD,MAAP,KAAkB,QAAtB,EAAgC;cAC1B,OAAOoD,KAAKpD,MAAL,CAAP,KAAwB,WAA5B,EAAyC;kBACjC,IAAIyK,SAAJ,wBAAkCzK,MAAlC,QAAN;;;eAEGA,MAAL;;OAbG,CAAP;KA7Mc;;;;0BAsDK;eACZgB,OAAP;;;;;;;;;;;;OA+KF9B,QAAF,EACGsE,EADH,CACMjC,MAAMkC,cADZ,EAC4BnC,SAAS2C,WADrC,EACkD,UAAU7G,KAAV,EAAiB;UACzDmG,cAAN;;QACIN,gBAAJ,CAAqBlG,IAArB,CAA0BN,KAAE,IAAF,CAA1B,EAAmC,MAAnC;GAHJ;;;;;;;OAYE6B,EAAF,CAAKyC,IAAL,IAAa4d,IAAI1b,gBAAjB;OACE3E,EAAF,CAAKyC,IAAL,EAAW4C,WAAX,GAAyBgb,GAAzB;;OACErgB,EAAF,CAAKyC,IAAL,EAAW6C,UAAX,GAAwB,YAAY;SAChCtF,EAAF,CAAKyC,IAAL,IAAaK,kBAAb;WACOud,IAAI1b,gBAAX;GAFF;;SAKO0b,GAAP;CAzPU,CA0PTliB,CA1PS,CAAZ;;ACGA;;;;;;;AAOA,CAAC,UAACA,IAAD,EAAO;MACF,OAAOA,IAAP,KAAa,WAAjB,EAA8B;UACtB,IAAIgO,SAAJ,CAAc,kGAAd,CAAN;;;MAGIgV,UAAUhjB,KAAE6B,EAAF,CAAKqP,MAAL,CAAY4M,KAAZ,CAAkB,GAAlB,EAAuB,CAAvB,EAA0BA,KAA1B,CAAgC,GAAhC,CAAhB;MACMmF,WAAW,CAAjB;MACMC,UAAU,CAAhB;MACMC,WAAW,CAAjB;MACMC,WAAW,CAAjB;MACMC,WAAW,CAAjB;;MAEIL,QAAQ,CAAR,IAAaE,OAAb,IAAwBF,QAAQ,CAAR,IAAaG,QAArC,IAAiDH,QAAQ,CAAR,MAAeC,QAAf,IAA2BD,QAAQ,CAAR,MAAeG,QAA1C,IAAsDH,QAAQ,CAAR,IAAaI,QAApH,IAAgIJ,QAAQ,CAAR,KAAcK,QAAlJ,EAA4J;UACpJ,IAAIlf,KAAJ,CAAU,8EAAV,CAAN;;CAbJ,EAeGnE,CAfH;;;;;;;;;;;;;;;;;;;;;;"}