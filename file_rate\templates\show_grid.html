{% extends "layout.html" %}
{% block body %}
{% if session.logged_in %}
<script>
{% include "file_rate_js.html" %}
</script>
  
<form action="{{ url_for('show_grid') }}" method="post" class="add-entry">
  {% include "search_contents.html" %}
  {% include "multi_update.html" %}
  <input type="checkbox" id="sel_all" value="on" name="sel_all" />Select All<br/>
  <p class="small">{{result.result_count}} records returned.</p>

  <div class="row">
  {% for file in result.get('files',[]) %}
  <div class="col-sm-2" id="cell_{{file.id}}" data-file-id="{{file.id}}">
    <table><tr><td>
      <input id="check_{{file.id}}" type="checkbox" name="select_upd" value="on">
      <img id="delete_btn_{{file.id}}" class="file_delete"
       src="{{url_for('static', filename='images/icons8-trash-50.png') }}" width="15"/>
      <div class="x-small" >
        <input id="upd_score_{{file.id}}" type="text" class="upd_score"
         name="in_Score_{{file.id}}" value="{{ file.Score|safe }}" width="2"/>
         <input id="upd_keyword_{{file.id}}" type="text" class="upd_keyword"
         name="in_Keywords_{{file.id}}" value="{{ file.Keywords|safe }}" width="2"/>
      </div>
      <a href="{{ url_for('show_image_form') }}?id={{ file.id }}" class="grid-image-link">
	<img src="{{ url_for('show_image') }}?image_file={{ file.Directory }}/{{ file.FileName }}" width="200" border="2">
      </a>
      </td></tr></table>
      </div>
    {% else %}
     <div class="col-sm-6">
       <em>Unbelievable.  No files here so far</em>
     </div>
     {% endfor %}
     </div>
</form>
{% else %}
  <h4>Login first to see list.</h4>
  <a href={{ url_for('login') }}>Login</a>
{% endif %}
{% endblock %}
