body            { font-family: sans-serif; background: #eee; }
a, h1, h2       { color: #377BA8; }
h1, h2          { font-family: 'Georgia', serif; margin: 0; }
h1              { border-bottom: 2px solid #eee; }
h2              { font-size: 1.2em; }

/* Sortable tables */
table.sortable thead {
    background-color:#eee;
    color:#666666;
    font-weight: bold;
    cursor: default;
}

.files          { list-style: none; margin: 0; padding: 0; border: 2px }
.files li       { margin: 0.8em 1.2em; }
.files li h2    { margin-left: -1em; }
.add-file       { font-size: 0.9em; border-bottom: 1px solid #ccc; }
.add-file dl    { font-weight: bold; }
.metanav        { text-align: right; font-size: 0.8em; padding: 0.3em;
                  margin-bottom: 1em; background: #fafafa; }
.flash          { background: #CEE5F5; padding: 0.5em;
                  border: 1px solid #AACBE2; }
.error          { background: #F0D6D6; padding: 0.5em; }


.modal {
    display: none;
    position: fixed;
    z-index: 1;
    padding-top: 20px;
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
    overflow: auto;
    background-color: rgb(0,0,0);
    background-color: rgba(0,0,0,0.4);
}

.x-small {
    font-size: x-small;
    width: 25px;
}

.modal-content {
    background-color: #fefefe;
    margin: auto;
    padding: 20px;
    border: 1px solid #888;
    width: 80%;
    font-size: small;
}

.close {
    color: #aaaaaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
}

.close:hover,
.close:focus {
    color: #000;
    text-decoration: none;
    cursor: pointer;
}

.full-width {
    width: 100%;      /* occupy the entire width of the container */
    height: auto;     /* adjust the height while maintaining aspect ratio */
}

.full-size {
    width: auto;      /* use the image's original width */
    height: auto;     /* use the image's original height */
}

/* Add these styles to improve table appearance */
.table-responsive {
    overflow-x: auto;
}

.table th, .table td {
    vertical-align: middle;
}

td#clip {
    cursor: pointer;
    user-select: all;
}

td#clip:hover {
    background-color: #f8f9fa;
}

.form-control-sm {
    height: calc(1.5em + 0.5rem + 2px);
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    border-radius: 0.2rem;
}

.upd_score, .upd_keyword {
    min-width: 60px;
}

.badge {
    font-weight: 500;
    padding: 0.35em 0.65em;
}

.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1.5rem;
}

.card-header {
    padding: 0.75rem 1.25rem;
}

.text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
