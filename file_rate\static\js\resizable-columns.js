/**
 * Makes table columns resizable
 */
function makeResizableTable(tableSelector) {
  const table = document.querySelector(tableSelector);
  if (!table) return;
  
  table.classList.add('table-resizable');
  
  const headers = table.querySelectorAll('th');
  const tableBody = table.querySelector('tbody');
  
  // Add resizers to all headers except the last one
  headers.forEach((header, index) => {
    if (index < headers.length - 1) { // Skip the last header
      const resizer = document.createElement('div');
      resizer.classList.add('resizer');
      header.appendChild(resizer);
      
      let startX, startWidth, nextStartWidth;
      
      const onMouseDown = (e) => {
        startX = e.pageX;
        startWidth = header.offsetWidth;
        const nextHeader = headers[index + 1];
        nextStartWidth = nextHeader ? nextHeader.offsetWidth : 0;
        
        // Add resizing class to table
        table.classList.add('resizing');
        
        // Add event listeners for mouse movement and release
        document.addEventListener('mousemove', onMouseMove);
        document.addEventListener('mouseup', onMouseUp);
        
        e.preventDefault();
      };
      
      const onMouseMove = (e) => {
        const diffX = e.pageX - startX;
        
        // Update column widths
        header.style.width = `${startWidth + diffX}px`;
        
        // Optional: adjust the next column width to maintain total table width
        const nextHeader = headers[index + 1];
        if (nextHeader && nextStartWidth - diffX > 50) { // Ensure minimum width
          nextHeader.style.width = `${nextStartWidth - diffX}px`;
        }
      };
      
      const onMouseUp = () => {
        // Remove resizing class
        table.classList.remove('resizing');
        
        // Remove event listeners
        document.removeEventListener('mousemove', onMouseMove);
        document.removeEventListener('mouseup', onMouseUp);
      };
      
      resizer.addEventListener('mousedown', onMouseDown);
    }
  });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
  makeResizableTable('.table-responsive table');
});