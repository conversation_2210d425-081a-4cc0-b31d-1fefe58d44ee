
from pyparsing import *

OP_STR = '= != < > >= <= eq ne lt le gt ge like'
OPERATORS = OP_STR.split(' ')
ABREVIATIONS = {
    'directory':'Directory',
    'filename':'FileName',
    'modifydate':'FileModifyDate',
    'createdate':'FileCreateDate',
    'size':'FileSize',
    'comment':'Keywords',
    'rating':'Score',
    'score':'Score',
    'keywords':'Keywords',
    'accessed':'LastAccess',
    }

#messed up go back to previous

def get_parser():
    search = Forward()
    simple = Word(alphas)
    number = Word(nums)
    singleQuotedString = Group(QuotedString("'", escQuote="''"))
    single = ( singleQuotedString | number | Word(alphas) )
    binop = oneOf(OP_STR, caseless=True)
    special = Group( Word(alphas) + binop + single )
    operator = CaselessLiteral("or") | CaselessLiteral("and")
    onot = CaselessLiteral("not")
    parenthetical = Suppress("(") + Group(search) + Suppress(")")
    term = special | single | parenthetical | negative
    search << term + ZeroOrMore(Optional(operator) + term)
    return search

def do_parse(search, str):
    try:
        pstr = search.parseString(str)
        print(pstr)
        return 0
    except Exception as e:
        errstr = repr(e)
        errarray = errstr.split(',')
        print('>>>>>>   ERROR:....'+errarray[-2]+','+errarray[-1])
        return 1

def test_print(search, str):
    print("ORIG:", str)
    return do_parse(search, str)
        
def run_tests(search):
    err = 0
    err += test_print(search, "abc")
    err += test_print(search, "'abc'")
    err += test_print(search, "() ()")
    err += test_print(search, "123")
    err += test_print(search, "123 OR 4")
    err += test_print(search, "aou = 'aaa'")
    err += test_print(search, "zzzzz > 10")
#    err += test_print(search, "(five ge 'three) NOT 'aaaa' eee eq 'aoaoa' ()")
    err += test_print(search, "1111111111111")
#    err += test_print(search, "(((() and( a=b))(")
    err += test_print(search, "'ala''oooo' 123")
    err += test_print(search, "(123) or not (aa != '000' OR 'xxx%() ()')")
    print("Total errors:", err )

def make_sql(obj):
    print("OBJ", type(obj), obj)
    ans = ''
    if ( isinstance(obj,str) or isinstance(obj, int)):
        print("Got str or int")
        if ( obj.lower()=='not' or
             obj.lower()=='or' or
             obj.lower()=='and' ):
            print("and/or/not:", obj)
            return(' ' + obj)
        ans = " (Directory like '%" + obj + \
              "%' OR Filename like '%" + obj + \
              "%' OR Keywords like '%" + obj + \
              "%')"
        print("String or int:", ans)
        return ans
    elif isinstance(obj,dict):
        print("Dict, return nothing", obj)
        return ''
    elif isinstance(obj,list):
        print("Got list")
        if len(obj)==0:
            print("Empty list")
            return('')
        elif len(obj)==1:
            ans = make_sql(obj[0])
            print("One on list:", ans)
            return(ans)
        elif (len(obj)==3 and
            isinstance(obj[0],str) and
            isinstance(obj[1],str) and
            (isinstance(obj[2],str) or
             isinstance(obj[2],int))):
            print("List of three strings...")
            if obj[1] in OPERATORS and isinstance(obj[0],str):
                var1 = obj[0]
                o2 = obj[2]
                val = ''
                if isinstance(o2,int):
                    val = o2
                elif isinstance(o2,str):
                    try:
                        if (o2==str(int(o2))):
                            val = o2
                        else:
                            val = "'" + o2 + "'"
                    except:
                        val = "'" + o2 + "'"
                else:
                    print("How'd we get here", o2)
                    val = make_sql(o2)
                print("before abrev check")
                for k in ABREVIATIONS:
                    # print("checking", var1, k)
                    if (k.startswith(var1.lower())):
                        newvar = ABREVIATIONS[k]
                        print('found abrev:', newvar)
                        ans = "(" + newvar +' '+obj[1]+' ' + val + ")"
                        print("Constructed:", ans)
                print("Maybe:", ans)
                if (not ans.startswith("(")):
                    print("ERROR: var1 not found", var1)
                else:
                    print("Good?", var1)
                print("Answer now:", ans)
                return ans
            print("List of three non-string", obj)
        elif ( len(obj)==3 and
             isinstance(obj[1], str) and
             ( obj[1].lower()=='not' or
               obj[1].lower()=='or' or
               obj[1].lower()=='and' )):
            print("found complex and")
            ans = ( str(make_sql(obj[0])) +
                      ' ' + obj[1] +
                      ' ' + str(make_sql(obj[2])))
            return(ans)
        else:
            print("alt list")
            ans = ( str(make_sql(obj[0])) + ' and ' +
                      str(make_sql(obj[1:])))
            return(ans)
        print("Answer now:", ans)
        return(ans)
    else:
        print("Not str, int, dict, list")
    
if __name__ == "__main__":

    mystr = ''

    search = get_parser()
    
    while mystr != 'quit':
        print('Type quit to exit.\n')
        mystr = input('Type string: ')

        if mystr=='test':
            run_tests(search)
            continue
        
        if mystr=='quit':
            break
        
        do_parse(search, mystr)

        
    print('Done')
