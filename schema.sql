drop table if exists files;
create table files (
  id integer primary key autoincrement,
  Key text not null unique,
  Directory text not null,
  FileName text not null,
  FileModifyDate,
  FileCreateDate,
  FileSize integer not null,
  Comment,
  SharedUserRating integer,
  Score integer,
  Predicted_score integer,
  Keywords,
  MIMEType,
  ImageSize,
  LastAccess integer,
  LastUpdate integer,
  FileModifyTime integer,
  FileCreateTime integer,
  UpdateTime integer,
  AccessTime integer,
  Deleted integer
);

drop table if exists config;
create table config (
  id integer primary key autoincrement,
  akey,
  avalue
);

drop table if exists root_folders;
create table root_folders (
  id integer primary key autoincrement,
  folder text not null
);
