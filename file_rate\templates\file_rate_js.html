$(document).ready( function() { 
    /**
    * upd_score for pages that have a score field
    * as soon as the change is complete, we want to update the row. 
    */
    $(".upd_score").change(function(){
        console.log(this.name);
        console.log(this.value);
        var fileId = $(this).closest('[data-file-id]').data('file-id');
        
        $.post("{{ url_for('_update_row') }}",
        {
            name: this.name,
            value: this.value,
            update_access: true
        },
        function(data, status){
            console.log("Data: " + data + "\nStatus: " + status);
        });
    });
    /**
     * upd_keyword for pages that have a keywords or comment field
     * as soon as the keyword field is updated, we want to update the row
     */
    $(".upd_keyword").change(function(){
        console.log(this.name);
        console.log(this.value);
        var fileId = $(this).closest('[data-file-id]').data('file-id');
        
        $.post("{{ url_for('_update_row') }}",
        {
            name: this.name,
            value: this.value,
            update_access: true
        },
        function(data, status){
            console.log("Data: " + data + "\nStatus: " + status);
        });
    });
    /**
     * file_delete is for pages that have a delete icon next to a media file
     * When the bottom is pressed, we want to prompt for delete confirmation
     * then we need to remove the cell and delete the underlying file.
     * While prompting for confirmation, we reduce the opacity and restore it
     * if the user cancels the delete.
     */
    $(".file_delete").click(function(){
        console.log("Delete "+this.id)
        var myId=(this.id).split("_").pop()
        myCell = document.getElementById("cell_"+myId)
        myCell.style.opacity = .6
        setTimeout(function() {
            if(confirm("Are you sure you want to delete this image?")) {
                const myNode = myCell;
                while (myNode.firstChild) {
                    myNode.removeChild(myNode.lastChild);
                }
                myCell.remove();
                //Logic to delete the item
                $.post("{{ url_for('_delete_file') }}",
                {
                    id: myId
                },
                function(data, status){
                    console.log("Data: " + data + "\nStatus: " + status);
                });
            } else {
                myCell.style.opacity = 1
            }
        }, 0);
    });
    /**
     * update_last_access updates the last_access time for a file
     * when the row is clicked.
    **/
    $(document).ready(function(){
        $("tr").click(function(){
          var fileId = $(this).data('file-id');
          $.ajax({
            url: "{{ url_for('update_last_access') }}",
            type: "POST",
            data: { file_id: fileId },
            success: function(response) {
              if (response.status === 'OK') {
                console.log('LastAccess updated successfully.');
              } else {
                console.error('Error updating LastAccess:', response.error);
              }
            },
            error: function(xhr, status, error) {
              console.error('AJAX error:', error);
            }
          });
        });
      });
    /**
     * Need to check on this one...
     */
    $("input#upd").change(function(){
        console.log(this.name);
        console.log(this.value);
        $.post("{{ url_for('_update_row') }}",
        {
            name: this.name,
            value: this.value
        },
        function(data, status){
        console.log("Data: " + data + "\nStatus: " + status);
        });
    });
    /**
     * for multiple seleted items, we want to update with general form
     * parameters. This function passes a whole list to the script, along with
     * the new values of parameters. It also updates those parameters on the page.
     * (like Score)
     */
    $("button.update").click(function(){
        console.log("Update Selected button!");
        var selx = document.querySelectorAll('input[name=select_upd]:checked');
        var all_selected = ""
        var separator = ""
        var new_keyword = document.getElementById("new_keyword").value
        var add_keyword = document.getElementById("add_keyword").value
        var new_score = parseInt(document.getElementById("new_score").value)
        var add_score = parseInt(document.getElementById("add_score").value)
        for(var i=0, n=selx.length; i<n; i++) {
            console.log(selx[i].id + ", ")
            var myId = (selx[i].id).split("_").pop()
            var myKeywordsElement = document.getElementById("upd_keyword_" + myId)
            var myScoreElement = document.getElementById("upd_score_" + myId)
            var myKeywords = myKeywordsElement.value
            var myScore = parseInt(myScoreElement.value)
            if (!myKeywords) {
                myKeywords = ""
            }
            if (!myScore) {
                myScore = 0
            }
            if (new_keyword) {
                myKeywords = new_keyword
            }
            if (new_score) {
                myScore = new_score
            }
            if (add_keyword) {
                myKeywords = myKeywords + " " + add_keyword
            }
            if (add_score) {
                myScore = myScore + add_score
            }
            myKeywords.replace("~",",")
            myKeywords.replace("|","/")
            all_selected = all_selected + separator + myId + "~" + myScore + "~" + myKeywords
            separator = "|"
            myKeywordsElement.value = myKeywords
            myScoreElement.value = myScore
        };
        console.log("all_selected=" + all_selected);
        $.post("{{ url_for('_update_selected') }}",
        {
            all_selected: all_selected,
        },
        function(data, status) {
            console.log("Data: " + data + "\nStatus: " + status);
        });
    });

    $(".dir-grid").click(function(){
        console.log("Directory button clicked");
        var myDir=(this.id).split(";").pop();
        console.log("Dir: " + myDir)
    });

    $("button.delete_selected").click(function(){
        console.log("Delete Selected button!");
        var selx = document.querySelectorAll('input[name=select_upd]:checked');
        var all_selected = ""
        var separator = ""

        if(selx.length === 0) {
            alert("Please select files to delete first.");
            return;
        }

        setTimeout(function() {
            if(confirm("Are you sure you want to delete the selected files? This will permanently delete the files from disk.")) {
                for(var i=0, n=selx.length; i<n; i++) {
                    console.log(selx[i].id + ", ")
                    var myId = (selx[i].id).split("_").pop()
                    all_selected = all_selected + separator + myId
                    separator = "|"
                };
                console.log("all_selected=" + all_selected);
                $.post("{{ url_for('_delete_selected') }}",
                {
                    all_selected: all_selected,
                },
                function(data, status) {
                    console.log("Data: " + data + "\nStatus: " + status);
                    if(data.status === 'OK') {
                        // Remove deleted rows from the table
                        selx.forEach(function(checkbox) {
                            var row = checkbox.closest('tr');
                            row.remove();
                        });
                    } else {
                        alert("Error deleting files. Please check the console for details.");
                    }
                });
            }
        }, 0);
    });

    // Select all functionality
    $("input#sel_all").click(function(){
        checkboxes = document.getElementsByName('select_upd');
        for(var i=0, n=checkboxes.length;i<n;i++) {
            checkboxes[i].checked = this.checked;
        }
    });

    /**
     * Update AccessTime when filename/path is clicked or highlighted
     */
    $("td#clip").click(function(){
        var fileId = $(this).closest('tr').data('file-id');
        updateAccessTime(fileId);
        
        // Original clipboard functionality
        $(this).focus();
        $(this).select();
        try {
            document.execCommand('copy');
            console.log("Copied to clipboard successfully!");
        } catch(err) {
            console.error("Unable to write to clipboard. :-(");
        }
    });
    
    /**
     * Update AccessTime when clicking on an image in grid view
     */
    $(".grid-image-link").click(function(e){
        var fileId = $(this).closest('[data-file-id]').data('file-id');
        updateAccessTime(fileId);
    });
    
    /**
     * Helper function to update AccessTime
     */
    function updateAccessTime(fileId) {
        if (!fileId) return;
        
        $.ajax({
            url: "{{ url_for('update_last_access') }}",
            type: "POST",
            data: { file_id: fileId },
            success: function(response) {
                if (response.status === 'OK') {
                    console.log('AccessTime updated successfully for file ID: ' + fileId);
                } else {
                    console.error('Error updating AccessTime:', response.error);
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX error:', error);
            }
        });
    }
});
