import sys
import getopt
import csv
import math
from keras.models import Sequential
from keras.layers.core import Dense, Dropout, Activation
from keras.wrappers.scikit_learn import KerasRegressor
from sklearn.model_selection import train_test_split
from keras.preprocessing import *
from keras.preprocessing.text import *
from keras.layers.advanced_activations import LeakyReLU, PReLU

# BATCH_SIZE = 4
NUM_EPOCHS = 100
MIN_TIME = 946684800            # 1/1/2000 00:00:00
MAX_TIME = 1893456000           # 1/1/2030 00:00:00


def main(argv):

    ifile = ''
    ofile = ''

    try:
        opts, remainder = getopt.getopt(argv,"hi:o:",["ifile","ofile"])
    except getopt.GetoptError:
        print("predict_score.py -i <input_file> -o <output_file>")
        exit()

    for opt, arg in opts:
        if opt in ('-i', '--ifile'):
            ifile = arg
        elif opt in ('-o', '--ofile'):
            ofile = arg
        else:
            print("predict_score.py -i <input_file> -o <output_file>")

    print("input file: {0}".format(ifile))

    Xlist = []
    Ylist = []

    tok = Tokenizer()
    try:
        incsv = csv.DictReader(open(ifile, 'r'))
        for row in incsv:
            tok.fit_on_texts([
                row.get('Directory',''),
#                row.get('FileName',''),
                row.get('Keywords',''),
                row.get('MIMEType',''),])
            next
        print(f'Number of words: {len(tok.word_index)}')
    except:
        print("Error in word-pass")

    try:
        incsv = csv.DictReader(open(ifile, 'r'))
        for row in incsv:
            score = (getfloat(row.get('Score')))
            if score > 0:
                fsize = setlow(getfloat(row.get('FileSize')))
                fsize = (math.log(fsize)/22.0)
                createtime = getfloat(row.get('FileCreateTime'))
                modifytime = getfloat(row.get('FileModifyTime'))
                accesstime = getfloat(row.get('AccessTime'))
                updatetime = getfloat(row.get('UpdateTime'))
                createtime = setlow((createtime - MIN_TIME)/MAX_TIME)
                modifytime = setlow((modifytime - MIN_TIME)/MAX_TIME)
                accesstime = setlow((accesstime - MIN_TIME)/MAX_TIME)
                updatetime = setlow((updatetime - MIN_TIME)/MAX_TIME)
                rowx = [
                    fsize,
                    createtime,
                    modifytime,
                    accesstime,
                    updatetime,
                ]
                rowx.extend(tok.texts_to_matrix([
                    row.get('Directory','')])[0].tolist())
#                rowx.extend(tok.texts_to_matrix([
#                    row.get('FileName','')])[0].tolist())
                rowx.extend(tok.texts_to_matrix([
                    row.get('Keywords','')])[0].tolist())
                rowx.extend(tok.texts_to_matrix([
                    row.get('MIMEType','')])[0].tolist())
                Xlist.append(rowx)
                Ylist.append(score)

    except:
        print("Error in array pass")

    print(f'Token Dict: {tok.word_index}')

    Ylist=[Ylist]
    
    X = np.array(Xlist)
    Y = np.array(Ylist).transpose()
    print(f'X has shape: {X.shape}')
    print(f'Y has shape: {Y.shape}')

    print(f'X: {X}')
    
    
    Xtrain, Xtest, Ytrain, Ytest = train_test_split(X, Y, test_size=0.3,
                                                random_state=42)
    print(Xtrain.shape, Xtest.shape, Ytrain.shape, Ytest.shape)


    model = Sequential()
    model.add(Dense(3000, input_shape=(Xtrain.shape[1],)))
    model.add(LeakyReLU(alpha=.001)) 
    model.add(Dropout(0.25))
    model.add(Dense(2000, activation='relu'))
    model.add(Dense(2000, activation='relu'))
#    model.add(Dense(2000, activation='relu'))
    model.add(Dense(Ytrain.shape[1], activation='linear'))

    model.compile(optimizer="adam", loss="mean_squared_error",
                  metrics=["accuracy"])
    history = model.fit(Xtrain, Ytrain, 
                        epochs=NUM_EPOCHS, verbose=1,
                        validation_data=(Xtest, Ytest))
    # history = model.fit(Xtrain, Ytrain, batch_size=BATCH_SIZE, 
    #                     epochs=NUM_EPOCHS, verbose=1,
    #                     validation_data=(Xtest, Ytest))



    
def getfloat(str):
    ans = 0
    try:
        ans = float(str)
    except:
        pass
    return ans

def setlow(fl):
    if fl<=0.0:
        fl = 0.2
    return fl
# INPUT_FILE = "./file_rate.csv"
# WORD2VEC_MODEL = "../data/GoogleNews-vectors-negative300.bin.gz"
# VOCAB_SIZE = 5000
# EMBED_SIZE = 300
# NUM_FILTERS = 256
# NUM_WORDS = 3
# BATCH_SIZE = 64
# NUM_EPOCHS = 10



# counter = collections.Counter()
# fin = open(INPUT_FILE, "rb")
# maxlen = 0
# for line in fin:
#     _, sent = line.strip().split("\t")
#     words = [x.lower() for x in nltk.word_tokenize(sent)]
#     if len(words) > maxlen:
#         maxlen = len(words)
#     for word in words:
#         counter[word] += 1
# fin.close()

# word2index = collections.defaultdict(int)
# for wid, word in enumerate(counter.most_common(VOCAB_SIZE)):
#     word2index[word[0]] = wid + 1
# vocab_sz = len(word2index) + 1
# index2word = {v:k for k, v in word2index.items()}
    
# xs, ys = [], []
# fin = open(INPUT_FILE, "rb")
# for line in fin:
#     label, sent = line.strip().split("\t")
#     ys.append(int(label))
#     words = [x.lower() for x in nltk.word_tokenize(sent)]
#     wids = [word2index[word] for word in words]
#     xs.append(wids)
# fin.close()
# X = pad_sequences(xs, maxlen=maxlen)
# Y = np_utils.to_categorical(ys)

# Xtrain, Xtest, Ytrain, Ytest = train_test_split(X, Y, test_size=0.3, 
#                                                 random_state=42)
# print(Xtrain.shape, Xtest.shape, Ytrain.shape, Ytest.shape)

# # load word2vec model
# word2vec = KeyedVectors.load_word2vec_format(WORD2VEC_MODEL, binary=True)
# embedding_weights = np.zeros((vocab_sz, EMBED_SIZE))
# for word, index in word2index.items():
#     try:
#         embedding_weights[index, :] = word2vec[word]
#     except KeyError:
#         pass

# model = Sequential()
# model.add(Embedding(vocab_sz, EMBED_SIZE, input_length=maxlen,
#                     weights=[embedding_weights],
#                     trainable=True))
# model.add(SpatialDropout1D(Dropout(0.2)))
# model.add(Conv1D(filters=NUM_FILTERS, kernel_size=NUM_WORDS,
#                  activation="relu"))
# model.add(GlobalMaxPooling1D())
# model.add(Dense(2, activation="softmax"))

# model.compile(optimizer="adam", loss="categorical_crossentropy",
#               metrics=["accuracy"])
# history = model.fit(Xtrain, Ytrain, batch_size=BATCH_SIZE,
#                     epochs=NUM_EPOCHS,
#                     validation_data=(Xtest, Ytest))              

# # plot loss function
# plt.subplot(211)
# plt.title("accuracy")
# plt.plot(history.history["acc"], color="r", label="train")
# plt.plot(history.history["val_acc"], color="b", label="validation")
# plt.legend(loc="best")

# plt.subplot(212)
# plt.title("loss")
# plt.plot(history.history["loss"], color="r", label="train")
# plt.plot(history.history["val_loss"], color="b", label="validation")
# plt.legend(loc="best")

# plt.tight_layout()
# plt.show()

# # evaluate model
# score = model.evaluate(Xtest, Ytest, verbose=1)
# print("Test score: {:.3f}, accuracy: {:.3f}".format(score[0], score[1]))

if __name__ == "__main__":
    main(sys.argv[1:])
         
