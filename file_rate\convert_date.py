import sqlite3

DB_FILE = 'file_rate\file_rate.db'

def get_db():
    rv = sqlite3.connect(DB_FILE)
    rv.row_factory = sqlite3.Row
    return rv

def convert_date_to_epoch():
    db = get_db()
    cur = db.cursor()
    cur.execute('select * from files')
    rows = cur.fetchall()
    cur.close()
    db.commit()
    columns = [d[0] for d in cur.description]
    for row in rows:
        print(row('FileCreateDate')


if __name__ == "__main__":
    convert_date_to_epoch()

