{% extends "layout.html" %}

{% block body %}
{% if session.logged_in %}

<script type="text/javascript">
$(document).ready(function() {
  // Update AccessTime when viewing an image
  updateAccessTime({{ result.id }});
  
  function updateAccessTime(fileId) {
    if (!fileId) return;
    
    $.ajax({
      url: "{{ url_for('update_last_access') }}",
      type: "POST",
      data: { file_id: fileId },
      success: function(response) {
        if (response.status === 'OK') {
          console.log('AccessTime updated successfully for file ID: ' + fileId);
        } else {
          console.error('Error updating AccessTime:', response.error);
        }
      },
      error: function(xhr, status, error) {
        console.error('AJAX error:', error);
      }
    });
  }
});
</script>

<div class="container mt-3">
  <button class="btn btn-primary" onclick="toggleFullWidth()">Full Width</button>
  <button class="btn btn-primary" onclick="toggleFullSize()">Full Size</button>
  <button class="btn btn-primary" onclick="toggleCenterFit()">Center Fit</button>
</div>

<form action="{{ url_for('show_grid') }}" method="post" data-file-id="{{ result.id }}">
  <input type="hidden" name="sfile" value="{{ result.Directory }}" />
  <table>
    <thead>
      <tr>
        <th>Full Path</th>
        <th>File Size</th>
        <th>Score</th>
        <th>In Keywords</th>
        <th>In Score</th>
      </tr>
    </thead>
    <tr>
      <td id="clip">{{ result.Directory }}/{{ result.FileName }}</td>
      <td>{{ result.FileSize|safe }}</td>
      <td>{{ result.Score|safe }}</td>
      <td>ID: {{ id }}</td>
      <td><input id="upd_keywords" type="text" name="in_Keywords_{{result.id}}"
        value="{{ result.Keywords|safe }}" class="upd_keyword"></td>
      <td><input id="upd_score" type="text" name="in_Score_{{result.id}}"
        value="{{ result.Score|safe }}" class="upd_score"></td>
      <!-- the following needs the parameters from the filename to directory-->
      <td><button type="submit" class="btn btn-primary">
            Directory</button></td>
    </tr>
  </table>

  <div class="imgbox mt-3">
    <img id="displayedImage" class="center-fit" src="{{ url_for('show_image') }}?image_file={{ result.Directory }}/{{ result.FileName }}">
  </div>
</form>

{% else %}
  <h4>Login first to see image.</h4>
  <a href={{ url_for('login') }}>Login</a>
{% endif %}
{% endblock %}
