    <dl>
  <div class="row small">
      <div class="col-md-4">
	<dt>File Path Like:
  	  <dd><input type="text" size="50" name="sfile" value="{{result.sfile}}">
	<dt>Keywords Like:
	  <dd><input type="text" size="50" name="scomment" value="{{result.scomment}}">
	<dt>Extra SQL:
	  <dd><input type="text" size="70" name="ssql" value="{{result.ssql}}">    
      </div>
      <div class="col-md-2">      
	<dt>Minimum Score:
	  <dd><input type="text" cols="60" name="minscore" value="{{result.minscore}}">
	<dt>Maximum Score:
	  <dd><input type="text" cols="60" name="maxscore" value="{{result.maxscore}}">
	<dt>Minimum Create Time:
	  <dd><input type="text" cols="60" name="mincreatetime" value="{{result.mincreatetime}}">
	<dt>Maximum Create Time:
	  <dd><input type="text" cols="60" name="maxcreatetime" value="{{result.maxcreatetime}}">
	<dt>Minimum Mod Time:
	  <dd><input type="text" cols="60" name="minmodtime" value="{{result.minmodtime}}">
	<dt>Maximum Mod Time:
	  <dd><input type="text" cols="60" name="maxmodtime" value="{{result.maxmodtime}}">
      </div>
      <div class="col-md-2">      
	<dt>Minimum Size:
	  <dd><input type="text" cols="60" name="minsize" value="{{result.minsize}}">
	<dt>Maximum Size:
	  <dd><input type="text" cols="60" name="maxsize" value="{{result.maxsize}}">

        <dt>Sort Desc:
          {% if result.desc==1 %}
            <input type="checkbox" name="descending" value="" checked="checked">
	      {% else %}
	    <input type="checkbox" name="descending" value="">
	      {% endif %}
        <dt>Deleted:
          {% if result.deleted==1 %}
            <input type="checkbox" name="deleted" value="" checked="checked">
	      {% else %}
	    <input type="checkbox" name="deleted" value="">
	      {% endif %}
	<dt>Number of Rows:
	  <dd><input type="text" cols="30" name="nrows" value="{{result.nrows}}">
      </div>
      <div class="col-md-4">
	<dt>File Path Not Like:
	  <dd><input type="text" cols="60" name="nfile" value="{{result.nfile}}">
	<dt>Keywords Not Like:
	  <dd><input type="text" cols="60" name="ncomment" value="{{result.ncomment}}">
        <dt>Sort Order:
	  <select name="sort_order">
	    <option value="random" {{result.sortsel["random"]}} >Random</option>
	    <option value="score" {{result.sortsel["score"]}} >Score</option>
	    <option value="predict" {{result.sortsel["predict"]}} >Predicted_score</option>
	    <option value="created" {{result.sortsel["created"]}} >Created</option>
	    <option value="modified" {{result.sortsel["modified"]}} >Modified</option>
	    <option value="edited" {{result.sortsel["edited"]}} >Edited</option>
	    <option value="accessed" {{result.sortsel["accessed"]}} >Accessed</option>
	  </select>
	  
	  <dd><input name="submit" type="submit" value="search_form">
      </div>
  </div>
  </dl>
