# Compiled source #
###################
*.com
*.class
*.dll
*.exe
*.o
*.so
# Packages #
############
# it's better to unpack these files and commit the raw source
# git has its own built in compression methods
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# Logs and databases #
######################
*.log
### *.sql
*.sqlite

# OS generated files #
######################
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
*.db

# My stuff #
############
*.save*
fr_env/
.git*/
**/*cache*
data/
*~
*.csv
*.202*
get*.cmd
file_rate/templates_save/*
venv/*

### Python ###
## Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class
#
## Distribution / packaging
#.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
#
## PyInstaller
##  Usually these files are written by a python script from a template
##  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec
#
## Installer logs
pip-log.txt
pip-delete-this-directory.txt
#
## Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/
#
## Translations
*.mo
*.pot
#
## Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal
#
## Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

