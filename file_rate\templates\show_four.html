{% extends "layout.html" %}
{% block body %}
{% if session.logged_in %}
<script>
{% include "file_rate_js.html" %}

// Show Four specific JavaScript
$(document).ready(function() {
    var clickOrder = [];
    var maxClicks = 4;
    
    // Handle image clicks for ranking
    $('.ranking-image').click(function() {
        var imageId = $(this).data('image-id');
        var currentRank = $(this).find('.rank-number').text();
        
        // If already ranked, remove from order and clear number
        if (currentRank) {
            var rankIndex = parseInt(currentRank) - 1;
            clickOrder.splice(rankIndex, 1);
            // Update all subsequent ranks
            for (var i = rankIndex; i < clickOrder.length; i++) {
                var nextImageId = clickOrder[i];
                $('#rank-' + nextImageId).text(i + 1);
            }
            $('#rank-' + imageId).text('');
        } else {
            // Add new rank if we haven't reached max
            if (clickOrder.length < maxClicks) {
                clickOrder.push(imageId);
                $('#rank-' + imageId).text(clickOrder.length);
            }
        }
        
        // Enable/disable submit button
        $('#submit-ranking').prop('disabled', clickOrder.length !== maxClicks);
    });
    
    // Reset button
    $('#reset-ranking').click(function() {
        clickOrder = [];
        $('.rank-number').text('');
        $('#submit-ranking').prop('disabled', true);
        $('#results-section').hide();
    });
    
    // Submit ranking
    $('#submit-ranking').click(function() {
        if (clickOrder.length === maxClicks) {
            $.post("{{ url_for('_calculate_scores') }}", {
                ranking: clickOrder.join(','),
                scored_images: $('#scored-images').val(),
                unscored_images: $('#unscored-images').val()
            }, function(data) {
                if (data.status === 'OK') {
                    displayResults(data.results);
                } else {
                    alert('Error calculating scores: ' + data.error);
                }
            });
        }
    });
    
    // Confirm new scores
    $('#confirm-scores').click(function() {
        var newScores = [];
        $('.new-score').each(function() {
            newScores.push({
                id: $(this).data('image-id'),
                score: $(this).val()
            });
        });
        
        $.post("{{ url_for('_save_new_scores') }}", {
            scores: JSON.stringify(newScores)
        }, function(data) {
            if (data.status === 'OK') {
                alert('Scores saved successfully!');
                location.reload();
            } else {
                alert('Error saving scores: ' + data.error);
            }
        });
    });
    
    // Cancel new scores
    $('#cancel-scores').click(function() {
        $('#results-section').hide();
    });
    
    function displayResults(results) {
        var html = '<h4>Proposed New Scores</h4><div class="row">';
        for (var i = 0; i < results.length; i++) {
            var result = results[i];
            html += '<div class="col-md-3 text-center mb-3">';
            html += '<img src="' + result.image_url + '" width="150" class="img-thumbnail"><br>';
            html += '<small>Rank: ' + result.rank + '</small><br>';
            html += '<small>Old Score: ' + (result.old_score || 'None') + '</small><br>';
            html += '<label>New Score:</label><br>';
            html += '<input type="number" class="new-score form-control" data-image-id="' + result.id + '" value="' + result.new_score + '">';
            html += '</div>';
        }
        html += '</div>';
        html += '<div class="text-center mt-3">';
        html += '<button id="confirm-scores" class="btn btn-success">Confirm</button> ';
        html += '<button id="cancel-scores" class="btn btn-secondary">Cancel</button>';
        html += '</div>';
        
        $('#results-content').html(html);
        $('#results-section').show();
        
        // Re-bind events for new buttons
        $('#confirm-scores').click(function() {
            var newScores = [];
            $('.new-score').each(function() {
                newScores.push({
                    id: $(this).data('image-id'),
                    score: $(this).val()
                });
            });
            
            $.post("{{ url_for('_save_new_scores') }}", {
                scores: JSON.stringify(newScores)
            }, function(data) {
                if (data.status === 'OK') {
                    alert('Scores saved successfully!');
                    location.reload();
                } else {
                    alert('Error saving scores: ' + data.error);
                }
            });
        });
        
        $('#cancel-scores').click(function() {
            $('#results-section').hide();
        });
    }
});
</script>

<div class="container-fluid">
<form action="{{ url_for('show_four') }}" method="post" class="add-entry">
  {% include "search_contents.html" %}
  <div class="text-center mb-3">
    <input name="submit" type="submit" value="Get Four Images" class="btn btn-primary">
  </div>
</form>

{% if images %}
<div class="card mt-4">
  <div class="card-header bg-light">
    <h5 class="mb-0">Show Four - Rank by Preference (Click images in order: 1=favorite, 4=least favorite)</h5>
  </div>
  <div class="card-body">
    <!-- Hidden inputs to track scored vs unscored images -->
    <input type="hidden" id="scored-images" value="{{ scored_ids|join(',') }}">
    <input type="hidden" id="unscored-images" value="{{ unscored_ids|join(',') }}">
    
    <!-- First row of images -->
    <div class="row mb-4">
      {% for image in images[:2] %}
      <div class="col-md-6 text-center">
        <div class="position-relative d-inline-block">
          <img src="{{ url_for('show_image') }}?image_file={{ image.Directory }}/{{ image.FileName }}" 
               width="300" class="img-thumbnail ranking-image" 
               data-image-id="{{ image.id }}" style="cursor: pointer;">
          <div class="rank-number position-absolute top-0 start-0 bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" 
               id="rank-{{ image.id }}" style="width: 30px; height: 30px; font-weight: bold; font-size: 16px;"></div>
        </div>
        <div class="mt-2">
          <small class="text-muted">{{ image.FileName }}</small><br>
          <small class="text-muted">Current Score: {{ image.Score if image.Score else 'None' }}</small>
        </div>
      </div>
      {% endfor %}
    </div>
    
    <!-- Second row of images -->
    <div class="row mb-4">
      {% for image in images[2:] %}
      <div class="col-md-6 text-center">
        <div class="position-relative d-inline-block">
          <img src="{{ url_for('show_image') }}?image_file={{ image.Directory }}/{{ image.FileName }}" 
               width="300" class="img-thumbnail ranking-image" 
               data-image-id="{{ image.id }}" style="cursor: pointer;">
          <div class="rank-number position-absolute top-0 start-0 bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" 
               id="rank-{{ image.id }}" style="width: 30px; height: 30px; font-weight: bold; font-size: 16px;"></div>
        </div>
        <div class="mt-2">
          <small class="text-muted">{{ image.FileName }}</small><br>
          <small class="text-muted">Current Score: {{ image.Score if image.Score else 'None' }}</small>
        </div>
      </div>
      {% endfor %}
    </div>
    
    <!-- Control buttons -->
    <div class="text-center">
      <button id="reset-ranking" class="btn btn-warning">Reset</button>
      <button id="submit-ranking" class="btn btn-success" disabled>Submit Ranking</button>
    </div>
  </div>
</div>

<!-- Results section (hidden initially) -->
<div id="results-section" class="card mt-4" style="display: none;">
  <div class="card-header bg-info text-white">
    <h5 class="mb-0">Score Adjustment Results</h5>
  </div>
  <div class="card-body" id="results-content">
    <!-- Results will be populated by JavaScript -->
  </div>
</div>

{% elif request.method == 'POST' %}
<div class="alert alert-warning mt-4">
  <h5>No suitable images found</h5>
  <p>Could not find enough images matching your criteria. Please adjust your search parameters.</p>
  <ul>
    <li>Need at least 2 images with scores in the specified range</li>
    <li>Need at least 2 images with no scores (or outside the score range)</li>
  </ul>
</div>
{% endif %}

</div>
{% else %}
<div class="container mt-5">
  <div class="card">
    <div class="card-body text-center">
      <h4 class="mb-3">Login first to see Show Four</h4>
      <a href={{ url_for('login') }} class="btn btn-primary">Login</a>
    </div>
  </div>
</div>
{% endif %}
{% endblock %}
