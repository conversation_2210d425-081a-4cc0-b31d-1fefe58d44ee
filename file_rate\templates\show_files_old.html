{% extends "layout.html" %}
{% block body %}
{% if session.logged_in %}
<script type=text/javascript>
  $(document).ready(function(){
    $("input#upd").change(function(){
      console.log(this.name);
      console.log(this.value);
      $.post("{{ url_for('_update_row') }}",
      {
        name: this.name,
        value: this.value
      },
      function(data, status){
        console.log("Data: " + data + "\nStatus: " + status);
      });
    });
  });

  $(document).ready(function(){
    $("td#clip").click(function(){
      $(this).focus();
      $(this).select();
      try {
        document.execCommand('copy');
        console.log("Copied to clipboard successfully!");
      } catch(err) {
         console.error("Unable to write to clipboard. :-(");
      }
    });
  });

</script>
<div class="container-fluid">
<form action="{{ url_for('show_files') }}" method="post" class="add-entry">
  {% include "search_contents.html" %}
</form>
  </div>
  <p class="small">{{result.result_count}} records returned.</p>
  <table class="table-striped sortable">
    <thead>
      <tr>
        <th>Full Path</th>
        <th>File Size</th>
        <th>Score</th>
        <th>In Keywords</th>
        <th>In Score</th>
      </tr>
    </thead>
    {% for file in result.get('files',[]) %}
    <tr>
      <td id="clip">{{ file.Directory }}/{{ file.FileName }}</td>
      <td>{{ file.FileSize|safe }}</td>
      <td>{{ file.Score|safe }}</td>
      <td><input id="upd" type="text" name="in_Keywords_{{file.id}}"
		 value="{{ file.Keywords|safe }}"</td>
      <td><input id="upd" type="text" name="in_Score_{{file.id}}"
		 value="{{ file.Score|safe }}"</td>
    </tr>
    {% else %}
    <tr><td><em>Unbelievable.  No files here so far</em></td></tr>
    {% endfor %}
  </table>
{% else %}
  <h4>Login first to see list.</h4>
  <a href={{ url_for('login') }}>Login</a>
{% endif %}
{% endblock %}
