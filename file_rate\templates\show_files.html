{% extends "layout.html" %}
{% block body %}
{% if session.logged_in %}
<script type=text/javascript>
  {% include "file_rate_js.html" %}
  $(document).ready(function(){
    
    $("td#clip").click(function(){
      $(this).focus();
      $(this).select();
      try {
        document.execCommand('copy');
        console.log("Copied to clipboard successfully!");
      } catch(err) {
         console.error("Unable to write to clipboard. :-(");
      }
    });
  });

</script>
<!-- Add the resizable columns CSS -->
<link rel="stylesheet" type="text/css" href="{{ url_for('static', filename='css/resizable-columns.css') }}">
<!-- Add the resizable columns JavaScript -->
<script type="text/javascript" src="{{ url_for('static', filename='js/resizable-columns.js') }}"></script>

<div class="container-fluid">
<form action="{{ url_for('show_files') }}" method="post" class="add-entry">
  {% include "search_contents.html" %}
  {% include "multi_update.html" %}
  <input type="checkbox" id="sel_all" value="on" name="sel_all" class="form-check-input" />
  <label for="sel_all" class="form-check-label">Select All</label>
</form>
</div>
<div class="container-fluid mt-3">
  <div class="card">
    <div class="card-header bg-light">
      <div class="d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Files</h5>
        <span class="badge bg-primary">{{result.result_count}} records</span>
      </div>
    </div>
    <div class="card-body">
      <div class="table-responsive">
        <table class="table table-striped table-hover sortable">
          <thead class="table-dark">
            <tr>
              <th class="px-2" style="width: 50px;">Select</th>
              <th class="px-2" style="width: 300px;">Full Path</th>
              <th class="px-2" style="width: 80px;">File Size</th>
              <th class="px-2" style="width: 60px;">Score</th>
              <th class="px-2" style="width: 150px;">Create</th>
              <th class="px-2" style="width: 150px;">Modify</th>
              <th class="px-2" style="width: 150px;">Last Access</th>
              <th class="px-2" style="width: 200px;">Keywords</th>
              <th class="px-2" style="width: 60px;">Score</th>
            </tr>
          </thead>
          <tbody>
          {% for file in result.get('files',[]) %}
          <tr data-file-id="{{ file.id }}" class="align-middle">
            <td class="px-2"><input id="check_{{file.id}}" type="checkbox" name="select_upd" value="on" class="form-check-input"></td>
            <td id="clip" class="px-2 text-truncate" style="max-width: 300px;" title="{{ file.Directory }}/{{ file.FileName }}">{{ file.Directory }}/{{ file.FileName }}</td>
            <td class="px-2">{{ file.FileSize|safe }}</td>
            <td class="px-2">{{ file.Score|safe }}</td>
            <td class="px-2">{{ file.FileCreateAscii|safe }}</td>
            <td class="px-2">{{ file.FileModifyAscii|safe }}</td>
            <td class="px-2">{{ file.AccessTimeAscii|safe }}</td>
            <td class="px-2"><input id="upd_keyword_{{file.id}}" type="text" class="upd_keyword form-control form-control-sm"
            name="in_Keywords_{{file.id}}" value="{{ file.Keywords|safe }}"></td>
            <td class="px-2"><input id="upd_score_{{file.id}}" type="text" class="upd_score form-control form-control-sm"
            name="in_Score_{{file.id}}" value="{{ file.Score|safe }}"></td>
          </tr>
          {% else %}
          <tr><td colspan="9" class="text-center py-3"><em>No files found</em></td></tr>
          {% endfor %}
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>
{% else %}
<div class="container mt-5">
  <div class="card">
    <div class="card-body text-center">
      <h4 class="mb-3">Login first to see list</h4>
      <a href={{ url_for('login') }} class="btn btn-primary">Login</a>
    </div>
  </div>
</div>
{% endif %}
{% endblock %}
