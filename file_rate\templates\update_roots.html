{% extends "layout.html" %}
{% block body %}
{% if session.logged_in %}

<div class="container-fluid">
<form action="{{ url_for('update_roots') }}" method="post" class="add-entry">

  
</form>
  </div>

  <table class="table-striped sortable">
    <thead>
      <tr>
        <th>Folder</th>
      </tr>
    </thead>
    {% for row in roots %}
    <tr>
      <td>{{ row['folder'] }}</td>
    </tr>
    {% else %}
    <tr><td><em>Unbelievable.  No files here so far</em></td></tr>
    {% endfor %}
  </table>
{% else %}
<h4>Login first to see list.</h4>
{% endif %}
{% endblock %}
