import getopt
import os
import re
import sqlite3
import sys
import time
import traceback
from calendar import timegm

def main(argv):
    query = ""
    try:
        opts, remainder = getopt.getopt(argv,"hq:",["query"])
    except getopt.GetoptError:
        help()
        exit()

    for opt, arg in opts:
        if opt in ('-q', '--query'):
            query = arg
        else:
            help()

    print("Query:",query)

    '''
    We would like to take:
    path=*abc* ==>  (Directory like '%abc%' OR FileName like '%abc%')
    dir=*foo*  ==>  (Directory like '%foo%')
    file=*bar*ttt*  ==> (FileName like '%bar%ttt%')
    score>92  ==>  (Score > 92)
    modified>2018-10-01  ==> (FileModifyTime > <epoch time for 10/1/2018 00:00:00>)
    key=*mop  ==> (Key like '%mop'
    key=*xyz* OR key=*abc*   ==> (Key like '%xyz%' OR Key like '%abc%')
    NOT score=99  ==> (NOT Score = 99)
    file=Opera   ==> (FileName like 'Opera')


    etc...
    '''


def replace_pairs(query):

    re.sub(r'
    m = re.match

    
def help():
    print("{0} -i <input_file> -o <output_file>".
          format(os.path.basename(__file__)))


if __name__ == "__main__":
    main(sys.argv[1:])



