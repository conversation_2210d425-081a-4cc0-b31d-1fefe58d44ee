<!doctype html>
<title>File_Rate</title>
<link rel="shortcut icon" href="{{ url_for('static', filename='favicon.ico') }}"><meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
<!-- <link rel="stylesheet" type="text/css"
       href="{{ url_for('static', filename='style.css') }}">
-->
<link rel="stylesheet" type="text/css"
      href="{{ url_for('static', filename='css/bootstrap.min.css') }}">
<link rel="stylesheet" type="text/css"
      href="{{ url_for('static', filename='css/resizable-columns.css') }}">
<style>
  * {
      margin: 0;
      padding: 0;
  }
  .imgbox {
      display: grid;
      height: 100%;
  }
  .center-fit {
      max-width: 100%;
      max-height: 100vh;
      margin: auto;
  }
</style>   
<script type=text/javascript src="{{
      url_for('static', filename='jquery-3.2.1.min.js') }}">
</script>
<script type=text/javascript src="{{
      url_for('static', filename='js/bootstrap.min.js') }}">
</script>
<script type=text/javascript src="{{
      url_for('static', filename='sorttable.js') }}">
</script>
<div class="container-fluid">
  <h1>File_Rate</h1>
  <div class="metanav">
    <div class="row">
    <div class="col-sm-8">
      <a href="{{ url_for('show_index') }}">Home</a>
    </div>
      <div class="col-sm-4">
  {% if not session.logged_in %}
    <a href="{{ url_for('login') }}">log in</a>
  {% else %}
    <a href="{{ url_for('logout') }}">log out</a>
    {% endif %}
    </div>
    </div>
  </div>
  {% for message in get_flashed_messages() %}
    <div class="flash">{{ message }}</div>
  {% endfor %}
  {% block body %}{% endblock %}
</div>
