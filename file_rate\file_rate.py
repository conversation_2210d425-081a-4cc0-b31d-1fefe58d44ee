import click
import csv
import json
import os
import random
import re
import socket
import sqlite3
import subprocess
import time
import traceback
from calendar import timegm
from flask import Flask, request, session, g, redirect, url_for, abort, \
     render_template, flash, jsonify, send_file
#from file_rate.fr_search import get_parser, make_sql
### XXXX

#search = get_parser()
app = Flask(__name__)
app.config.from_object(__name__)

print("Name: (0)".format(app))
app.config.update(dict(
  DATABASE=os.path.join(
    app.root_path,
    'file_rate_{0}.db'.format(socket.gethostname())),
  SECRET_KEY='development key',
  USERNAME='admin',
  PASSWORD='dummy123'
))
app.config.from_envvar('FILE_RATE_SETTINGS', silent=True)

def connect_db():
  """Connects to the database."""
  rv = sqlite3.connect(app.config['DATABASE'])
  rv.row_factory = sqlite3.Row
  return rv
  
def get_db():
  if not hasattr(g, 'sqlite_db'):
    print("Reopen db")
    g.sqlite_db = connect_db()
  return g.sqlite_db

@app.teardown_appcontext
def close_db(error):
  if hasattr(g, 'sqlite_db'):
    g.sqlite_db.close()

def init_db():
  db = get_db()
  with app.open_resource('schema.sql', mode='r') as f:
    db.cursor().executescript(f.read())
  db.commit()
  
@app.cli.command('initdb')
def initdb_command():
  init_db()
  print('Initialized the database.')

@app.cli.command('set_delete')
def set_delete():
  db = get_db()
  cur = db.cursor()
  cur.execute('select * from files')
  rows = cur.fetchall()
  cur.close()
  db.commit()
#  columns = [d[0] for d in cur.description]
  for row in rows:
    myfile = os.path.join(row['Directory'], row['FileName'])
    if ( not os.path.isfile(myfile) ):
      id = row['id']
      print("Deleting {0}".format(id))
      sqlcmd = "update files set Deleted = 1 where id = ?"
      cur = db.execute(sqlcmd, [id])
      db.commit()

  
@app.cli.command('dumpdb')
def dumpdb():
  data_json = []
  db = get_db()
  cur = db.cursor()
  cur.execute('select * from files')
  rows = cur.fetchall()
  cur.close()
  db.commit()
  columns = [d[0] for d in cur.description]
  for row in rows:
    data_json.append(dict(zip(columns, row)))
  f = open('file_rate.json', mode='w')
  f.write(json.dumps(data_json, indent=4))
  f.close()

@app.cli.command('dumpcsv')
@click.option('--file')
def dumpcsv(file):
  data_array = []
  db = get_db()
  cur = db.cursor()
  cur.execute('select * from files')
  rows = cur.fetchall()
  cur.close()
  db.commit()
  columns = [d[0] for d in cur.description]
  for row in rows:
    data_array.append(dict(zip(columns, row)))
  with open(file, 'w') as output_file:
    dict_writer = csv.DictWriter(output_file, columns)
    dict_writer.writeheader()
    dict_writer.writerows(data_array)

@app.cli.command('migrate1')
@click.option('--file')
def migrate1(file):
  db = get_db()
  length = 0
  with open(file, encoding="utf8") as json_file:
    json_data = json.load(json_file)
    length = len(json_data)
    for i in range(0,length):
      Directory = json_data[i].get('Directory','')
      FileName = json_data[i].get('FileName','')
      FileModifyDate = json_data[i].get('FileModifyDate','')
      FileSize = json_data[i].get('FileSize','')
      Comment = json_data[i].get('Comment','')
      SharedUserRating = json_data[i].get('SharedUserRating','')
      Score = json_data[i].get('Score','')
      Keywords = json_data[i].get('Keywords','')

      # kbr = re.compile("^(.*?)([^\\/\\\\]*)$")
      # m = kbr.match()
      # if m:
      #   if Directory == '':
      #     Directory = m.group(1)
      #   if FileName == '':
      #     FileName = m.group(2)

      pass
      
### continue this later

@app.cli.command('convert_dates')
def convert_dates():
  db = get_db()
  cur = db.cursor()
  cur.execute('select * from files')
  rows = cur.fetchall()
  cur.close()
  db.commit()
  match = re.compile(r'^(.*)\:([^\:]*)$')
  for row in rows:
    id = row['id']
    modstring = row['FileModifyDate']
    modstring = match.sub(r'\1\2',modstring) 
    modtime = int(time.mktime(
      time.strptime(modstring, "%Y:%m:%d %H:%M:%S%z")))
    createstring = row['FileCreateDate']
    createstring = match.sub(r'\1\2',createstring) 
    createtime = int(time.mktime(
      time.strptime(createstring, "%Y:%m:%d %H:%M:%S%z")))
    sqlcmd = "update files set FileModifyTime=?, FileCreateTime=? where id = ?"
    cur = db.execute(sqlcmd, [modtime,createtime, id])
    db.commit()

@app.cli.command('load')
@click.option('--file')
def load_json_command(file):
  print('loading file: {}'.format(file))
  db = get_db()
  match = re.compile(r'^(.*)\:([^\:]*)$')
  length = 0
  with open(file, encoding="utf8") as json_file:
    json_data = json.load(json_file)
    length = len(json_data)
    for i in range(0,length):
      SourceFile = json_data[i].get('SourceFile','')
      FileName = json_data[i].get('FileName','')
      Directory = json_data[i].get('Directory','')
      FileSize = json_data[i].get('FileSize','')
      Comment = json_data[i].get('Comment','')
      SharedUserRating = json_data[i].get('SharedUserRating','')
      MIMEType = json_data[i].get('MIMEType','')
      ImageSize = json_data[i].get('ImageSize','')
      FileModifyDate = json_data[i].get('FileModifyDate')
#      modstring = match.sub(r'\1\2',FileModifyDate)
      modstring = FileModifyDate
      modtime = int(time.mktime(
        time.strptime(modstring, "%Y:%m:%d %H:%M:%S%z")))
      FileCreateDate = json_data[i].get('FileCreateDate')
#      createstring = match.sub(r'\1\2',FileCreateDate)
      createstring = FileCreateDate
      createtime = int(time.mktime(
        time.strptime(createstring, "%Y:%m:%d %H:%M:%S%z")))
      if MIMEType=='':
        if '.jpg' in FileName:
          MIMEType='image/jpg'
        if '.tif' in FileName:
          MIMEType='image/tiff'
        if '.png' in FileName:
          MIMEType='image/png'
        if '.gif' in FileName:
          MIMEType='image/gif'
      if FileName == '' or Directory == '':
        kbr = re.compile("^(.*?)([^\\/\\\\]*)$")
        m = kbr.match(SourceFile)
        if m:
          if Directory == '':
            Directory = m.group(1)
          if FileName == '':
            FileName = m.group(2)
      Key = "{0}+{1}".format(FileName,FileSize)
      curr = db.execute("""insert or ignore into files
           (Key, Directory, FileName, FileModifyDate, FileCreateDate,
            FileSize, Comment, SharedUserRating, Score, Keywords,
            MIMEType, ImageSize, FileModifyTime, FileCreateTime)
            values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)""",
            [Key,
             Directory,
             FileName,
             FileModifyDate,
             FileCreateDate,
             FileSize,
             Comment,
             SharedUserRating,
             SharedUserRating,
             Comment,
             MIMEType,
             ImageSize,
             modtime,
             createtime])

      if curr.rowcount==0:
          curr = db.execute("""update files set
              Directory=?,
              FileModifyDate=?,
              FileCreateDate=?,
              Comment=?,
              SharedUserRating=?,
              MimeType=?,
              ImageSize=?,
              FileModifyTime=?,
              FileCreateTime=?
              where Key=? """,
              [Directory,
               FileModifyDate,
               FileCreateDate,
               Comment,
               SharedUserRating,
               MIMEType,
               ImageSize,
               modtime,
               createtime,
               Key])

  db.commit()
  print("Loaded {} file records".format(length))


@app.route('/')
def show_index():
    return render_template('index.html')

@app.route('/test/<page>')
def test_page(page):
    return render_template("{0}.html".format(page))

@app.route('/show_image/', methods=['GET'])
def show_image():
    image_file = request.args.get('image_file','')
    try:
        return send_file(image_file)
    except Exception as e:
        return str(e)

@app.route('/show_image_form/', methods=['GET'])
def show_image_form():
#    image_file = request.args.get('image_file','')
    id = request.args.get('id','')
    db = get_db()
    sqlstring = "select * from files where id=?"
    cur = db.execute(sqlstring, [id])
    rows = cur.fetchall()
    print(f'getting image info for {id}. Should have 1:actual={len(rows)}')
    file = {}
    file['id'] = id
    if len(rows)>0:
      row = rows[0]
      file['Directory'] = row['Directory']
      file['FileName'] = row['FileName']
      file['Score'] = row['Score']
      file['Keywords'] = row['Keywords']
      image_file = os.path.join(file['Directory'], file['FileName'])
      file['image_url'] = send_file(image_file)
      
    return render_template('show_image_form.html', result=file)

    #   result = {
    #     return send_file(image_file)
    # except Exception as e:
    #     return str(e)


      
@app.route('/update_roots', methods=['GET', 'POST'])
def update_roots():
    db = get_db()
  
    sqlstring = "select folder from root_folders"
                          
    print( "SQL: {}".format(sqlstring) )
    cur = db.execute(sqlstring)
    roots = cur.fetchall()
    result_count = len(roots)
    return render_template('update_roots.html', roots=roots)


  
@app.route('/search_grid', methods=['GET', 'POST'])
def show_grid():
    dict = get_search_criteria("show_grid")
    return render_template('show_grid.html', result=dict)
  
@app.route('/search', methods=['GET', 'POST'])
def show_files():
    dict = get_search_criteria("show_files")
    return render_template('show_files.html', result=dict)

def get_search_criteria(search_type):
    db = get_db()
    error = None
    sfile = "%"
    scomment = "%"
    ssql = ""
    nfile = ""
    ncomment = ""
    minscore = 0
    maxscore = 100
    minmodtime = 0
    maxmodtime = 25000000000
    mincreatetime = 0
    maxcreatetime = 25000000000
    minsize = 0
    maxsize = 10000000000
    nrows = 150
    null_score = "Score!='' and "
    null_keyword = "Keywords!='' and "
    orderstr = ""
    descstr = "ASC"
    delstr = "(Deleted IS NULL or NOT Deleted=1)"
    is_deleted = []
    is_descending = []
    sort_order = ""
    sortsel = {}
    extra_sql = ''
    if request.method == 'POST':
        sfile = "%{}%".format(request.form.get('sfile',''))
        scomment = "%{}%".format(request.form.get('scomment',''))
        ssql = "{}".format(request.form.get('ssql',''))
        nfile = "{}".format(request.form.get('nfile',''))
        ncomment = "{}".format(request.form.get('ncomment',''))
        minscore = int("{}".format(request.form.get('minscore',0)))
        maxscore = int("{}".format(request.form.get('maxscore',1000)))
        minsize = int("{}".format(request.form.get('minsize',0)))
        maxsize = int("{}".format(request.form.get('maxsize',10000000000)))
        is_deleted = request.form.getlist('deleted')
        is_descending = request.form.getlist('descending')
        sort_order = "{}".format(request.form.get('sort_order','Random'))
        nrows = int("{}".format(request.form.get('nrows',150)))
        if not nrows:
          nrows = 150
        if nrows < 5:
          nrows = 5
        try:
          mincreatetime = timegm(time.strptime(
            request.form.get('mincreatetime'),'%m/%d/%Y %H:%M:%S'))
        except Exception as e:
          print("Error: ", request.form.get('mincreatetime'), e)
        try:
          maxcreatetime = timegm(time.strptime(
            request.form.get('maxcreatetime'),'%m/%d/%Y %H:%M:%S'))
        except:
          pass
        try:
          minmodtime = timegm(time.strptime(
            request.form.get('minmodtime'),'%m/%d/%Y %H:%M:%S'))
        except:
          pass
        try:
          maxmodtime = timegm(time.strptime(
            request.form.get('maxmodtime'),'%m/%d/%Y %H:%M:%S'))
        except:
          pass
        try:
      #    psql = search.parseString(ssql)
      #    extra_sql = ' and ' + make_sql(psql.asList())
      #    print("XTRA_SQL:", extra_sql)
          if len(ssql) > 0 and not ssql.isspace():
            extra_sql = ' and ' + ssql
            print("XTRA_SQL:", extra_sql)
        except:
          pass
    print( "sort_order is {}".format(sort_order))
    if minscore == 0:
        null_score = "Score='' or "
    if ncomment == '':
        null_keyword = "Keywords is null or Keywords='' or "
    if nfile != '':
        nfile = "%{}%".format(nfile)
    if is_deleted:
        delstr = "Deleted=1"
    if is_descending:
        descstr = "DESC"
    sortsel[sort_order] = 'selected="selected"'
    if sort_order=='random':
        orderstr = "order by random()"
    elif sort_order=='score':
        orderstr = "order by Score {0}".format(descstr)
    elif sort_order=='predict':
        orderstr = "order by Predicted_Score {0}".format(descstr)
    elif sort_order=='created':
        orderstr = "order by FileCreateTime {0}".format(descstr)
    elif sort_order=='modified':
        orderstr = "order by FileModifyTime {0}".format(descstr)
    elif sort_order=='edited':
        orderstr = "order by UpdateTime {0}".format(descstr)
    elif sort_order=='accessed':
        orderstr = "order by AccessTime {0}".format(descstr)

    if search_type=='show_files':
        mime = "video%"
    elif search_type=='show_grid':
        mime = "image%"
        
    sqlstring = """select * from files where (FileName like ? 
                or Directory like ?) 
                and Keywords like ? 
                and ({0} Score >= ?) 
                and ({0} Score <= ?)
                and (FileSize >= ?) 
                and (FileSize <= ?)
                and (FileCreateTime >= ?)
                and (FileCreateTime <= ?)
                and (FileModifyTime >= ?)
                and (FileModifyTime <= ?)
                and FileName not like ? 
                and Directory not like ?
                and MIMEType like ? 
                and ({1} Keywords not like ?)
                and {2}
                {3}
                {4} limit {5}""".format(null_score,
                                        null_keyword,
                                        delstr,
                                        extra_sql,
                                        orderstr,
                                        nrows)
    print( "SQL: {}".format(sqlstring) )
    print( "Params: {0},{1},{2},{3},{4},{5},{6},{7},{8},{9},{10},{11},{12},{13}".format(
       sfile, scomment, minscore, maxscore, minsize, mincreatetime,
       maxcreatetime, minmodtime, maxmodtime,
       maxsize, nfile, mime, ncomment, ssql))
    cur = db.execute(sqlstring,
                     [sfile, sfile, scomment, minscore, maxscore,
                      minsize, maxsize, mincreatetime, maxcreatetime,
                      minmodtime, maxmodtime, nfile, nfile,mime, ncomment]
    )
#    files = cur.fetchall()
    files = [dict(row) for row in cur.fetchall()]
    sfile = re.sub(r'^[\%\s]*(.*?)[\%\s]*$',
                   r'\1',
                   sfile)
    scomment = re.sub(r'^[\%\s]*(.*?)[\%\s]*$',
                      r'\1',
                      scomment)
    nfile = re.sub(r'^[\%\s]*(.*?)[\%\s]*$',
                      r'\1',
                      nfile)
    result_count = len(files)
    for afile in files:
#      print(dir(afile))
      amodtime = afile['FileModifyTime']
      afile['FileModifyAscii'] = time.strftime('%m/%d/%Y %H:%M:%S',time.gmtime(amodtime))
      acreatetime = afile['FileCreateTime']
      afile['FileCreateAscii'] = time.strftime('%m/%d/%Y %H:%M:%S',time.gmtime(acreatetime))
      if afile['AccessTime'] and afile['AccessTime'] > 0:
          afile['AccessTimeAscii'] = time.strftime('%m/%d/%Y %H:%M:%S', time.gmtime(afile['AccessTime']))
      else:
          afile['AccessTimeAscii'] = 'Never'
    if is_descending:
      descx = 1
    else:
      descx = 0
    if is_deleted:
      delx = 1
    else:
      delx = 0
    mincreatestr = time.strftime('%m/%d/%Y %H:%M:%S',time.gmtime(mincreatetime))
    maxcreatestr = time.strftime('%m/%d/%Y %H:%M:%S',time.gmtime(maxcreatetime))
    minmodstr = time.strftime('%m/%d/%Y %H:%M:%S',time.gmtime(minmodtime))
    maxmodstr = time.strftime('%m/%d/%Y %H:%M:%S',time.gmtime(maxmodtime))
    adict = {'files':files, 'result_count':result_count,
            'sfile':sfile, 'scomment':scomment, 'ssql':ssql,
            'nfile':nfile, 'ncomment':ncomment,
            'minscore':minscore, 'maxscore':maxscore,
            'minsize':minsize, 'maxsize':maxsize,
            'mincreatetime':mincreatestr,'maxcreatetime':maxcreatestr,
            'minmodtime':minmodstr,'maxmodtime':maxmodstr,
            'desc':descx, 'deleted':delx,
            'nrows':nrows,'sortsel':sortsel }
    return adict
  

@app.route('/login', methods=['GET', 'POST'])
def login():
    error = None
    if request.method == 'POST':
        if request.form['username'] != app.config['USERNAME']:
            error = 'Invalid username'
        elif request.form['password'] != app.config['PASSWORD']:
            error = 'Invalid password'
        else:
            session['logged_in'] = True
            flash('You were logged in')
            return redirect(url_for('show_index'))
    return render_template('login.html', error=error)

@app.route('/_update_row', methods=['GET', 'POST'])
def _update_row():
    myname = ''
    myvalue = ''
    status = 'OK'
    field_name = ''
    update_access = 'false' # default is false
    if request.method == 'POST':
        myname = request.form.get('name', '')
        myvalue = request.form.get('value', '')
        update_access = request.form.get('update_access', 'false')
        
        print("Got name:{0}, value:{1}, update_access:{2}".format(myname,myvalue,update_access))
        id_re = re.compile(r"in_([^_]+).*?_(\d+)")
        m = id_re.match(myname)
        if m:
          field = m.group(1)
          if field == 'Score' or field == 'Keywords':
            field_name = field
          if field == 'Score':
            myvalue = int(myvalue)
          id = int(m.group(2))
          try:
            db = get_db()
            # If update_access is true, also update AccessTime
            if update_access == 'true':
                sqlcmd = """update files set {0}=?, 
                   UpdateTime=strftime('%s', 'now'),
                   AccessTime=strftime('%s', 'now')
                   where id = ?""".format(field_name)
            else:
                sqlcmd = """update files set {0}=?, 
                   UpdateTime=strftime('%s', 'now')
                   where id = ?""".format(field_name)
                   
            print("SQL: {0}\n[{1},{2}]".format(sqlcmd,myvalue,id))
            cur = db.execute(sqlcmd, [myvalue, id])
            db.commit()
          except Exception as e:
            print("Error from SQL:", str(e))
            status = 'Error'
    return json.dumps({'data':{},
                      'status':status})

@app.route('/update_last_access', methods=['POST'])
def update_last_access():
    status = 'OK'
    error = None
    
    if request.method == 'POST':
        file_id = request.form.get('file_id')
        
        if file_id:
            try:
                db = get_db()
                sqlcmd = """update files set 
                    AccessTime=strftime('%s', 'now')
                    where id = ?"""
                cur = db.execute(sqlcmd, [file_id])
                db.commit()
            except Exception as e:
                status = 'Error'
                error = str(e)
                print("Error updating AccessTime:", error)
        else:
            status = 'Error'
            error = 'No file_id provided'
            
    return jsonify({'status': status, 'error': error})


@app.route('/_delete_file', methods=['GET', 'POST'])
def _delete_file():
    status = 'OK'
    field_name = ''
    if request.method == 'POST':
        try:
          id = request.form.get('id', '')
          db = get_db()
          sqlcmd = "select Directory, Filename from files where id = ?"
          cur = db.execute(sqlcmd, [id])
          rows = cur.fetchall()
          myfile = os.path.join(rows[0]['Directory'], rows[0]['FileName'])
          sqlcmd = "update files set Deleted = 1 where id = ?"
          print("SQL: {0}\n[{1}]".format(sqlcmd,id))
          cur = db.execute(sqlcmd, [id])
          db.commit()
          myfilew = myfile.replace("/","\\")
          proc = subprocess.Popen(["del", myfilew], stdout=subprocess.PIPE, shell=True)
          (out, err) = proc.communicate()
          print( "program output:", out, err )
          print("Deleted file {0}".format(myfilew))
        except Exception as e:
          status = 'Error'
          print(traceback.format_exc())
    return json.dumps({'data':{},
                        'status':status})


@app.route('/_update_selected', methods=['GET', 'POST'])
def _update_selected():
    status = 'OK'
    field_name = ''
    if request.method == 'POST':
        all_selected = request.form.get('all_selected', '')
        checked = all_selected.split('|')
        if len(checked) > 0:
          try:
            db = get_db()
            for row in checked:
              (id, score, keywords) = row.split('~')
              sqlcmd = """update files set Score=?,
                Keywords=?, 
                UpdateTime=strftime('%s', 'now')
                where id = ?"""
              cur = db.execute(sqlcmd, [score, keywords, id])
            db.commit()
          except Exception as e:
            print("Error from SQL:", str(e))
            status = 'Error'
    return json.dumps({'data':{},
                        'status':status})

@app.route('/_delete_selected', methods=['GET', 'POST'])
def _delete_selected():
    status = 'OK'
    if request.method == 'POST':
        all_selected = request.form.get('all_selected', '')
        checked = all_selected.split('|')
        if len(checked) > 0:
          try:
            db = get_db()
            for id in checked:
              # First get the file path before deleting from database
              sqlcmd = "select Directory, FileName from files where id = ?"
              cur = db.execute(sqlcmd, [id])
              rows = cur.fetchall()
              if rows:
                myfile = os.path.join(rows[0]['Directory'], rows[0]['FileName'])
                # Mark as deleted in database (consistent with _delete_file)
                sqlcmd = "update files set Deleted = 1 where id = ?"
                cur = db.execute(sqlcmd, [id])
                # Delete the physical file
                myfilew = myfile.replace("/","\\")
                proc = subprocess.Popen(["del", myfilew], stdout=subprocess.PIPE, shell=True)
                (out, err) = proc.communicate()
                print("Deleted file {0}".format(myfilew))
            db.commit()
          except Exception as e:
            print("Error from SQL:", str(e))
            print(traceback.format_exc())
            status = 'Error'
    return json.dumps({'data':{},
                        'status':status})



def fix_kb_name(kbname):
    kbr = re.compile(r"([\d\.]+)\s*([a-zA-Z]+)")
    m = kbr.match(kbname)
    val = 0
    if m:
        num = float(m.group(1))
        val = num
        scale = m.group(2)
        if (scale == 'kB'):
            val = 1024*num
        if (scale == 'MB'):
            val = 1024*1024*num
        if (scale == 'GB'):
            val = 1024*1024*1024*num
    else:
        print("Unknown scale in size value: {}".format(kbname))
    return val    

@app.route('/logout')
def logout():
    session.pop('logged_in', None)
    flash('You were logged out')
    return redirect(url_for('show_index'))

@app.cli.command('update_missing_times')
def update_missing_times():
    """Update missing AccessTime and UpdateTime with FileModifyTime values."""
    db = get_db()
    cur = db.cursor()
    
    # Get records with missing AccessTime or UpdateTime
    cur.execute('''
        SELECT id, FileModifyTime 
        FROM files 
        WHERE AccessTime IS NULL 
        OR AccessTime = 0 
        OR UpdateTime IS NULL 
        OR UpdateTime = 0
    ''')
    
    rows = cur.fetchall()
    count = 0
    
    for row in rows:
        id = row['id']
        mod_time = row['FileModifyTime']
        
        # Update only if values are missing
        sqlcmd = '''
            UPDATE files 
            SET AccessTime = CASE WHEN (AccessTime IS NULL OR AccessTime = 0) THEN ? ELSE AccessTime END,
                UpdateTime = CASE WHEN (UpdateTime IS NULL OR UpdateTime = 0) THEN ? ELSE UpdateTime END
            WHERE id = ?
        '''
        
        db.execute(sqlcmd, [mod_time, mod_time, id])
        count += 1
        
        if count % 100 == 0:
            print(f"Updated {count} records...")
            db.commit()
    
    db.commit()
    print(f"Completed. Updated {count} records with missing time values.")

def get_scored_images(sfile, scomment, minscore, maxscore, minsize, maxsize,
                     mincreatetime, maxcreatetime, minmodtime, maxmodtime,
                     nfile, ncomment, mime, ssql, deleted):
    """Get images that have scores within the specified range."""
    db = get_db()

    # Build the WHERE clause
    delstr = "Deleted = 0" if not deleted else "1=1"
    extra_sql = f"and ({ssql})" if ssql else ""

    sqlstring = """select * from files where (FileName like ?
                or Directory like ?)
                and Keywords like ?
                and (Score >= ?)
                and (Score <= ?)
                and (Score IS NOT NULL)
                and (Score > 0)
                and (FileSize >= ?)
                and (FileSize <= ?)
                and (FileCreateTime >= ?)
                and (FileCreateTime <= ?)
                and (FileModifyTime >= ?)
                and (FileModifyTime <= ?)
                and FileName not like ?
                and Directory not like ?
                and MIMEType like ?
                and (Keywords not like ?)
                and {0}
                {1}
                and MIMEType like 'image%'
                order by random()""".format(delstr, extra_sql)

    params = [sfile, sfile, scomment, minscore, maxscore,
              minsize, maxsize, mincreatetime, maxcreatetime,
              minmodtime, maxmodtime, nfile, nfile, mime, ncomment]

    print(f"DEBUG: get_scored_images SQL: {sqlstring}")
    print(f"DEBUG: get_scored_images params: {params}")

    cur = db.execute(sqlstring, params)
    results = [dict(row) for row in cur.fetchall()]

    print(f"DEBUG: get_scored_images returned {len(results)} results")
    if len(results) > 0:
        print(f"DEBUG: First scored image: {results[0].get('FileName', 'N/A')} (Score: {results[0].get('Score', 'N/A')})")

    return results

def get_unscored_images(sfile, scomment, minscore, maxscore, minsize, maxsize,
                       mincreatetime, maxcreatetime, minmodtime, maxmodtime,
                       nfile, ncomment, mime, ssql, deleted):
    """Get images that have no scores or scores outside the specified range."""
    db = get_db()

    # Build the WHERE clause
    delstr = "Deleted = 0" if not deleted else "1=1"
    extra_sql = f"and ({ssql})" if ssql else ""

    sqlstring = """select * from files where (FileName like ?
                or Directory like ?)
                and Keywords like ?
                and (Score IS NULL OR Score = 0 OR Score < ? OR Score > ?)
                and (FileSize >= ?)
                and (FileSize <= ?)
                and (FileCreateTime >= ?)
                and (FileCreateTime <= ?)
                and (FileModifyTime >= ?)
                and (FileModifyTime <= ?)
                and FileName not like ?
                and Directory not like ?
                and MIMEType like ?
                and (Keywords not like ?)
                and {0}
                {1}
                and MIMEType like 'image%'
                order by random()""".format(delstr, extra_sql)

    params = [sfile, sfile, scomment, minscore, maxscore,
              minsize, maxsize, mincreatetime, maxcreatetime,
              minmodtime, maxmodtime, nfile, nfile, mime, ncomment]

    print(f"DEBUG: get_unscored_images SQL: {sqlstring}")
    print(f"DEBUG: get_unscored_images params: {params}")

    cur = db.execute(sqlstring, params)
    results = [dict(row) for row in cur.fetchall()]

    print(f"DEBUG: get_unscored_images returned {len(results)} results")
    if len(results) > 0:
        print(f"DEBUG: First unscored image: {results[0].get('FileName', 'N/A')} (Score: {results[0].get('Score', 'N/A')})")

    return results

def get_scored_images_in_range(sfile, scomment, minscore, maxscore, minsize, maxsize,
                              mincreatetime, maxcreatetime, minmodtime, maxmodtime,
                              nfile, ncomment, mime, ssql, deleted):
    """Get images that have scores within a specific range."""
    db = get_db()

    # Build the WHERE clause
    delstr = "Deleted = 0" if not deleted else "1=1"
    extra_sql = f"and ({ssql})" if ssql else ""

    sqlstring = """select * from files where (FileName like ?
                or Directory like ?)
                and Keywords like ?
                and (Score >= ?)
                and (Score <= ?)
                and (Score IS NOT NULL)
                and (Score > 0)
                and (FileSize >= ?)
                and (FileSize <= ?)
                and (FileCreateTime >= ?)
                and (FileCreateTime <= ?)
                and (FileModifyTime >= ?)
                and (FileModifyTime <= ?)
                and FileName not like ?
                and Directory not like ?
                and MIMEType like ?
                and (Keywords not like ?)
                and {0}
                {1}
                and MIMEType like 'image%'
                order by random()""".format(delstr, extra_sql)

    params = [sfile, sfile, scomment, minscore, maxscore,
              minsize, maxsize, mincreatetime, maxcreatetime,
              minmodtime, maxmodtime, nfile, nfile, mime, ncomment]

    cur = db.execute(sqlstring, params)
    results = [dict(row) for row in cur.fetchall()]

    return results

def create_default_result():
    """Create a default result object for the template."""
    return {
        'sfile': '',
        'scomment': '',
        'minscore': '',
        'maxscore': '',
        'minsize': '',
        'maxsize': '',
        'mincreatetime': '',
        'maxcreatetime': '',
        'minmodtime': '',
        'maxmodtime': '',
        'nfile': '',
        'ncomment': '',
        'ssql': '',
        'deleted': 0,
        'desc': 0,
        'nrows': '100',
        'sortsel': {
            'random': '',
            'score': '',
            'predict': '',
            'created': '',
            'modified': '',
            'edited': '',
            'accessed': ''
        }
    }

@app.route('/show_four', methods=['GET', 'POST'])
def show_four():
    if not session.get('logged_in'):
        return redirect(url_for('login'))

    images = None
    scored_ids = []
    unscored_ids = []

    if request.method == 'POST':
        # Get search parameters (same as show_grid)
        sfile = request.form.get('sfile', '%')
        scomment = request.form.get('scomment', '%')
        minscore = request.form.get('minscore', '0')
        maxscore = request.form.get('maxscore', '100')
        minsize = request.form.get('minsize', '0')
        maxsize = request.form.get('maxsize', '999999999999')
        mincreatetime = request.form.get('mincreatetime', '0')
        maxcreatetime = request.form.get('maxcreatetime', '9999999999')
        minmodtime = request.form.get('minmodtime', '0')
        maxmodtime = request.form.get('maxmodtime', '9999999999')
        nfile = request.form.get('nfile', '')
        ncomment = request.form.get('ncomment', '')
        mime = request.form.get('mime', '%')
        ssql = request.form.get('ssql', '')
        deleted = request.form.get('deleted', '')

        # Convert parameters following the same logic as get_search_criteria
        if sfile == '': sfile = '%'
        if scomment == '': scomment = '%'
        if minscore == '': minscore = '0'
        if maxscore == '': maxscore = '100'
        if minsize == '': minsize = '0'
        if maxsize == '': maxsize = '999999999999'
        if mincreatetime == '': mincreatetime = '0'
        if maxcreatetime == '': maxcreatetime = '9999999999'
        if minmodtime == '': minmodtime = '0'
        if maxmodtime == '': maxmodtime = '9999999999'
        if mime == '': mime = '%'

        # Handle nfile and ncomment like the original code
        if nfile != '':
            nfile = "%{}%".format(nfile)
        if ncomment == '':
            ncomment = 'XXXXXX'  # Use a placeholder that won't match real keywords

        try:
            minscore = int(minscore)
            maxscore = int(maxscore)
            minsize = int(minsize)
            maxsize = int(maxsize)
        except ValueError:
            minscore = 0
            maxscore = 100
            minsize = 0
            maxsize = 999999999999

        # Handle date conversion using the same method as get_search_criteria
        try:
            mincreatetime = timegm(time.strptime(mincreatetime, '%m/%d/%Y %H:%M:%S'))
        except:
            try:
                mincreatetime = int(mincreatetime)
            except:
                mincreatetime = 0

        try:
            maxcreatetime = timegm(time.strptime(maxcreatetime, '%m/%d/%Y %H:%M:%S'))
        except:
            try:
                maxcreatetime = int(maxcreatetime)
            except:
                maxcreatetime = 9999999999

        try:
            minmodtime = timegm(time.strptime(minmodtime, '%m/%d/%Y %H:%M:%S'))
        except:
            try:
                minmodtime = int(minmodtime)
            except:
                minmodtime = 0

        try:
            maxmodtime = timegm(time.strptime(maxmodtime, '%m/%d/%Y %H:%M:%S'))
        except:
            try:
                maxmodtime = int(maxmodtime)
            except:
                maxmodtime = 9999999999

        # Get images with scores in range
        scored_images = get_scored_images(sfile, scomment, minscore, maxscore,
                                        minsize, maxsize, mincreatetime, maxcreatetime,
                                        minmodtime, maxmodtime, nfile, ncomment, mime, ssql, deleted)

        # Get images without scores (or outside score range)
        unscored_images = get_unscored_images(sfile, scomment, minscore, maxscore,
                                            minsize, maxsize, mincreatetime, maxcreatetime,
                                            minmodtime, maxmodtime, nfile, ncomment, mime, ssql, deleted)

        # If no unscored images exist, get images from different score ranges
        if len(unscored_images) < 2 and len(scored_images) >= 4:
            print("DEBUG: No unscored images found, using different score ranges instead")
            # Split scored images into two groups: lower half and upper half of score range
            mid_score = (minscore + maxscore) / 2
            lower_scored = get_scored_images_in_range(sfile, scomment, minscore, mid_score,
                                                    minsize, maxsize, mincreatetime, maxcreatetime,
                                                    minmodtime, maxmodtime, nfile, ncomment, mime, ssql, deleted)
            upper_scored = get_scored_images_in_range(sfile, scomment, mid_score + 1, maxscore,
                                                    minsize, maxsize, mincreatetime, maxcreatetime,
                                                    minmodtime, maxmodtime, nfile, ncomment, mime, ssql, deleted)

            print(f"DEBUG: Lower score range ({minscore}-{mid_score}): {len(lower_scored)} images")
            print(f"DEBUG: Upper score range ({mid_score + 1}-{maxscore}): {len(upper_scored)} images")

            if len(lower_scored) >= 2 and len(upper_scored) >= 2:
                scored_images = lower_scored
                unscored_images = upper_scored
                print("DEBUG: Using lower and upper score ranges as scored/unscored groups")

        # Debug output
        print(f"DEBUG: Search parameters:")
        print(f"  sfile: {sfile}")
        print(f"  scomment: {scomment}")
        print(f"  minscore: {minscore}, maxscore: {maxscore}")
        print(f"  minsize: {minsize}, maxsize: {maxsize}")
        print(f"  mincreatetime: {mincreatetime}, maxcreatetime: {maxcreatetime}")
        print(f"  minmodtime: {minmodtime}, maxmodtime: {maxmodtime}")
        print(f"  nfile: {nfile}, ncomment: {ncomment}")
        print(f"  mime: {mime}, ssql: {ssql}, deleted: {deleted}")
        print(f"DEBUG: Found {len(scored_images)} scored images, {len(unscored_images)} unscored images")

        # Additional debugging - check total counts in database
        db = get_db()
        total_files = db.execute("SELECT COUNT(*) as count FROM files").fetchone()['count']
        image_files = db.execute("SELECT COUNT(*) as count FROM files WHERE MIMEType like 'image%'").fetchone()['count']
        scored_files = db.execute("SELECT COUNT(*) as count FROM files WHERE Score IS NOT NULL AND Score > 0 AND MIMEType like 'image%'").fetchone()['count']
        unscored_files = db.execute("SELECT COUNT(*) as count FROM files WHERE (Score IS NULL OR Score = 0) AND MIMEType like 'image%'").fetchone()['count']

        print(f"DEBUG: Database stats:")
        print(f"  Total files: {total_files}")
        print(f"  Image files: {image_files}")
        print(f"  Scored image files: {scored_files}")
        print(f"  Unscored image files: {unscored_files}")

        # Test a simple query to see if we can get any images at all
        simple_query = "SELECT COUNT(*) as count FROM files WHERE MIMEType like 'image%' AND Deleted = 0"
        simple_count = db.execute(simple_query).fetchone()['count']
        print(f"DEBUG: Simple image count (no filters): {simple_count}")

        # Test with just the basic filters
        test_query = """SELECT COUNT(*) as count FROM files WHERE
                       MIMEType like 'image%' AND Deleted = 0
                       AND FileName not like ? AND Keywords not like ?"""
        test_count = db.execute(test_query, [nfile, ncomment]).fetchone()['count']
        print(f"DEBUG: Count with nfile/ncomment filters: {test_count}")
        print(f"DEBUG: nfile filter: '{nfile}', ncomment filter: '{ncomment}'")

        # Show score distribution
        score_dist = db.execute("""
            SELECT
                COUNT(CASE WHEN Score IS NULL THEN 1 END) as null_scores,
                COUNT(CASE WHEN Score = 0 THEN 1 END) as zero_scores,
                COUNT(CASE WHEN Score > 0 AND Score <= 25 THEN 1 END) as low_scores,
                COUNT(CASE WHEN Score > 25 AND Score <= 50 THEN 1 END) as mid_scores,
                COUNT(CASE WHEN Score > 50 AND Score <= 75 THEN 1 END) as high_scores,
                COUNT(CASE WHEN Score > 75 THEN 1 END) as very_high_scores
            FROM files WHERE MIMEType like 'image%' AND Deleted = 0
        """).fetchone()

        print(f"DEBUG: Score distribution:")
        print(f"  NULL scores: {score_dist['null_scores']}")
        print(f"  Zero scores: {score_dist['zero_scores']}")
        print(f"  Low scores (1-25): {score_dist['low_scores']}")
        print(f"  Mid scores (26-50): {score_dist['mid_scores']}")
        print(f"  High scores (51-75): {score_dist['high_scores']}")
        print(f"  Very high scores (76+): {score_dist['very_high_scores']}")

        if len(scored_images) >= 2 and len(unscored_images) >= 2:
            # Select 2 random images from each group
            selected_scored = random.sample(scored_images, 2)
            selected_unscored = random.sample(unscored_images, 2)

            # Combine and shuffle
            images = selected_scored + selected_unscored
            random.shuffle(images)

            scored_ids = [img['id'] for img in selected_scored]
            unscored_ids = [img['id'] for img in selected_unscored]

    # Prepare result object for search_contents.html
    if request.method == 'POST':
        result = {
            'sfile': request.form.get('sfile', ''),
            'scomment': request.form.get('scomment', ''),
            'minscore': request.form.get('minscore', ''),
            'maxscore': request.form.get('maxscore', ''),
            'minsize': request.form.get('minsize', ''),
            'maxsize': request.form.get('maxsize', ''),
            'mincreatetime': request.form.get('mincreatetime', ''),
            'maxcreatetime': request.form.get('maxcreatetime', ''),
            'minmodtime': request.form.get('minmodtime', ''),
            'maxmodtime': request.form.get('maxmodtime', ''),
            'nfile': request.form.get('nfile', ''),
            'ncomment': request.form.get('ncomment', ''),
            'ssql': request.form.get('ssql', ''),
            'deleted': 1 if request.form.get('deleted') else 0,
            'desc': 1 if request.form.get('descending') else 0,
            'nrows': request.form.get('nrows', '100'),
            'sortsel': {
                'random': 'selected' if request.form.get('sort_order') == 'random' else '',
                'score': 'selected' if request.form.get('sort_order') == 'score' else '',
                'predict': 'selected' if request.form.get('sort_order') == 'predict' else '',
                'created': 'selected' if request.form.get('sort_order') == 'created' else '',
                'modified': 'selected' if request.form.get('sort_order') == 'modified' else '',
                'edited': 'selected' if request.form.get('sort_order') == 'edited' else '',
                'accessed': 'selected' if request.form.get('sort_order') == 'accessed' else ''
            }
        }
    else:
        # For GET requests, provide default values
        result = create_default_result()

    return render_template('show_four.html', images=images, scored_ids=scored_ids,
                         unscored_ids=unscored_ids, result=result)

@app.route('/_calculate_scores', methods=['POST'])
def _calculate_scores():
    """Calculate new scores based on user ranking."""
    try:
        ranking = request.form.get('ranking', '').split(',')
        scored_images = request.form.get('scored_images', '').split(',')
        unscored_images = request.form.get('unscored_images', '').split(',')

        if len(ranking) != 4:
            return jsonify({'status': 'Error', 'error': 'Invalid ranking data'})

        # Get current scores for scored images
        db = get_db()
        image_data = {}

        for img_id in ranking:
            cur = db.execute("SELECT id, Score, Directory, FileName FROM files WHERE id = ?", [img_id])
            row = cur.fetchone()
            if row:
                image_data[img_id] = {
                    'id': row['id'],
                    'old_score': row['Score'],
                    'directory': row['Directory'],
                    'filename': row['FileName']
                }

        # Calculate new scores based on ranking rules
        results = calculate_new_scores(ranking, scored_images, unscored_images, image_data)

        return jsonify({'status': 'OK', 'results': results})

    except Exception as e:
        print(f"Error calculating scores: {str(e)}")
        print(traceback.format_exc())
        return jsonify({'status': 'Error', 'error': str(e)})

def calculate_new_scores(ranking, scored_images, unscored_images, image_data):
    """Apply the scoring rules based on user ranking."""
    results = []

    # Find positions of scored and unscored images in ranking
    scored_positions = []
    unscored_positions = []

    for i, img_id in enumerate(ranking):
        if img_id in scored_images:
            scored_positions.append((i, img_id))
        elif img_id in unscored_images:
            unscored_positions.append((i, img_id))

    # Get scores of the two scored images
    scored_scores = []
    for pos, img_id in scored_positions:
        score = image_data[img_id]['old_score']
        scored_scores.append((pos, img_id, score if score is not None else 0))

    # Sort scored images by their original scores
    scored_scores.sort(key=lambda x: x[2])  # Sort by score
    lower_score_img = scored_scores[0]  # (position, id, score)
    higher_score_img = scored_scores[1]  # (position, id, score)

    # Check if scored images are in same order as their scores
    scored_in_order = lower_score_img[0] > higher_score_img[0]  # Lower score should have higher position (worse rank)

    # Process scored images
    if scored_in_order:
        # Keep existing scores
        new_scores = {
            lower_score_img[1]: lower_score_img[2],
            higher_score_img[1]: higher_score_img[2]
        }
    else:
        # Swap scores
        new_scores = {
            lower_score_img[1]: higher_score_img[2],
            higher_score_img[1]: lower_score_img[2]
        }

    # Process unscored images
    min_scored_pos = min(scored_positions, key=lambda x: x[0])[0]
    max_scored_pos = max(scored_positions, key=lambda x: x[0])[0]
    min_score = min(lower_score_img[2], higher_score_img[2])
    max_score = max(lower_score_img[2], higher_score_img[2])

    unscored_new_scores = {}
    for pos, img_id in unscored_positions:
        if pos < min_scored_pos:
            # Ranked higher than both scored images
            unscored_new_scores[img_id] = max_score + 4
        elif pos > max_scored_pos:
            # Ranked lower than both scored images
            unscored_new_scores[img_id] = min_score - 4
        else:
            # Between the scored images
            unscored_new_scores[img_id] = (min_score + max_score) // 2

    # Adjust for multiple unscored images in same range
    if len(unscored_positions) == 2:
        pos1, id1 = unscored_positions[0]
        pos2, id2 = unscored_positions[1]

        # Check if both are in same range relative to scored images
        both_higher = pos1 < min_scored_pos and pos2 < min_scored_pos
        both_lower = pos1 > max_scored_pos and pos2 > max_scored_pos
        both_between = min_scored_pos < pos1 < max_scored_pos and min_scored_pos < pos2 < max_scored_pos

        if both_higher or both_lower or both_between:
            # Adjust scores based on relative ranking
            if pos1 < pos2:  # id1 ranked higher than id2
                unscored_new_scores[id1] += 1
                unscored_new_scores[id2] -= 1
            else:  # id2 ranked higher than id1
                unscored_new_scores[id2] += 1
                unscored_new_scores[id1] -= 1

    # Combine all new scores
    new_scores.update(unscored_new_scores)

    # Prepare results
    for i, img_id in enumerate(ranking):
        data = image_data[img_id]
        results.append({
            'id': img_id,
            'rank': i + 1,
            'old_score': data['old_score'],
            'new_score': new_scores[img_id],
            'image_url': url_for('show_image') + f"?image_file={data['directory']}/{data['filename']}"
        })

    return results

@app.route('/_save_new_scores', methods=['POST'])
def _save_new_scores():
    """Save the new scores to the database."""
    try:
        scores_json = request.form.get('scores', '')
        scores = json.loads(scores_json)

        db = get_db()
        for score_data in scores:
            img_id = score_data['id']
            new_score = int(score_data['score'])

            sqlcmd = """UPDATE files SET Score = ?,
                       UpdateTime = strftime('%s', 'now')
                       WHERE id = ?"""
            db.execute(sqlcmd, [new_score, img_id])

        db.commit()
        return jsonify({'status': 'OK'})

    except Exception as e:
        print(f"Error saving scores: {str(e)}")
        print(traceback.format_exc())
        return jsonify({'status': 'Error', 'error': str(e)})

if __name__ == "__main__":
    app.run(port=5000, host='localhost')

