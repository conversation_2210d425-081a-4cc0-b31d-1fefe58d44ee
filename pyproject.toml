[tool.poetry]
name = "file_rate"
version = "0.1.0"
description = "File_Rate is a Flask app to manage media files. It knows about images and videos and can provides pages to comment on and score the media files. A search capability allows files to be found based on various criteria."
authors = ["<PERSON> <<EMAIL>>"]

[tool.poetry.dependencies]
python = "^3.8"
Flask = "^2.0.3"
click = "^8.1.3"

[tool.poetry.dev-dependencies]

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"
