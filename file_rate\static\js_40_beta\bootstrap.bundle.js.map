{"version": 3, "file": "bootstrap.bundle.js", "sources": ["../../js/src/util.js", "../../js/src/alert.js", "../../js/src/button.js", "../../js/src/carousel.js", "../../js/src/collapse.js", "../../node_modules/popper.js/dist/esm/popper.js", "../../js/src/dropdown.js", "../../js/src/modal.js", "../../js/src/tooltip.js", "../../js/src/popover.js", "../../js/src/scrollspy.js", "../../js/src/tab.js", "../../js/src/index.js"], "sourcesContent": ["import $ from 'jquery'\n\n/**\n * --------------------------------------------------------------------------\n * <PERSON>trap (v4.0.0-beta.2): util.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst Util = (() => {\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Private TransitionEnd Helpers\n   * ------------------------------------------------------------------------\n   */\n\n  let transition = false\n\n  const MAX_UID = 1000000\n\n  const TransitionEndEvent = {\n    WebkitTransition : 'webkitTransitionEnd',\n    MozTransition    : 'transitionend',\n    OTransition      : 'oTransitionEnd otransitionend',\n    transition       : 'transitionend'\n  }\n\n  // shoutout AngusCroll (https://goo.gl/pxwQGp)\n  function toType(obj) {\n    return {}.toString.call(obj).match(/\\s([a-zA-Z]+)/)[1].toLowerCase()\n  }\n\n  function getSpecialTransitionEndEvent() {\n    return {\n      bindType: transition.end,\n      delegateType: transition.end,\n      handle(event) {\n        if ($(event.target).is(this)) {\n          return event.handleObj.handler.apply(this, arguments) // eslint-disable-line prefer-rest-params\n        }\n        return undefined // eslint-disable-line no-undefined\n      }\n    }\n  }\n\n  function transitionEndTest() {\n    if (window.QUnit) {\n      return false\n    }\n\n    const el = document.createElement('bootstrap')\n\n    for (const name in TransitionEndEvent) {\n      if (typeof el.style[name] !== 'undefined') {\n        return {\n          end: TransitionEndEvent[name]\n        }\n      }\n    }\n\n    return false\n  }\n\n  function transitionEndEmulator(duration) {\n    let called = false\n\n    $(this).one(Util.TRANSITION_END, () => {\n      called = true\n    })\n\n    setTimeout(() => {\n      if (!called) {\n        Util.triggerTransitionEnd(this)\n      }\n    }, duration)\n\n    return this\n  }\n\n  function setTransitionEndSupport() {\n    transition = transitionEndTest()\n\n    $.fn.emulateTransitionEnd = transitionEndEmulator\n\n    if (Util.supportsTransitionEnd()) {\n      $.event.special[Util.TRANSITION_END] = getSpecialTransitionEndEvent()\n    }\n  }\n\n\n  /**\n   * --------------------------------------------------------------------------\n   * Public Util Api\n   * --------------------------------------------------------------------------\n   */\n\n  const Util = {\n\n    TRANSITION_END: 'bsTransitionEnd',\n\n    getUID(prefix) {\n      do {\n        // eslint-disable-next-line no-bitwise\n        prefix += ~~(Math.random() * MAX_UID) // \"~~\" acts like a faster Math.floor() here\n      } while (document.getElementById(prefix))\n      return prefix\n    },\n\n    getSelectorFromElement(element) {\n      let selector = element.getAttribute('data-target')\n      if (!selector || selector === '#') {\n        selector = element.getAttribute('href') || ''\n      }\n\n      try {\n        const $selector = $(document).find(selector)\n        return $selector.length > 0 ? selector : null\n      } catch (error) {\n        return null\n      }\n    },\n\n    reflow(element) {\n      return element.offsetHeight\n    },\n\n    triggerTransitionEnd(element) {\n      $(element).trigger(transition.end)\n    },\n\n    supportsTransitionEnd() {\n      return Boolean(transition)\n    },\n\n    isElement(obj) {\n      return (obj[0] || obj).nodeType\n    },\n\n    typeCheckConfig(componentName, config, configTypes) {\n      for (const property in configTypes) {\n        if (Object.prototype.hasOwnProperty.call(configTypes, property)) {\n          const expectedTypes = configTypes[property]\n          const value         = config[property]\n          const valueType     = value && Util.isElement(value) ?\n                                'element' : toType(value)\n\n          if (!new RegExp(expectedTypes).test(valueType)) {\n            throw new Error(\n              `${componentName.toUpperCase()}: ` +\n              `Option \"${property}\" provided type \"${valueType}\" ` +\n              `but expected type \"${expectedTypes}\".`)\n          }\n        }\n      }\n    }\n  }\n\n  setTransitionEndSupport()\n\n  return Util\n\n})($)\n\nexport default Util\n", "import $ from 'jquery'\nimport Util from './util'\n\n\n/**\n * --------------------------------------------------------------------------\n * <PERSON><PERSON><PERSON> (v4.0.0-beta.2): alert.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst Alert = (() => {\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Constants\n   * ------------------------------------------------------------------------\n   */\n\n  const NAME                = 'alert'\n  const VERSION             = '4.0.0-beta.2'\n  const DATA_KEY            = 'bs.alert'\n  const EVENT_KEY           = `.${DATA_KEY}`\n  const DATA_API_KEY        = '.data-api'\n  const JQUERY_NO_CONFLICT  = $.fn[NAME]\n  const TRANSITION_DURATION = 150\n\n  const Selector = {\n    DISMISS : '[data-dismiss=\"alert\"]'\n  }\n\n  const Event = {\n    CLOSE          : `close${EVENT_KEY}`,\n    CLOSED         : `closed${EVENT_KEY}`,\n    CLICK_DATA_API : `click${EVENT_KEY}${DATA_API_KEY}`\n  }\n\n  const ClassName = {\n    ALERT : 'alert',\n    FADE  : 'fade',\n    SHOW  : 'show'\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Class Definition\n   * ------------------------------------------------------------------------\n   */\n\n  class Alert {\n\n    constructor(element) {\n      this._element = element\n    }\n\n\n    // getters\n\n    static get VERSION() {\n      return VERSION\n    }\n\n\n    // public\n\n    close(element) {\n      element = element || this._element\n\n      const rootElement = this._getRootElement(element)\n      const customEvent = this._triggerCloseEvent(rootElement)\n\n      if (customEvent.isDefaultPrevented()) {\n        return\n      }\n\n      this._removeElement(rootElement)\n    }\n\n    dispose() {\n      $.removeData(this._element, DATA_KEY)\n      this._element = null\n    }\n\n\n    // private\n\n    _getRootElement(element) {\n      const selector = Util.getSelectorFromElement(element)\n      let parent     = false\n\n      if (selector) {\n        parent = $(selector)[0]\n      }\n\n      if (!parent) {\n        parent = $(element).closest(`.${ClassName.ALERT}`)[0]\n      }\n\n      return parent\n    }\n\n    _triggerCloseEvent(element) {\n      const closeEvent = $.Event(Event.CLOSE)\n\n      $(element).trigger(closeEvent)\n      return closeEvent\n    }\n\n    _removeElement(element) {\n      $(element).removeClass(ClassName.SHOW)\n\n      if (!Util.supportsTransitionEnd() ||\n          !$(element).hasClass(ClassName.FADE)) {\n        this._destroyElement(element)\n        return\n      }\n\n      $(element)\n        .one(Util.TRANSITION_END, (event) => this._destroyElement(element, event))\n        .emulateTransitionEnd(TRANSITION_DURATION)\n    }\n\n    _destroyElement(element) {\n      $(element)\n        .detach()\n        .trigger(Event.CLOSED)\n        .remove()\n    }\n\n\n    // static\n\n    static _jQueryInterface(config) {\n      return this.each(function () {\n        const $element = $(this)\n        let data       = $element.data(DATA_KEY)\n\n        if (!data) {\n          data = new Alert(this)\n          $element.data(DATA_KEY, data)\n        }\n\n        if (config === 'close') {\n          data[config](this)\n        }\n      })\n    }\n\n    static _handleDismiss(alertInstance) {\n      return function (event) {\n        if (event) {\n          event.preventDefault()\n        }\n\n        alertInstance.close(this)\n      }\n    }\n\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Data Api implementation\n   * ------------------------------------------------------------------------\n   */\n\n  $(document).on(\n    Event.CLICK_DATA_API,\n    Selector.DISMISS,\n    Alert._handleDismiss(new Alert())\n  )\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * jQuery\n   * ------------------------------------------------------------------------\n   */\n\n  $.fn[NAME]             = Alert._jQueryInterface\n  $.fn[NAME].Constructor = Alert\n  $.fn[NAME].noConflict  = function () {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Alert._jQueryInterface\n  }\n\n  return Alert\n\n})($)\n\nexport default Alert\n", "import $ from 'jquery'\n/**\n * --------------------------------------------------------------------------\n * <PERSON>trap (v4.0.0-beta.2): button.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst Button = (() => {\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Constants\n   * ------------------------------------------------------------------------\n   */\n\n  const NAME                = 'button'\n  const VERSION             = '4.0.0-beta.2'\n  const DATA_KEY            = 'bs.button'\n  const EVENT_KEY           = `.${DATA_KEY}`\n  const DATA_API_KEY        = '.data-api'\n  const JQUERY_NO_CONFLICT  = $.fn[NAME]\n\n  const ClassName = {\n    ACTIVE : 'active',\n    BUTTON : 'btn',\n    FOCUS  : 'focus'\n  }\n\n  const Selector = {\n    DATA_TOGGLE_CARROT : '[data-toggle^=\"button\"]',\n    DATA_TOGGLE        : '[data-toggle=\"buttons\"]',\n    INPUT              : 'input',\n    ACTIVE             : '.active',\n    BUTTON             : '.btn'\n  }\n\n  const Event = {\n    CLICK_DATA_API      : `click${EVENT_KEY}${DATA_API_KEY}`,\n    FOCUS_BLUR_DATA_API : `focus${EVENT_KEY}${DATA_API_KEY} `\n                        + `blur${EVENT_KEY}${DATA_API_KEY}`\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Class Definition\n   * ------------------------------------------------------------------------\n   */\n\n  class Button {\n\n    constructor(element) {\n      this._element = element\n    }\n\n\n    // getters\n\n    static get VERSION() {\n      return VERSION\n    }\n\n\n    // public\n\n    toggle() {\n      let triggerChangeEvent = true\n      let addAriaPressed = true\n      const rootElement      = $(this._element).closest(\n        Selector.DATA_TOGGLE\n      )[0]\n\n      if (rootElement) {\n        const input = $(this._element).find(Selector.INPUT)[0]\n\n        if (input) {\n          if (input.type === 'radio') {\n            if (input.checked &&\n              $(this._element).hasClass(ClassName.ACTIVE)) {\n              triggerChangeEvent = false\n\n            } else {\n              const activeElement = $(rootElement).find(Selector.ACTIVE)[0]\n\n              if (activeElement) {\n                $(activeElement).removeClass(ClassName.ACTIVE)\n              }\n            }\n          }\n\n          if (triggerChangeEvent) {\n            if (input.hasAttribute('disabled') ||\n              rootElement.hasAttribute('disabled') ||\n              input.classList.contains('disabled') ||\n              rootElement.classList.contains('disabled')) {\n              return\n            }\n            input.checked = !$(this._element).hasClass(ClassName.ACTIVE)\n            $(input).trigger('change')\n          }\n\n          input.focus()\n          addAriaPressed = false\n        }\n\n      }\n\n      if (addAriaPressed) {\n        this._element.setAttribute('aria-pressed',\n          !$(this._element).hasClass(ClassName.ACTIVE))\n      }\n\n      if (triggerChangeEvent) {\n        $(this._element).toggleClass(ClassName.ACTIVE)\n      }\n    }\n\n    dispose() {\n      $.removeData(this._element, DATA_KEY)\n      this._element = null\n    }\n\n\n    // static\n\n    static _jQueryInterface(config) {\n      return this.each(function () {\n        let data = $(this).data(DATA_KEY)\n\n        if (!data) {\n          data = new Button(this)\n          $(this).data(DATA_KEY, data)\n        }\n\n        if (config === 'toggle') {\n          data[config]()\n        }\n      })\n    }\n\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Data Api implementation\n   * ------------------------------------------------------------------------\n   */\n\n  $(document)\n    .on(Event.CLICK_DATA_API, Selector.DATA_TOGGLE_CARROT, (event) => {\n      event.preventDefault()\n\n      let button = event.target\n\n      if (!$(button).hasClass(ClassName.BUTTON)) {\n        button = $(button).closest(Selector.BUTTON)\n      }\n\n      Button._jQueryInterface.call($(button), 'toggle')\n    })\n    .on(Event.FOCUS_BLUR_DATA_API, Selector.DATA_TOGGLE_CARROT, (event) => {\n      const button = $(event.target).closest(Selector.BUTTON)[0]\n      $(button).toggleClass(ClassName.FOCUS, /^focus(in)?$/.test(event.type))\n    })\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * jQuery\n   * ------------------------------------------------------------------------\n   */\n\n  $.fn[NAME]             = Button._jQueryInterface\n  $.fn[NAME].Constructor = Button\n  $.fn[NAME].noConflict  = function () {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Button._jQueryInterface\n  }\n\n  return Button\n\n})($)\n\nexport default Button\n", "import $ from 'jquery'\nimport Util from './util'\n\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.0.0-beta.2): carousel.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst Carousel = (() => {\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Constants\n   * ------------------------------------------------------------------------\n   */\n\n  const NAME                   = 'carousel'\n  const VERSION                = '4.0.0-beta.2'\n  const DATA_KEY               = 'bs.carousel'\n  const EVENT_KEY              = `.${DATA_KEY}`\n  const DATA_API_KEY           = '.data-api'\n  const JQUERY_NO_CONFLICT     = $.fn[NAME]\n  const TRANSITION_DURATION    = 600\n  const ARROW_LEFT_KEYCODE     = 37 // KeyboardEvent.which value for left arrow key\n  const ARROW_RIGHT_KEYCODE    = 39 // KeyboardEvent.which value for right arrow key\n  const TOUCHEVENT_COMPAT_WAIT = 500 // Time for mouse compat events to fire after touch\n\n  const Default = {\n    interval : 5000,\n    keyboard : true,\n    slide    : false,\n    pause    : 'hover',\n    wrap     : true\n  }\n\n  const DefaultType = {\n    interval : '(number|boolean)',\n    keyboard : 'boolean',\n    slide    : '(boolean|string)',\n    pause    : '(string|boolean)',\n    wrap     : 'boolean'\n  }\n\n  const Direction = {\n    NEXT     : 'next',\n    PREV     : 'prev',\n    LEFT     : 'left',\n    RIGHT    : 'right'\n  }\n\n  const Event = {\n    SLIDE          : `slide${EVENT_KEY}`,\n    SLID           : `slid${EVENT_KEY}`,\n    KEYDOWN        : `keydown${EVENT_KEY}`,\n    MOUSEENTER     : `mouseenter${EVENT_KEY}`,\n    MOUSELEAVE     : `mouseleave${EVENT_KEY}`,\n    TOUCHEND       : `touchend${EVENT_KEY}`,\n    LOAD_DATA_API  : `load${EVENT_KEY}${DATA_API_KEY}`,\n    CLICK_DATA_API : `click${EVENT_KEY}${DATA_API_KEY}`\n  }\n\n  const ClassName = {\n    CAROUSEL : 'carousel',\n    ACTIVE   : 'active',\n    SLIDE    : 'slide',\n    RIGHT    : 'carousel-item-right',\n    LEFT     : 'carousel-item-left',\n    NEXT     : 'carousel-item-next',\n    PREV     : 'carousel-item-prev',\n    ITEM     : 'carousel-item'\n  }\n\n  const Selector = {\n    ACTIVE      : '.active',\n    ACTIVE_ITEM : '.active.carousel-item',\n    ITEM        : '.carousel-item',\n    NEXT_PREV   : '.carousel-item-next, .carousel-item-prev',\n    INDICATORS  : '.carousel-indicators',\n    DATA_SLIDE  : '[data-slide], [data-slide-to]',\n    DATA_RIDE   : '[data-ride=\"carousel\"]'\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Class Definition\n   * ------------------------------------------------------------------------\n   */\n\n  class Carousel {\n\n    constructor(element, config) {\n      this._items             = null\n      this._interval          = null\n      this._activeElement     = null\n\n      this._isPaused          = false\n      this._isSliding         = false\n\n      this.touchTimeout       = null\n\n      this._config            = this._getConfig(config)\n      this._element           = $(element)[0]\n      this._indicatorsElement = $(this._element).find(Selector.INDICATORS)[0]\n\n      this._addEventListeners()\n    }\n\n\n    // getters\n\n    static get VERSION() {\n      return VERSION\n    }\n\n    static get Default() {\n      return Default\n    }\n\n\n    // public\n\n    next() {\n      if (!this._isSliding) {\n        this._slide(Direction.NEXT)\n      }\n    }\n\n    nextWhenVisible() {\n      // Don't call next when the page isn't visible\n      // or the carousel or its parent isn't visible\n      if (!document.hidden &&\n        ($(this._element).is(':visible') && $(this._element).css('visibility') !== 'hidden')) {\n        this.next()\n      }\n    }\n\n    prev() {\n      if (!this._isSliding) {\n        this._slide(Direction.PREV)\n      }\n    }\n\n    pause(event) {\n      if (!event) {\n        this._isPaused = true\n      }\n\n      if ($(this._element).find(Selector.NEXT_PREV)[0] &&\n        Util.supportsTransitionEnd()) {\n        Util.triggerTransitionEnd(this._element)\n        this.cycle(true)\n      }\n\n      clearInterval(this._interval)\n      this._interval = null\n    }\n\n    cycle(event) {\n      if (!event) {\n        this._isPaused = false\n      }\n\n      if (this._interval) {\n        clearInterval(this._interval)\n        this._interval = null\n      }\n\n      if (this._config.interval && !this._isPaused) {\n        this._interval = setInterval(\n          (document.visibilityState ? this.nextWhenVisible : this.next).bind(this),\n          this._config.interval\n        )\n      }\n    }\n\n    to(index) {\n      this._activeElement = $(this._element).find(Selector.ACTIVE_ITEM)[0]\n\n      const activeIndex = this._getItemIndex(this._activeElement)\n\n      if (index > this._items.length - 1 || index < 0) {\n        return\n      }\n\n      if (this._isSliding) {\n        $(this._element).one(Event.SLID, () => this.to(index))\n        return\n      }\n\n      if (activeIndex === index) {\n        this.pause()\n        this.cycle()\n        return\n      }\n\n      const direction = index > activeIndex ?\n        Direction.NEXT :\n        Direction.PREV\n\n      this._slide(direction, this._items[index])\n    }\n\n    dispose() {\n      $(this._element).off(EVENT_KEY)\n      $.removeData(this._element, DATA_KEY)\n\n      this._items             = null\n      this._config            = null\n      this._element           = null\n      this._interval          = null\n      this._isPaused          = null\n      this._isSliding         = null\n      this._activeElement     = null\n      this._indicatorsElement = null\n    }\n\n\n    // private\n\n    _getConfig(config) {\n      config = $.extend({}, Default, config)\n      Util.typeCheckConfig(NAME, config, DefaultType)\n      return config\n    }\n\n    _addEventListeners() {\n      if (this._config.keyboard) {\n        $(this._element)\n          .on(Event.KEYDOWN, (event) => this._keydown(event))\n      }\n\n      if (this._config.pause === 'hover') {\n        $(this._element)\n          .on(Event.MOUSEENTER, (event) => this.pause(event))\n          .on(Event.MOUSELEAVE, (event) => this.cycle(event))\n        if ('ontouchstart' in document.documentElement) {\n          // if it's a touch-enabled device, mouseenter/leave are fired as\n          // part of the mouse compatibility events on first tap - the carousel\n          // would stop cycling until user tapped out of it;\n          // here, we listen for touchend, explicitly pause the carousel\n          // (as if it's the second time we tap on it, mouseenter compat event\n          // is NOT fired) and after a timeout (to allow for mouse compatibility\n          // events to fire) we explicitly restart cycling\n          $(this._element).on(Event.TOUCHEND, () => {\n            this.pause()\n            if (this.touchTimeout) {\n              clearTimeout(this.touchTimeout)\n            }\n            this.touchTimeout = setTimeout((event) => this.cycle(event), TOUCHEVENT_COMPAT_WAIT + this._config.interval)\n          })\n        }\n      }\n    }\n\n    _keydown(event) {\n      if (/input|textarea/i.test(event.target.tagName)) {\n        return\n      }\n\n      switch (event.which) {\n        case ARROW_LEFT_KEYCODE:\n          event.preventDefault()\n          this.prev()\n          break\n        case ARROW_RIGHT_KEYCODE:\n          event.preventDefault()\n          this.next()\n          break\n        default:\n          return\n      }\n    }\n\n    _getItemIndex(element) {\n      this._items = $.makeArray($(element).parent().find(Selector.ITEM))\n      return this._items.indexOf(element)\n    }\n\n    _getItemByDirection(direction, activeElement) {\n      const isNextDirection = direction === Direction.NEXT\n      const isPrevDirection = direction === Direction.PREV\n      const activeIndex     = this._getItemIndex(activeElement)\n      const lastItemIndex   = this._items.length - 1\n      const isGoingToWrap   = isPrevDirection && activeIndex === 0 ||\n                              isNextDirection && activeIndex === lastItemIndex\n\n      if (isGoingToWrap && !this._config.wrap) {\n        return activeElement\n      }\n\n      const delta     = direction === Direction.PREV ? -1 : 1\n      const itemIndex = (activeIndex + delta) % this._items.length\n\n      return itemIndex === -1 ?\n        this._items[this._items.length - 1] : this._items[itemIndex]\n    }\n\n\n    _triggerSlideEvent(relatedTarget, eventDirectionName) {\n      const targetIndex = this._getItemIndex(relatedTarget)\n      const fromIndex = this._getItemIndex($(this._element).find(Selector.ACTIVE_ITEM)[0])\n      const slideEvent = $.Event(Event.SLIDE, {\n        relatedTarget,\n        direction: eventDirectionName,\n        from: fromIndex,\n        to: targetIndex\n      })\n\n      $(this._element).trigger(slideEvent)\n\n      return slideEvent\n    }\n\n    _setActiveIndicatorElement(element) {\n      if (this._indicatorsElement) {\n        $(this._indicatorsElement)\n          .find(Selector.ACTIVE)\n          .removeClass(ClassName.ACTIVE)\n\n        const nextIndicator = this._indicatorsElement.children[\n          this._getItemIndex(element)\n        ]\n\n        if (nextIndicator) {\n          $(nextIndicator).addClass(ClassName.ACTIVE)\n        }\n      }\n    }\n\n    _slide(direction, element) {\n      const activeElement = $(this._element).find(Selector.ACTIVE_ITEM)[0]\n      const activeElementIndex = this._getItemIndex(activeElement)\n      const nextElement   = element || activeElement &&\n        this._getItemByDirection(direction, activeElement)\n      const nextElementIndex = this._getItemIndex(nextElement)\n      const isCycling = Boolean(this._interval)\n\n      let directionalClassName\n      let orderClassName\n      let eventDirectionName\n\n      if (direction === Direction.NEXT) {\n        directionalClassName = ClassName.LEFT\n        orderClassName = ClassName.NEXT\n        eventDirectionName = Direction.LEFT\n      } else {\n        directionalClassName = ClassName.RIGHT\n        orderClassName = ClassName.PREV\n        eventDirectionName = Direction.RIGHT\n      }\n\n      if (nextElement && $(nextElement).hasClass(ClassName.ACTIVE)) {\n        this._isSliding = false\n        return\n      }\n\n      const slideEvent = this._triggerSlideEvent(nextElement, eventDirectionName)\n      if (slideEvent.isDefaultPrevented()) {\n        return\n      }\n\n      if (!activeElement || !nextElement) {\n        // some weirdness is happening, so we bail\n        return\n      }\n\n      this._isSliding = true\n\n      if (isCycling) {\n        this.pause()\n      }\n\n      this._setActiveIndicatorElement(nextElement)\n\n      const slidEvent = $.Event(Event.SLID, {\n        relatedTarget: nextElement,\n        direction: eventDirectionName,\n        from: activeElementIndex,\n        to: nextElementIndex\n      })\n\n      if (Util.supportsTransitionEnd() &&\n        $(this._element).hasClass(ClassName.SLIDE)) {\n\n        $(nextElement).addClass(orderClassName)\n\n        Util.reflow(nextElement)\n\n        $(activeElement).addClass(directionalClassName)\n        $(nextElement).addClass(directionalClassName)\n\n        $(activeElement)\n          .one(Util.TRANSITION_END, () => {\n            $(nextElement)\n              .removeClass(`${directionalClassName} ${orderClassName}`)\n              .addClass(ClassName.ACTIVE)\n\n            $(activeElement).removeClass(`${ClassName.ACTIVE} ${orderClassName} ${directionalClassName}`)\n\n            this._isSliding = false\n\n            setTimeout(() => $(this._element).trigger(slidEvent), 0)\n\n          })\n          .emulateTransitionEnd(TRANSITION_DURATION)\n\n      } else {\n        $(activeElement).removeClass(ClassName.ACTIVE)\n        $(nextElement).addClass(ClassName.ACTIVE)\n\n        this._isSliding = false\n        $(this._element).trigger(slidEvent)\n      }\n\n      if (isCycling) {\n        this.cycle()\n      }\n    }\n\n\n    // static\n\n    static _jQueryInterface(config) {\n      return this.each(function () {\n        let data      = $(this).data(DATA_KEY)\n        const _config = $.extend({}, Default, $(this).data())\n\n        if (typeof config === 'object') {\n          $.extend(_config, config)\n        }\n\n        const action = typeof config === 'string' ? config : _config.slide\n\n        if (!data) {\n          data = new Carousel(this, _config)\n          $(this).data(DATA_KEY, data)\n        }\n\n        if (typeof config === 'number') {\n          data.to(config)\n        } else if (typeof action === 'string') {\n          if (typeof data[action] === 'undefined') {\n            throw new Error(`No method named \"${action}\"`)\n          }\n          data[action]()\n        } else if (_config.interval) {\n          data.pause()\n          data.cycle()\n        }\n      })\n    }\n\n    static _dataApiClickHandler(event) {\n      const selector = Util.getSelectorFromElement(this)\n\n      if (!selector) {\n        return\n      }\n\n      const target = $(selector)[0]\n\n      if (!target || !$(target).hasClass(ClassName.CAROUSEL)) {\n        return\n      }\n\n      const config     = $.extend({}, $(target).data(), $(this).data())\n      const slideIndex = this.getAttribute('data-slide-to')\n\n      if (slideIndex) {\n        config.interval = false\n      }\n\n      Carousel._jQueryInterface.call($(target), config)\n\n      if (slideIndex) {\n        $(target).data(DATA_KEY).to(slideIndex)\n      }\n\n      event.preventDefault()\n    }\n\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Data Api implementation\n   * ------------------------------------------------------------------------\n   */\n\n  $(document)\n    .on(Event.CLICK_DATA_API, Selector.DATA_SLIDE, Carousel._dataApiClickHandler)\n\n  $(window).on(Event.LOAD_DATA_API, () => {\n    $(Selector.DATA_RIDE).each(function () {\n      const $carousel = $(this)\n      Carousel._jQueryInterface.call($carousel, $carousel.data())\n    })\n  })\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * jQuery\n   * ------------------------------------------------------------------------\n   */\n\n  $.fn[NAME]             = Carousel._jQueryInterface\n  $.fn[NAME].Constructor = Carousel\n  $.fn[NAME].noConflict  = function () {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Carousel._jQueryInterface\n  }\n\n  return Carousel\n\n})($)\n\nexport default Carousel\n", "import $ from 'jquery'\nimport Util from './util'\n\n\n/**\n * --------------------------------------------------------------------------\n * <PERSON><PERSON><PERSON> (v4.0.0-beta.2): collapse.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst Collapse = (() => {\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Constants\n   * ------------------------------------------------------------------------\n   */\n\n  const NAME                = 'collapse'\n  const VERSION             = '4.0.0-beta.2'\n  const DATA_KEY            = 'bs.collapse'\n  const EVENT_KEY           = `.${DATA_KEY}`\n  const DATA_API_KEY        = '.data-api'\n  const JQUERY_NO_CONFLICT  = $.fn[NAME]\n  const TRANSITION_DURATION = 600\n\n  const Default = {\n    toggle : true,\n    parent : ''\n  }\n\n  const DefaultType = {\n    toggle : 'boolean',\n    parent : '(string|element)'\n  }\n\n  const Event = {\n    SHOW           : `show${EVENT_KEY}`,\n    SHOWN          : `shown${EVENT_KEY}`,\n    HIDE           : `hide${EVENT_KEY}`,\n    HIDDEN         : `hidden${EVENT_KEY}`,\n    CLICK_DATA_API : `click${EVENT_KEY}${DATA_API_KEY}`\n  }\n\n  const ClassName = {\n    SHOW       : 'show',\n    COLLAPSE   : 'collapse',\n    COLLAPSING : 'collapsing',\n    COLLAPSED  : 'collapsed'\n  }\n\n  const Dimension = {\n    WIDTH  : 'width',\n    HEIGHT : 'height'\n  }\n\n  const Selector = {\n    ACTIVES     : '.show, .collapsing',\n    DATA_TOGGLE : '[data-toggle=\"collapse\"]'\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Class Definition\n   * ------------------------------------------------------------------------\n   */\n\n  class Collapse {\n\n    constructor(element, config) {\n      this._isTransitioning = false\n      this._element         = element\n      this._config          = this._getConfig(config)\n      this._triggerArray    = $.makeArray($(\n        `[data-toggle=\"collapse\"][href=\"#${element.id}\"],` +\n        `[data-toggle=\"collapse\"][data-target=\"#${element.id}\"]`\n      ))\n      const tabToggles = $(Selector.DATA_TOGGLE)\n      for (let i = 0; i < tabToggles.length; i++) {\n        const elem = tabToggles[i]\n        const selector = Util.getSelectorFromElement(elem)\n        if (selector !== null && $(selector).filter(element).length > 0) {\n          this._triggerArray.push(elem)\n        }\n      }\n\n      this._parent = this._config.parent ? this._getParent() : null\n\n      if (!this._config.parent) {\n        this._addAriaAndCollapsedClass(this._element, this._triggerArray)\n      }\n\n      if (this._config.toggle) {\n        this.toggle()\n      }\n    }\n\n\n    // getters\n\n    static get VERSION() {\n      return VERSION\n    }\n\n    static get Default() {\n      return Default\n    }\n\n\n    // public\n\n    toggle() {\n      if ($(this._element).hasClass(ClassName.SHOW)) {\n        this.hide()\n      } else {\n        this.show()\n      }\n    }\n\n    show() {\n      if (this._isTransitioning ||\n        $(this._element).hasClass(ClassName.SHOW)) {\n        return\n      }\n\n      let actives\n      let activesData\n\n      if (this._parent) {\n        actives = $.makeArray($(this._parent).children().children(Selector.ACTIVES))\n        if (!actives.length) {\n          actives = null\n        }\n      }\n\n      if (actives) {\n        activesData = $(actives).data(DATA_KEY)\n        if (activesData && activesData._isTransitioning) {\n          return\n        }\n      }\n\n      const startEvent = $.Event(Event.SHOW)\n      $(this._element).trigger(startEvent)\n      if (startEvent.isDefaultPrevented()) {\n        return\n      }\n\n      if (actives) {\n        Collapse._jQueryInterface.call($(actives), 'hide')\n        if (!activesData) {\n          $(actives).data(DATA_KEY, null)\n        }\n      }\n\n      const dimension = this._getDimension()\n\n      $(this._element)\n        .removeClass(ClassName.COLLAPSE)\n        .addClass(ClassName.COLLAPSING)\n\n      this._element.style[dimension] = 0\n\n      if (this._triggerArray.length) {\n        $(this._triggerArray)\n          .removeClass(ClassName.COLLAPSED)\n          .attr('aria-expanded', true)\n      }\n\n      this.setTransitioning(true)\n\n      const complete = () => {\n        $(this._element)\n          .removeClass(ClassName.COLLAPSING)\n          .addClass(ClassName.COLLAPSE)\n          .addClass(ClassName.SHOW)\n\n        this._element.style[dimension] = ''\n\n        this.setTransitioning(false)\n\n        $(this._element).trigger(Event.SHOWN)\n      }\n\n      if (!Util.supportsTransitionEnd()) {\n        complete()\n        return\n      }\n\n      const capitalizedDimension = dimension[0].toUpperCase() + dimension.slice(1)\n      const scrollSize           = `scroll${capitalizedDimension}`\n\n      $(this._element)\n        .one(Util.TRANSITION_END, complete)\n        .emulateTransitionEnd(TRANSITION_DURATION)\n\n      this._element.style[dimension] = `${this._element[scrollSize]}px`\n    }\n\n    hide() {\n      if (this._isTransitioning ||\n        !$(this._element).hasClass(ClassName.SHOW)) {\n        return\n      }\n\n      const startEvent = $.Event(Event.HIDE)\n      $(this._element).trigger(startEvent)\n      if (startEvent.isDefaultPrevented()) {\n        return\n      }\n\n      const dimension       = this._getDimension()\n\n      this._element.style[dimension] = `${this._element.getBoundingClientRect()[dimension]}px`\n\n      Util.reflow(this._element)\n\n      $(this._element)\n        .addClass(ClassName.COLLAPSING)\n        .removeClass(ClassName.COLLAPSE)\n        .removeClass(ClassName.SHOW)\n\n      if (this._triggerArray.length) {\n        for (let i = 0; i < this._triggerArray.length; i++) {\n          const trigger = this._triggerArray[i]\n          const selector = Util.getSelectorFromElement(trigger)\n          if (selector !== null) {\n            const $elem = $(selector)\n            if (!$elem.hasClass(ClassName.SHOW)) {\n              $(trigger).addClass(ClassName.COLLAPSED)\n                   .attr('aria-expanded', false)\n            }\n          }\n        }\n      }\n\n      this.setTransitioning(true)\n\n      const complete = () => {\n        this.setTransitioning(false)\n        $(this._element)\n          .removeClass(ClassName.COLLAPSING)\n          .addClass(ClassName.COLLAPSE)\n          .trigger(Event.HIDDEN)\n      }\n\n      this._element.style[dimension] = ''\n\n      if (!Util.supportsTransitionEnd()) {\n        complete()\n        return\n      }\n\n      $(this._element)\n        .one(Util.TRANSITION_END, complete)\n        .emulateTransitionEnd(TRANSITION_DURATION)\n    }\n\n    setTransitioning(isTransitioning) {\n      this._isTransitioning = isTransitioning\n    }\n\n    dispose() {\n      $.removeData(this._element, DATA_KEY)\n\n      this._config          = null\n      this._parent          = null\n      this._element         = null\n      this._triggerArray    = null\n      this._isTransitioning = null\n    }\n\n\n    // private\n\n    _getConfig(config) {\n      config = $.extend({}, Default, config)\n      config.toggle = Boolean(config.toggle) // coerce string values\n      Util.typeCheckConfig(NAME, config, DefaultType)\n      return config\n    }\n\n    _getDimension() {\n      const hasWidth = $(this._element).hasClass(Dimension.WIDTH)\n      return hasWidth ? Dimension.WIDTH : Dimension.HEIGHT\n    }\n\n    _getParent() {\n      let parent = null\n      if (Util.isElement(this._config.parent)) {\n        parent = this._config.parent\n\n        // it's a jQuery object\n        if (typeof this._config.parent.jquery !== 'undefined') {\n          parent = this._config.parent[0]\n        }\n      } else {\n        parent = $(this._config.parent)[0]\n      }\n\n      const selector =\n        `[data-toggle=\"collapse\"][data-parent=\"${this._config.parent}\"]`\n\n      $(parent).find(selector).each((i, element) => {\n        this._addAriaAndCollapsedClass(\n          Collapse._getTargetFromElement(element),\n          [element]\n        )\n      })\n\n      return parent\n    }\n\n    _addAriaAndCollapsedClass(element, triggerArray) {\n      if (element) {\n        const isOpen = $(element).hasClass(ClassName.SHOW)\n\n        if (triggerArray.length) {\n          $(triggerArray)\n            .toggleClass(ClassName.COLLAPSED, !isOpen)\n            .attr('aria-expanded', isOpen)\n        }\n      }\n    }\n\n\n    // static\n\n    static _getTargetFromElement(element) {\n      const selector = Util.getSelectorFromElement(element)\n      return selector ? $(selector)[0] : null\n    }\n\n    static _jQueryInterface(config) {\n      return this.each(function () {\n        const $this   = $(this)\n        let data      = $this.data(DATA_KEY)\n        const _config = $.extend(\n          {},\n          Default,\n          $this.data(),\n          typeof config === 'object' && config\n        )\n\n        if (!data && _config.toggle && /show|hide/.test(config)) {\n          _config.toggle = false\n        }\n\n        if (!data) {\n          data = new Collapse(this, _config)\n          $this.data(DATA_KEY, data)\n        }\n\n        if (typeof config === 'string') {\n          if (typeof data[config] === 'undefined') {\n            throw new Error(`No method named \"${config}\"`)\n          }\n          data[config]()\n        }\n      })\n    }\n\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Data Api implementation\n   * ------------------------------------------------------------------------\n   */\n\n  $(document).on(Event.CLICK_DATA_API, Selector.DATA_TOGGLE, function (event) {\n    // preventDefault only for <a> elements (which change the URL) not inside the collapsible element\n    if (event.currentTarget.tagName === 'A') {\n      event.preventDefault()\n    }\n\n    const $trigger = $(this)\n    const selector = Util.getSelectorFromElement(this)\n    $(selector).each(function () {\n      const $target = $(this)\n      const data    = $target.data(DATA_KEY)\n      const config  = data ? 'toggle' : $trigger.data()\n      Collapse._jQueryInterface.call($target, config)\n    })\n  })\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * jQuery\n   * ------------------------------------------------------------------------\n   */\n\n  $.fn[NAME]             = Collapse._jQueryInterface\n  $.fn[NAME].Constructor = Collapse\n  $.fn[NAME].noConflict  = function () {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Collapse._jQueryInterface\n  }\n\n  return Collapse\n\n})($)\n\nexport default Collapse\n", "/**!\n * @fileOverview Kickass library to create and place poppers near their reference elements.\n * @version 1.12.5\n * @license\n * Copyright (c) 2016 <PERSON> and contributors\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all\n * copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n * SOFTWARE.\n */\nvar nativeHints = ['native code', '[object MutationObserverConstructor]'];\n\n/**\n * Determine if a function is implemented natively (as opposed to a polyfill).\n * @method\n * @memberof Popper.Utils\n * @argument {Function | undefined} fn the function to check\n * @returns {Boolean}\n */\nvar isNative = (function (fn) {\n  return nativeHints.some(function (hint) {\n    return (fn || '').toString().indexOf(hint) > -1;\n  });\n});\n\nvar isBrowser = typeof window !== 'undefined';\nvar longerTimeoutBrowsers = ['Edge', 'Trident', 'Firefox'];\nvar timeoutDuration = 0;\nfor (var i = 0; i < longerTimeoutBrowsers.length; i += 1) {\n  if (isBrowser && navigator.userAgent.indexOf(longerTimeoutBrowsers[i]) >= 0) {\n    timeoutDuration = 1;\n    break;\n  }\n}\n\nfunction microtaskDebounce(fn) {\n  var scheduled = false;\n  var i = 0;\n  var elem = document.createElement('span');\n\n  // MutationObserver provides a mechanism for scheduling microtasks, which\n  // are scheduled *before* the next task. This gives us a way to debounce\n  // a function but ensure it's called *before* the next paint.\n  var observer = new MutationObserver(function () {\n    fn();\n    scheduled = false;\n  });\n\n  observer.observe(elem, { attributes: true });\n\n  return function () {\n    if (!scheduled) {\n      scheduled = true;\n      elem.setAttribute('x-index', i);\n      i = i + 1; // don't use compund (+=) because it doesn't get optimized in V8\n    }\n  };\n}\n\nfunction taskDebounce(fn) {\n  var scheduled = false;\n  return function () {\n    if (!scheduled) {\n      scheduled = true;\n      setTimeout(function () {\n        scheduled = false;\n        fn();\n      }, timeoutDuration);\n    }\n  };\n}\n\n// It's common for MutationObserver polyfills to be seen in the wild, however\n// these rely on Mutation Events which only occur when an element is connected\n// to the DOM. The algorithm used in this module does not use a connected element,\n// and so we must ensure that a *native* MutationObserver is available.\nvar supportsNativeMutationObserver = isBrowser && isNative(window.MutationObserver);\n\n/**\n* Create a debounced version of a method, that's asynchronously deferred\n* but called in the minimum time possible.\n*\n* @method\n* @memberof Popper.Utils\n* @argument {Function} fn\n* @returns {Function}\n*/\nvar debounce = supportsNativeMutationObserver ? microtaskDebounce : taskDebounce;\n\n/**\n * Check if the given variable is a function\n * @method\n * @memberof Popper.Utils\n * @argument {Any} functionToCheck - variable to check\n * @returns {Boolean} answer to: is a function?\n */\nfunction isFunction(functionToCheck) {\n  var getType = {};\n  return functionToCheck && getType.toString.call(functionToCheck) === '[object Function]';\n}\n\n/**\n * Get CSS computed property of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Eement} element\n * @argument {String} property\n */\nfunction getStyleComputedProperty(element, property) {\n  if (element.nodeType !== 1) {\n    return [];\n  }\n  // NOTE: 1 DOM access here\n  var css = window.getComputedStyle(element, null);\n  return property ? css[property] : css;\n}\n\n/**\n * Returns the parentNode or the host of the element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} parent\n */\nfunction getParentNode(element) {\n  if (element.nodeName === 'HTML') {\n    return element;\n  }\n  return element.parentNode || element.host;\n}\n\n/**\n * Returns the scrolling parent of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} scroll parent\n */\nfunction getScrollParent(element) {\n  // Return body, `getScroll` will take care to get the correct `scrollTop` from it\n  if (!element || ['HTML', 'BODY', '#document'].indexOf(element.nodeName) !== -1) {\n    return window.document.body;\n  }\n\n  // Firefox want us to check `-x` and `-y` variations as well\n\n  var _getStyleComputedProp = getStyleComputedProperty(element),\n      overflow = _getStyleComputedProp.overflow,\n      overflowX = _getStyleComputedProp.overflowX,\n      overflowY = _getStyleComputedProp.overflowY;\n\n  if (/(auto|scroll)/.test(overflow + overflowY + overflowX)) {\n    return element;\n  }\n\n  return getScrollParent(getParentNode(element));\n}\n\n/**\n * Returns the offset parent of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} offset parent\n */\nfunction getOffsetParent(element) {\n  // NOTE: 1 DOM access here\n  var offsetParent = element && element.offsetParent;\n  var nodeName = offsetParent && offsetParent.nodeName;\n\n  if (!nodeName || nodeName === 'BODY' || nodeName === 'HTML') {\n    return window.document.documentElement;\n  }\n\n  // .offsetParent will return the closest TD or TABLE in case\n  // no offsetParent is present, I hate this job...\n  if (['TD', 'TABLE'].indexOf(offsetParent.nodeName) !== -1 && getStyleComputedProperty(offsetParent, 'position') === 'static') {\n    return getOffsetParent(offsetParent);\n  }\n\n  return offsetParent;\n}\n\nfunction isOffsetContainer(element) {\n  var nodeName = element.nodeName;\n\n  if (nodeName === 'BODY') {\n    return false;\n  }\n  return nodeName === 'HTML' || getOffsetParent(element.firstElementChild) === element;\n}\n\n/**\n * Finds the root node (document, shadowDOM root) of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} node\n * @returns {Element} root node\n */\nfunction getRoot(node) {\n  if (node.parentNode !== null) {\n    return getRoot(node.parentNode);\n  }\n\n  return node;\n}\n\n/**\n * Finds the offset parent common to the two provided nodes\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element1\n * @argument {Element} element2\n * @returns {Element} common offset parent\n */\nfunction findCommonOffsetParent(element1, element2) {\n  // This check is needed to avoid errors in case one of the elements isn't defined for any reason\n  if (!element1 || !element1.nodeType || !element2 || !element2.nodeType) {\n    return window.document.documentElement;\n  }\n\n  // Here we make sure to give as \"start\" the element that comes first in the DOM\n  var order = element1.compareDocumentPosition(element2) & Node.DOCUMENT_POSITION_FOLLOWING;\n  var start = order ? element1 : element2;\n  var end = order ? element2 : element1;\n\n  // Get common ancestor container\n  var range = document.createRange();\n  range.setStart(start, 0);\n  range.setEnd(end, 0);\n  var commonAncestorContainer = range.commonAncestorContainer;\n\n  // Both nodes are inside #document\n\n  if (element1 !== commonAncestorContainer && element2 !== commonAncestorContainer || start.contains(end)) {\n    if (isOffsetContainer(commonAncestorContainer)) {\n      return commonAncestorContainer;\n    }\n\n    return getOffsetParent(commonAncestorContainer);\n  }\n\n  // one of the nodes is inside shadowDOM, find which one\n  var element1root = getRoot(element1);\n  if (element1root.host) {\n    return findCommonOffsetParent(element1root.host, element2);\n  } else {\n    return findCommonOffsetParent(element1, getRoot(element2).host);\n  }\n}\n\n/**\n * Gets the scroll value of the given element in the given side (top and left)\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @argument {String} side `top` or `left`\n * @returns {number} amount of scrolled pixels\n */\nfunction getScroll(element) {\n  var side = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'top';\n\n  var upperSide = side === 'top' ? 'scrollTop' : 'scrollLeft';\n  var nodeName = element.nodeName;\n\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    var html = window.document.documentElement;\n    var scrollingElement = window.document.scrollingElement || html;\n    return scrollingElement[upperSide];\n  }\n\n  return element[upperSide];\n}\n\n/*\n * Sum or subtract the element scroll values (left and top) from a given rect object\n * @method\n * @memberof Popper.Utils\n * @param {Object} rect - Rect object you want to change\n * @param {HTMLElement} element - The element from the function reads the scroll values\n * @param {Boolean} subtract - set to true if you want to subtract the scroll values\n * @return {Object} rect - The modifier rect object\n */\nfunction includeScroll(rect, element) {\n  var subtract = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n\n  var scrollTop = getScroll(element, 'top');\n  var scrollLeft = getScroll(element, 'left');\n  var modifier = subtract ? -1 : 1;\n  rect.top += scrollTop * modifier;\n  rect.bottom += scrollTop * modifier;\n  rect.left += scrollLeft * modifier;\n  rect.right += scrollLeft * modifier;\n  return rect;\n}\n\n/*\n * Helper to detect borders of a given element\n * @method\n * @memberof Popper.Utils\n * @param {CSSStyleDeclaration} styles\n * Result of `getStyleComputedProperty` on the given element\n * @param {String} axis - `x` or `y`\n * @return {number} borders - The borders size of the given axis\n */\n\nfunction getBordersSize(styles, axis) {\n  var sideA = axis === 'x' ? 'Left' : 'Top';\n  var sideB = sideA === 'Left' ? 'Right' : 'Bottom';\n\n  return +styles['border' + sideA + 'Width'].split('px')[0] + +styles['border' + sideB + 'Width'].split('px')[0];\n}\n\n/**\n * Tells if you are running Internet Explorer 10\n * @method\n * @memberof Popper.Utils\n * @returns {Boolean} isIE10\n */\nvar isIE10 = undefined;\n\nvar isIE10$1 = function () {\n  if (isIE10 === undefined) {\n    isIE10 = navigator.appVersion.indexOf('MSIE 10') !== -1;\n  }\n  return isIE10;\n};\n\nfunction getSize(axis, body, html, computedStyle) {\n  return Math.max(body['offset' + axis], body['scroll' + axis], html['client' + axis], html['offset' + axis], html['scroll' + axis], isIE10$1() ? html['offset' + axis] + computedStyle['margin' + (axis === 'Height' ? 'Top' : 'Left')] + computedStyle['margin' + (axis === 'Height' ? 'Bottom' : 'Right')] : 0);\n}\n\nfunction getWindowSizes() {\n  var body = window.document.body;\n  var html = window.document.documentElement;\n  var computedStyle = isIE10$1() && window.getComputedStyle(html);\n\n  return {\n    height: getSize('Height', body, html, computedStyle),\n    width: getSize('Width', body, html, computedStyle)\n  };\n}\n\nvar classCallCheck = function (instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n};\n\nvar createClass = function () {\n  function defineProperties(target, props) {\n    for (var i = 0; i < props.length; i++) {\n      var descriptor = props[i];\n      descriptor.enumerable = descriptor.enumerable || false;\n      descriptor.configurable = true;\n      if (\"value\" in descriptor) descriptor.writable = true;\n      Object.defineProperty(target, descriptor.key, descriptor);\n    }\n  }\n\n  return function (Constructor, protoProps, staticProps) {\n    if (protoProps) defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) defineProperties(Constructor, staticProps);\n    return Constructor;\n  };\n}();\n\n\n\n\n\nvar defineProperty = function (obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n};\n\nvar _extends = Object.assign || function (target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i];\n\n    for (var key in source) {\n      if (Object.prototype.hasOwnProperty.call(source, key)) {\n        target[key] = source[key];\n      }\n    }\n  }\n\n  return target;\n};\n\n/**\n * Given element offsets, generate an output similar to getBoundingClientRect\n * @method\n * @memberof Popper.Utils\n * @argument {Object} offsets\n * @returns {Object} ClientRect like output\n */\nfunction getClientRect(offsets) {\n  return _extends({}, offsets, {\n    right: offsets.left + offsets.width,\n    bottom: offsets.top + offsets.height\n  });\n}\n\n/**\n * Get bounding client rect of given element\n * @method\n * @memberof Popper.Utils\n * @param {HTMLElement} element\n * @return {Object} client rect\n */\nfunction getBoundingClientRect(element) {\n  var rect = {};\n\n  // IE10 10 FIX: Please, don't ask, the element isn't\n  // considered in DOM in some circumstances...\n  // This isn't reproducible in IE10 compatibility mode of IE11\n  if (isIE10$1()) {\n    try {\n      rect = element.getBoundingClientRect();\n      var scrollTop = getScroll(element, 'top');\n      var scrollLeft = getScroll(element, 'left');\n      rect.top += scrollTop;\n      rect.left += scrollLeft;\n      rect.bottom += scrollTop;\n      rect.right += scrollLeft;\n    } catch (err) {}\n  } else {\n    rect = element.getBoundingClientRect();\n  }\n\n  var result = {\n    left: rect.left,\n    top: rect.top,\n    width: rect.right - rect.left,\n    height: rect.bottom - rect.top\n  };\n\n  // subtract scrollbar size from sizes\n  var sizes = element.nodeName === 'HTML' ? getWindowSizes() : {};\n  var width = sizes.width || element.clientWidth || result.right - result.left;\n  var height = sizes.height || element.clientHeight || result.bottom - result.top;\n\n  var horizScrollbar = element.offsetWidth - width;\n  var vertScrollbar = element.offsetHeight - height;\n\n  // if an hypothetical scrollbar is detected, we must be sure it's not a `border`\n  // we make this check conditional for performance reasons\n  if (horizScrollbar || vertScrollbar) {\n    var styles = getStyleComputedProperty(element);\n    horizScrollbar -= getBordersSize(styles, 'x');\n    vertScrollbar -= getBordersSize(styles, 'y');\n\n    result.width -= horizScrollbar;\n    result.height -= vertScrollbar;\n  }\n\n  return getClientRect(result);\n}\n\nfunction getOffsetRectRelativeToArbitraryNode(children, parent) {\n  var isIE10 = isIE10$1();\n  var isHTML = parent.nodeName === 'HTML';\n  var childrenRect = getBoundingClientRect(children);\n  var parentRect = getBoundingClientRect(parent);\n  var scrollParent = getScrollParent(children);\n\n  var styles = getStyleComputedProperty(parent);\n  var borderTopWidth = +styles.borderTopWidth.split('px')[0];\n  var borderLeftWidth = +styles.borderLeftWidth.split('px')[0];\n\n  var offsets = getClientRect({\n    top: childrenRect.top - parentRect.top - borderTopWidth,\n    left: childrenRect.left - parentRect.left - borderLeftWidth,\n    width: childrenRect.width,\n    height: childrenRect.height\n  });\n  offsets.marginTop = 0;\n  offsets.marginLeft = 0;\n\n  // Subtract margins of documentElement in case it's being used as parent\n  // we do this only on HTML because it's the only element that behaves\n  // differently when margins are applied to it. The margins are included in\n  // the box of the documentElement, in the other cases not.\n  if (!isIE10 && isHTML) {\n    var marginTop = +styles.marginTop.split('px')[0];\n    var marginLeft = +styles.marginLeft.split('px')[0];\n\n    offsets.top -= borderTopWidth - marginTop;\n    offsets.bottom -= borderTopWidth - marginTop;\n    offsets.left -= borderLeftWidth - marginLeft;\n    offsets.right -= borderLeftWidth - marginLeft;\n\n    // Attach marginTop and marginLeft because in some circumstances we may need them\n    offsets.marginTop = marginTop;\n    offsets.marginLeft = marginLeft;\n  }\n\n  if (isIE10 ? parent.contains(scrollParent) : parent === scrollParent && scrollParent.nodeName !== 'BODY') {\n    offsets = includeScroll(offsets, parent);\n  }\n\n  return offsets;\n}\n\nfunction getViewportOffsetRectRelativeToArtbitraryNode(element) {\n  var html = window.document.documentElement;\n  var relativeOffset = getOffsetRectRelativeToArbitraryNode(element, html);\n  var width = Math.max(html.clientWidth, window.innerWidth || 0);\n  var height = Math.max(html.clientHeight, window.innerHeight || 0);\n\n  var scrollTop = getScroll(html);\n  var scrollLeft = getScroll(html, 'left');\n\n  var offset = {\n    top: scrollTop - relativeOffset.top + relativeOffset.marginTop,\n    left: scrollLeft - relativeOffset.left + relativeOffset.marginLeft,\n    width: width,\n    height: height\n  };\n\n  return getClientRect(offset);\n}\n\n/**\n * Check if the given element is fixed or is inside a fixed parent\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @argument {Element} customContainer\n * @returns {Boolean} answer to \"isFixed?\"\n */\nfunction isFixed(element) {\n  var nodeName = element.nodeName;\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    return false;\n  }\n  if (getStyleComputedProperty(element, 'position') === 'fixed') {\n    return true;\n  }\n  return isFixed(getParentNode(element));\n}\n\n/**\n * Computed the boundaries limits and return them\n * @method\n * @memberof Popper.Utils\n * @param {HTMLElement} popper\n * @param {HTMLElement} reference\n * @param {number} padding\n * @param {HTMLElement} boundariesElement - Element used to define the boundaries\n * @returns {Object} Coordinates of the boundaries\n */\nfunction getBoundaries(popper, reference, padding, boundariesElement) {\n  // NOTE: 1 DOM access here\n  var boundaries = { top: 0, left: 0 };\n  var offsetParent = findCommonOffsetParent(popper, reference);\n\n  // Handle viewport case\n  if (boundariesElement === 'viewport') {\n    boundaries = getViewportOffsetRectRelativeToArtbitraryNode(offsetParent);\n  } else {\n    // Handle other cases based on DOM element used as boundaries\n    var boundariesNode = void 0;\n    if (boundariesElement === 'scrollParent') {\n      boundariesNode = getScrollParent(getParentNode(popper));\n      if (boundariesNode.nodeName === 'BODY') {\n        boundariesNode = window.document.documentElement;\n      }\n    } else if (boundariesElement === 'window') {\n      boundariesNode = window.document.documentElement;\n    } else {\n      boundariesNode = boundariesElement;\n    }\n\n    var offsets = getOffsetRectRelativeToArbitraryNode(boundariesNode, offsetParent);\n\n    // In case of HTML, we need a different computation\n    if (boundariesNode.nodeName === 'HTML' && !isFixed(offsetParent)) {\n      var _getWindowSizes = getWindowSizes(),\n          height = _getWindowSizes.height,\n          width = _getWindowSizes.width;\n\n      boundaries.top += offsets.top - offsets.marginTop;\n      boundaries.bottom = height + offsets.top;\n      boundaries.left += offsets.left - offsets.marginLeft;\n      boundaries.right = width + offsets.left;\n    } else {\n      // for all the other DOM elements, this one is good\n      boundaries = offsets;\n    }\n  }\n\n  // Add paddings\n  boundaries.left += padding;\n  boundaries.top += padding;\n  boundaries.right -= padding;\n  boundaries.bottom -= padding;\n\n  return boundaries;\n}\n\nfunction getArea(_ref) {\n  var width = _ref.width,\n      height = _ref.height;\n\n  return width * height;\n}\n\n/**\n * Utility used to transform the `auto` placement to the placement with more\n * available space.\n * @method\n * @memberof Popper.Utils\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction computeAutoPlacement(placement, refRect, popper, reference, boundariesElement) {\n  var padding = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : 0;\n\n  if (placement.indexOf('auto') === -1) {\n    return placement;\n  }\n\n  var boundaries = getBoundaries(popper, reference, padding, boundariesElement);\n\n  var rects = {\n    top: {\n      width: boundaries.width,\n      height: refRect.top - boundaries.top\n    },\n    right: {\n      width: boundaries.right - refRect.right,\n      height: boundaries.height\n    },\n    bottom: {\n      width: boundaries.width,\n      height: boundaries.bottom - refRect.bottom\n    },\n    left: {\n      width: refRect.left - boundaries.left,\n      height: boundaries.height\n    }\n  };\n\n  var sortedAreas = Object.keys(rects).map(function (key) {\n    return _extends({\n      key: key\n    }, rects[key], {\n      area: getArea(rects[key])\n    });\n  }).sort(function (a, b) {\n    return b.area - a.area;\n  });\n\n  var filteredAreas = sortedAreas.filter(function (_ref2) {\n    var width = _ref2.width,\n        height = _ref2.height;\n    return width >= popper.clientWidth && height >= popper.clientHeight;\n  });\n\n  var computedPlacement = filteredAreas.length > 0 ? filteredAreas[0].key : sortedAreas[0].key;\n\n  var variation = placement.split('-')[1];\n\n  return computedPlacement + (variation ? '-' + variation : '');\n}\n\n/**\n * Get offsets to the reference element\n * @method\n * @memberof Popper.Utils\n * @param {Object} state\n * @param {Element} popper - the popper element\n * @param {Element} reference - the reference element (the popper will be relative to this)\n * @returns {Object} An object containing the offsets which will be applied to the popper\n */\nfunction getReferenceOffsets(state, popper, reference) {\n  var commonOffsetParent = findCommonOffsetParent(popper, reference);\n  return getOffsetRectRelativeToArbitraryNode(reference, commonOffsetParent);\n}\n\n/**\n * Get the outer sizes of the given element (offset size + margins)\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Object} object containing width and height properties\n */\nfunction getOuterSizes(element) {\n  var styles = window.getComputedStyle(element);\n  var x = parseFloat(styles.marginTop) + parseFloat(styles.marginBottom);\n  var y = parseFloat(styles.marginLeft) + parseFloat(styles.marginRight);\n  var result = {\n    width: element.offsetWidth + y,\n    height: element.offsetHeight + x\n  };\n  return result;\n}\n\n/**\n * Get the opposite placement of the given one\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement\n * @returns {String} flipped placement\n */\nfunction getOppositePlacement(placement) {\n  var hash = { left: 'right', right: 'left', bottom: 'top', top: 'bottom' };\n  return placement.replace(/left|right|bottom|top/g, function (matched) {\n    return hash[matched];\n  });\n}\n\n/**\n * Get offsets to the popper\n * @method\n * @memberof Popper.Utils\n * @param {Object} position - CSS position the Popper will get applied\n * @param {HTMLElement} popper - the popper element\n * @param {Object} referenceOffsets - the reference offsets (the popper will be relative to this)\n * @param {String} placement - one of the valid placement options\n * @returns {Object} popperOffsets - An object containing the offsets which will be applied to the popper\n */\nfunction getPopperOffsets(popper, referenceOffsets, placement) {\n  placement = placement.split('-')[0];\n\n  // Get popper node sizes\n  var popperRect = getOuterSizes(popper);\n\n  // Add position, width and height to our offsets object\n  var popperOffsets = {\n    width: popperRect.width,\n    height: popperRect.height\n  };\n\n  // depending by the popper placement we have to compute its offsets slightly differently\n  var isHoriz = ['right', 'left'].indexOf(placement) !== -1;\n  var mainSide = isHoriz ? 'top' : 'left';\n  var secondarySide = isHoriz ? 'left' : 'top';\n  var measurement = isHoriz ? 'height' : 'width';\n  var secondaryMeasurement = !isHoriz ? 'height' : 'width';\n\n  popperOffsets[mainSide] = referenceOffsets[mainSide] + referenceOffsets[measurement] / 2 - popperRect[measurement] / 2;\n  if (placement === secondarySide) {\n    popperOffsets[secondarySide] = referenceOffsets[secondarySide] - popperRect[secondaryMeasurement];\n  } else {\n    popperOffsets[secondarySide] = referenceOffsets[getOppositePlacement(secondarySide)];\n  }\n\n  return popperOffsets;\n}\n\n/**\n * Mimics the `find` method of Array\n * @method\n * @memberof Popper.Utils\n * @argument {Array} arr\n * @argument prop\n * @argument value\n * @returns index or -1\n */\nfunction find(arr, check) {\n  // use native find if supported\n  if (Array.prototype.find) {\n    return arr.find(check);\n  }\n\n  // use `filter` to obtain the same behavior of `find`\n  return arr.filter(check)[0];\n}\n\n/**\n * Return the index of the matching object\n * @method\n * @memberof Popper.Utils\n * @argument {Array} arr\n * @argument prop\n * @argument value\n * @returns index or -1\n */\nfunction findIndex(arr, prop, value) {\n  // use native findIndex if supported\n  if (Array.prototype.findIndex) {\n    return arr.findIndex(function (cur) {\n      return cur[prop] === value;\n    });\n  }\n\n  // use `find` + `indexOf` if `findIndex` isn't supported\n  var match = find(arr, function (obj) {\n    return obj[prop] === value;\n  });\n  return arr.indexOf(match);\n}\n\n/**\n * Loop trough the list of modifiers and run them in order,\n * each of them will then edit the data object.\n * @method\n * @memberof Popper.Utils\n * @param {dataObject} data\n * @param {Array} modifiers\n * @param {String} ends - Optional modifier name used as stopper\n * @returns {dataObject}\n */\nfunction runModifiers(modifiers, data, ends) {\n  var modifiersToRun = ends === undefined ? modifiers : modifiers.slice(0, findIndex(modifiers, 'name', ends));\n\n  modifiersToRun.forEach(function (modifier) {\n    if (modifier.function) {\n      console.warn('`modifier.function` is deprecated, use `modifier.fn`!');\n    }\n    var fn = modifier.function || modifier.fn;\n    if (modifier.enabled && isFunction(fn)) {\n      // Add properties to offsets to make them a complete clientRect object\n      // we do this before each modifier to make sure the previous one doesn't\n      // mess with these values\n      data.offsets.popper = getClientRect(data.offsets.popper);\n      data.offsets.reference = getClientRect(data.offsets.reference);\n\n      data = fn(data, modifier);\n    }\n  });\n\n  return data;\n}\n\n/**\n * Updates the position of the popper, computing the new offsets and applying\n * the new style.<br />\n * Prefer `scheduleUpdate` over `update` because of performance reasons.\n * @method\n * @memberof Popper\n */\nfunction update() {\n  // if popper is destroyed, don't perform any further update\n  if (this.state.isDestroyed) {\n    return;\n  }\n\n  var data = {\n    instance: this,\n    styles: {},\n    arrowStyles: {},\n    attributes: {},\n    flipped: false,\n    offsets: {}\n  };\n\n  // compute reference element offsets\n  data.offsets.reference = getReferenceOffsets(this.state, this.popper, this.reference);\n\n  // compute auto placement, store placement inside the data object,\n  // modifiers will be able to edit `placement` if needed\n  // and refer to originalPlacement to know the original value\n  data.placement = computeAutoPlacement(this.options.placement, data.offsets.reference, this.popper, this.reference, this.options.modifiers.flip.boundariesElement, this.options.modifiers.flip.padding);\n\n  // store the computed placement inside `originalPlacement`\n  data.originalPlacement = data.placement;\n\n  // compute the popper offsets\n  data.offsets.popper = getPopperOffsets(this.popper, data.offsets.reference, data.placement);\n  data.offsets.popper.position = 'absolute';\n\n  // run the modifiers\n  data = runModifiers(this.modifiers, data);\n\n  // the first `update` will call `onCreate` callback\n  // the other ones will call `onUpdate` callback\n  if (!this.state.isCreated) {\n    this.state.isCreated = true;\n    this.options.onCreate(data);\n  } else {\n    this.options.onUpdate(data);\n  }\n}\n\n/**\n * Helper used to know if the given modifier is enabled.\n * @method\n * @memberof Popper.Utils\n * @returns {Boolean}\n */\nfunction isModifierEnabled(modifiers, modifierName) {\n  return modifiers.some(function (_ref) {\n    var name = _ref.name,\n        enabled = _ref.enabled;\n    return enabled && name === modifierName;\n  });\n}\n\n/**\n * Get the prefixed supported property name\n * @method\n * @memberof Popper.Utils\n * @argument {String} property (camelCase)\n * @returns {String} prefixed property (camelCase or PascalCase, depending on the vendor prefix)\n */\nfunction getSupportedPropertyName(property) {\n  var prefixes = [false, 'ms', 'Webkit', 'Moz', 'O'];\n  var upperProp = property.charAt(0).toUpperCase() + property.slice(1);\n\n  for (var i = 0; i < prefixes.length - 1; i++) {\n    var prefix = prefixes[i];\n    var toCheck = prefix ? '' + prefix + upperProp : property;\n    if (typeof window.document.body.style[toCheck] !== 'undefined') {\n      return toCheck;\n    }\n  }\n  return null;\n}\n\n/**\n * Destroy the popper\n * @method\n * @memberof Popper\n */\nfunction destroy() {\n  this.state.isDestroyed = true;\n\n  // touch DOM only if `applyStyle` modifier is enabled\n  if (isModifierEnabled(this.modifiers, 'applyStyle')) {\n    this.popper.removeAttribute('x-placement');\n    this.popper.style.left = '';\n    this.popper.style.position = '';\n    this.popper.style.top = '';\n    this.popper.style[getSupportedPropertyName('transform')] = '';\n  }\n\n  this.disableEventListeners();\n\n  // remove the popper if user explicity asked for the deletion on destroy\n  // do not use `remove` because IE11 doesn't support it\n  if (this.options.removeOnDestroy) {\n    this.popper.parentNode.removeChild(this.popper);\n  }\n  return this;\n}\n\nfunction attachToScrollParents(scrollParent, event, callback, scrollParents) {\n  var isBody = scrollParent.nodeName === 'BODY';\n  var target = isBody ? window : scrollParent;\n  target.addEventListener(event, callback, { passive: true });\n\n  if (!isBody) {\n    attachToScrollParents(getScrollParent(target.parentNode), event, callback, scrollParents);\n  }\n  scrollParents.push(target);\n}\n\n/**\n * Setup needed event listeners used to update the popper position\n * @method\n * @memberof Popper.Utils\n * @private\n */\nfunction setupEventListeners(reference, options, state, updateBound) {\n  // Resize event listener on window\n  state.updateBound = updateBound;\n  window.addEventListener('resize', state.updateBound, { passive: true });\n\n  // Scroll event listener on scroll parents\n  var scrollElement = getScrollParent(reference);\n  attachToScrollParents(scrollElement, 'scroll', state.updateBound, state.scrollParents);\n  state.scrollElement = scrollElement;\n  state.eventsEnabled = true;\n\n  return state;\n}\n\n/**\n * It will add resize/scroll events and start recalculating\n * position of the popper element when they are triggered.\n * @method\n * @memberof Popper\n */\nfunction enableEventListeners() {\n  if (!this.state.eventsEnabled) {\n    this.state = setupEventListeners(this.reference, this.options, this.state, this.scheduleUpdate);\n  }\n}\n\n/**\n * Remove event listeners used to update the popper position\n * @method\n * @memberof Popper.Utils\n * @private\n */\nfunction removeEventListeners(reference, state) {\n  // Remove resize event listener on window\n  window.removeEventListener('resize', state.updateBound);\n\n  // Remove scroll event listener on scroll parents\n  state.scrollParents.forEach(function (target) {\n    target.removeEventListener('scroll', state.updateBound);\n  });\n\n  // Reset state\n  state.updateBound = null;\n  state.scrollParents = [];\n  state.scrollElement = null;\n  state.eventsEnabled = false;\n  return state;\n}\n\n/**\n * It will remove resize/scroll events and won't recalculate popper position\n * when they are triggered. It also won't trigger onUpdate callback anymore,\n * unless you call `update` method manually.\n * @method\n * @memberof Popper\n */\nfunction disableEventListeners() {\n  if (this.state.eventsEnabled) {\n    window.cancelAnimationFrame(this.scheduleUpdate);\n    this.state = removeEventListeners(this.reference, this.state);\n  }\n}\n\n/**\n * Tells if a given input is a number\n * @method\n * @memberof Popper.Utils\n * @param {*} input to check\n * @return {Boolean}\n */\nfunction isNumeric(n) {\n  return n !== '' && !isNaN(parseFloat(n)) && isFinite(n);\n}\n\n/**\n * Set the style to the given popper\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element - Element to apply the style to\n * @argument {Object} styles\n * Object with a list of properties and values which will be applied to the element\n */\nfunction setStyles(element, styles) {\n  Object.keys(styles).forEach(function (prop) {\n    var unit = '';\n    // add unit if the value is numeric and is one of the following\n    if (['width', 'height', 'top', 'right', 'bottom', 'left'].indexOf(prop) !== -1 && isNumeric(styles[prop])) {\n      unit = 'px';\n    }\n    element.style[prop] = styles[prop] + unit;\n  });\n}\n\n/**\n * Set the attributes to the given popper\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element - Element to apply the attributes to\n * @argument {Object} styles\n * Object with a list of properties and values which will be applied to the element\n */\nfunction setAttributes(element, attributes) {\n  Object.keys(attributes).forEach(function (prop) {\n    var value = attributes[prop];\n    if (value !== false) {\n      element.setAttribute(prop, attributes[prop]);\n    } else {\n      element.removeAttribute(prop);\n    }\n  });\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} data.styles - List of style properties - values to apply to popper element\n * @argument {Object} data.attributes - List of attribute properties - values to apply to popper element\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The same data object\n */\nfunction applyStyle(data) {\n  // any property present in `data.styles` will be applied to the popper,\n  // in this way we can make the 3rd party modifiers add custom styles to it\n  // Be aware, modifiers could override the properties defined in the previous\n  // lines of this modifier!\n  setStyles(data.instance.popper, data.styles);\n\n  // any property present in `data.attributes` will be applied to the popper,\n  // they will be set as HTML attributes of the element\n  setAttributes(data.instance.popper, data.attributes);\n\n  // if arrowElement is defined and arrowStyles has some properties\n  if (data.arrowElement && Object.keys(data.arrowStyles).length) {\n    setStyles(data.arrowElement, data.arrowStyles);\n  }\n\n  return data;\n}\n\n/**\n * Set the x-placement attribute before everything else because it could be used\n * to add margins to the popper margins needs to be calculated to get the\n * correct popper offsets.\n * @method\n * @memberof Popper.modifiers\n * @param {HTMLElement} reference - The reference element used to position the popper\n * @param {HTMLElement} popper - The HTML element used as popper.\n * @param {Object} options - Popper.js options\n */\nfunction applyStyleOnLoad(reference, popper, options, modifierOptions, state) {\n  // compute reference element offsets\n  var referenceOffsets = getReferenceOffsets(state, popper, reference);\n\n  // compute auto placement, store placement inside the data object,\n  // modifiers will be able to edit `placement` if needed\n  // and refer to originalPlacement to know the original value\n  var placement = computeAutoPlacement(options.placement, referenceOffsets, popper, reference, options.modifiers.flip.boundariesElement, options.modifiers.flip.padding);\n\n  popper.setAttribute('x-placement', placement);\n\n  // Apply `position` to popper before anything else because\n  // without the position applied we can't guarantee correct computations\n  setStyles(popper, { position: 'absolute' });\n\n  return options;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction computeStyle(data, options) {\n  var x = options.x,\n      y = options.y;\n  var popper = data.offsets.popper;\n\n  // Remove this legacy support in Popper.js v2\n\n  var legacyGpuAccelerationOption = find(data.instance.modifiers, function (modifier) {\n    return modifier.name === 'applyStyle';\n  }).gpuAcceleration;\n  if (legacyGpuAccelerationOption !== undefined) {\n    console.warn('WARNING: `gpuAcceleration` option moved to `computeStyle` modifier and will not be supported in future versions of Popper.js!');\n  }\n  var gpuAcceleration = legacyGpuAccelerationOption !== undefined ? legacyGpuAccelerationOption : options.gpuAcceleration;\n\n  var offsetParent = getOffsetParent(data.instance.popper);\n  var offsetParentRect = getBoundingClientRect(offsetParent);\n\n  // Styles\n  var styles = {\n    position: popper.position\n  };\n\n  // floor sides to avoid blurry text\n  var offsets = {\n    left: Math.floor(popper.left),\n    top: Math.floor(popper.top),\n    bottom: Math.floor(popper.bottom),\n    right: Math.floor(popper.right)\n  };\n\n  var sideA = x === 'bottom' ? 'top' : 'bottom';\n  var sideB = y === 'right' ? 'left' : 'right';\n\n  // if gpuAcceleration is set to `true` and transform is supported,\n  //  we use `translate3d` to apply the position to the popper we\n  // automatically use the supported prefixed version if needed\n  var prefixedProperty = getSupportedPropertyName('transform');\n\n  // now, let's make a step back and look at this code closely (wtf?)\n  // If the content of the popper grows once it's been positioned, it\n  // may happen that the popper gets misplaced because of the new content\n  // overflowing its reference element\n  // To avoid this problem, we provide two options (x and y), which allow\n  // the consumer to define the offset origin.\n  // If we position a popper on top of a reference element, we can set\n  // `x` to `top` to make the popper grow towards its top instead of\n  // its bottom.\n  var left = void 0,\n      top = void 0;\n  if (sideA === 'bottom') {\n    top = -offsetParentRect.height + offsets.bottom;\n  } else {\n    top = offsets.top;\n  }\n  if (sideB === 'right') {\n    left = -offsetParentRect.width + offsets.right;\n  } else {\n    left = offsets.left;\n  }\n  if (gpuAcceleration && prefixedProperty) {\n    styles[prefixedProperty] = 'translate3d(' + left + 'px, ' + top + 'px, 0)';\n    styles[sideA] = 0;\n    styles[sideB] = 0;\n    styles.willChange = 'transform';\n  } else {\n    // othwerise, we use the standard `top`, `left`, `bottom` and `right` properties\n    var invertTop = sideA === 'bottom' ? -1 : 1;\n    var invertLeft = sideB === 'right' ? -1 : 1;\n    styles[sideA] = top * invertTop;\n    styles[sideB] = left * invertLeft;\n    styles.willChange = sideA + ', ' + sideB;\n  }\n\n  // Attributes\n  var attributes = {\n    'x-placement': data.placement\n  };\n\n  // Update `data` attributes, styles and arrowStyles\n  data.attributes = _extends({}, attributes, data.attributes);\n  data.styles = _extends({}, styles, data.styles);\n  data.arrowStyles = _extends({}, data.offsets.arrow, data.arrowStyles);\n\n  return data;\n}\n\n/**\n * Helper used to know if the given modifier depends from another one.<br />\n * It checks if the needed modifier is listed and enabled.\n * @method\n * @memberof Popper.Utils\n * @param {Array} modifiers - list of modifiers\n * @param {String} requestingName - name of requesting modifier\n * @param {String} requestedName - name of requested modifier\n * @returns {Boolean}\n */\nfunction isModifierRequired(modifiers, requestingName, requestedName) {\n  var requesting = find(modifiers, function (_ref) {\n    var name = _ref.name;\n    return name === requestingName;\n  });\n\n  var isRequired = !!requesting && modifiers.some(function (modifier) {\n    return modifier.name === requestedName && modifier.enabled && modifier.order < requesting.order;\n  });\n\n  if (!isRequired) {\n    var _requesting = '`' + requestingName + '`';\n    var requested = '`' + requestedName + '`';\n    console.warn(requested + ' modifier is required by ' + _requesting + ' modifier in order to work, be sure to include it before ' + _requesting + '!');\n  }\n  return isRequired;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction arrow(data, options) {\n  // arrow depends on keepTogether in order to work\n  if (!isModifierRequired(data.instance.modifiers, 'arrow', 'keepTogether')) {\n    return data;\n  }\n\n  var arrowElement = options.element;\n\n  // if arrowElement is a string, suppose it's a CSS selector\n  if (typeof arrowElement === 'string') {\n    arrowElement = data.instance.popper.querySelector(arrowElement);\n\n    // if arrowElement is not found, don't run the modifier\n    if (!arrowElement) {\n      return data;\n    }\n  } else {\n    // if the arrowElement isn't a query selector we must check that the\n    // provided DOM node is child of its popper node\n    if (!data.instance.popper.contains(arrowElement)) {\n      console.warn('WARNING: `arrow.element` must be child of its popper element!');\n      return data;\n    }\n  }\n\n  var placement = data.placement.split('-')[0];\n  var _data$offsets = data.offsets,\n      popper = _data$offsets.popper,\n      reference = _data$offsets.reference;\n\n  var isVertical = ['left', 'right'].indexOf(placement) !== -1;\n\n  var len = isVertical ? 'height' : 'width';\n  var sideCapitalized = isVertical ? 'Top' : 'Left';\n  var side = sideCapitalized.toLowerCase();\n  var altSide = isVertical ? 'left' : 'top';\n  var opSide = isVertical ? 'bottom' : 'right';\n  var arrowElementSize = getOuterSizes(arrowElement)[len];\n\n  //\n  // extends keepTogether behavior making sure the popper and its\n  // reference have enough pixels in conjuction\n  //\n\n  // top/left side\n  if (reference[opSide] - arrowElementSize < popper[side]) {\n    data.offsets.popper[side] -= popper[side] - (reference[opSide] - arrowElementSize);\n  }\n  // bottom/right side\n  if (reference[side] + arrowElementSize > popper[opSide]) {\n    data.offsets.popper[side] += reference[side] + arrowElementSize - popper[opSide];\n  }\n\n  // compute center of the popper\n  var center = reference[side] + reference[len] / 2 - arrowElementSize / 2;\n\n  // Compute the sideValue using the updated popper offsets\n  // take popper margin in account because we don't have this info available\n  var popperMarginSide = getStyleComputedProperty(data.instance.popper, 'margin' + sideCapitalized).replace('px', '');\n  var sideValue = center - getClientRect(data.offsets.popper)[side] - popperMarginSide;\n\n  // prevent arrowElement from being placed not contiguously to its popper\n  sideValue = Math.max(Math.min(popper[len] - arrowElementSize, sideValue), 0);\n\n  data.arrowElement = arrowElement;\n  data.offsets.arrow = {};\n  data.offsets.arrow[side] = Math.round(sideValue);\n  data.offsets.arrow[altSide] = ''; // make sure to unset any eventual altSide value from the DOM node\n\n  return data;\n}\n\n/**\n * Get the opposite placement variation of the given one\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement variation\n * @returns {String} flipped placement variation\n */\nfunction getOppositeVariation(variation) {\n  if (variation === 'end') {\n    return 'start';\n  } else if (variation === 'start') {\n    return 'end';\n  }\n  return variation;\n}\n\n/**\n * List of accepted placements to use as values of the `placement` option.<br />\n * Valid placements are:\n * - `auto`\n * - `top`\n * - `right`\n * - `bottom`\n * - `left`\n *\n * Each placement can have a variation from this list:\n * - `-start`\n * - `-end`\n *\n * Variations are interpreted easily if you think of them as the left to right\n * written languages. Horizontally (`top` and `bottom`), `start` is left and `end`\n * is right.<br />\n * Vertically (`left` and `right`), `start` is top and `end` is bottom.\n *\n * Some valid examples are:\n * - `top-end` (on top of reference, right aligned)\n * - `right-start` (on right of reference, top aligned)\n * - `bottom` (on bottom, centered)\n * - `auto-right` (on the side with more space available, alignment depends by placement)\n *\n * @static\n * @type {Array}\n * @enum {String}\n * @readonly\n * @method placements\n * @memberof Popper\n */\nvar placements = ['auto-start', 'auto', 'auto-end', 'top-start', 'top', 'top-end', 'right-start', 'right', 'right-end', 'bottom-end', 'bottom', 'bottom-start', 'left-end', 'left', 'left-start'];\n\n// Get rid of `auto` `auto-start` and `auto-end`\nvar validPlacements = placements.slice(3);\n\n/**\n * Given an initial placement, returns all the subsequent placements\n * clockwise (or counter-clockwise).\n *\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement - A valid placement (it accepts variations)\n * @argument {Boolean} counter - Set to true to walk the placements counterclockwise\n * @returns {Array} placements including their variations\n */\nfunction clockwise(placement) {\n  var counter = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n\n  var index = validPlacements.indexOf(placement);\n  var arr = validPlacements.slice(index + 1).concat(validPlacements.slice(0, index));\n  return counter ? arr.reverse() : arr;\n}\n\nvar BEHAVIORS = {\n  FLIP: 'flip',\n  CLOCKWISE: 'clockwise',\n  COUNTERCLOCKWISE: 'counterclockwise'\n};\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction flip(data, options) {\n  // if `inner` modifier is enabled, we can't use the `flip` modifier\n  if (isModifierEnabled(data.instance.modifiers, 'inner')) {\n    return data;\n  }\n\n  if (data.flipped && data.placement === data.originalPlacement) {\n    // seems like flip is trying to loop, probably there's not enough space on any of the flippable sides\n    return data;\n  }\n\n  var boundaries = getBoundaries(data.instance.popper, data.instance.reference, options.padding, options.boundariesElement);\n\n  var placement = data.placement.split('-')[0];\n  var placementOpposite = getOppositePlacement(placement);\n  var variation = data.placement.split('-')[1] || '';\n\n  var flipOrder = [];\n\n  switch (options.behavior) {\n    case BEHAVIORS.FLIP:\n      flipOrder = [placement, placementOpposite];\n      break;\n    case BEHAVIORS.CLOCKWISE:\n      flipOrder = clockwise(placement);\n      break;\n    case BEHAVIORS.COUNTERCLOCKWISE:\n      flipOrder = clockwise(placement, true);\n      break;\n    default:\n      flipOrder = options.behavior;\n  }\n\n  flipOrder.forEach(function (step, index) {\n    if (placement !== step || flipOrder.length === index + 1) {\n      return data;\n    }\n\n    placement = data.placement.split('-')[0];\n    placementOpposite = getOppositePlacement(placement);\n\n    var popperOffsets = data.offsets.popper;\n    var refOffsets = data.offsets.reference;\n\n    // using floor because the reference offsets may contain decimals we are not going to consider here\n    var floor = Math.floor;\n    var overlapsRef = placement === 'left' && floor(popperOffsets.right) > floor(refOffsets.left) || placement === 'right' && floor(popperOffsets.left) < floor(refOffsets.right) || placement === 'top' && floor(popperOffsets.bottom) > floor(refOffsets.top) || placement === 'bottom' && floor(popperOffsets.top) < floor(refOffsets.bottom);\n\n    var overflowsLeft = floor(popperOffsets.left) < floor(boundaries.left);\n    var overflowsRight = floor(popperOffsets.right) > floor(boundaries.right);\n    var overflowsTop = floor(popperOffsets.top) < floor(boundaries.top);\n    var overflowsBottom = floor(popperOffsets.bottom) > floor(boundaries.bottom);\n\n    var overflowsBoundaries = placement === 'left' && overflowsLeft || placement === 'right' && overflowsRight || placement === 'top' && overflowsTop || placement === 'bottom' && overflowsBottom;\n\n    // flip the variation if required\n    var isVertical = ['top', 'bottom'].indexOf(placement) !== -1;\n    var flippedVariation = !!options.flipVariations && (isVertical && variation === 'start' && overflowsLeft || isVertical && variation === 'end' && overflowsRight || !isVertical && variation === 'start' && overflowsTop || !isVertical && variation === 'end' && overflowsBottom);\n\n    if (overlapsRef || overflowsBoundaries || flippedVariation) {\n      // this boolean to detect any flip loop\n      data.flipped = true;\n\n      if (overlapsRef || overflowsBoundaries) {\n        placement = flipOrder[index + 1];\n      }\n\n      if (flippedVariation) {\n        variation = getOppositeVariation(variation);\n      }\n\n      data.placement = placement + (variation ? '-' + variation : '');\n\n      // this object contains `position`, we want to preserve it along with\n      // any additional property we may add in the future\n      data.offsets.popper = _extends({}, data.offsets.popper, getPopperOffsets(data.instance.popper, data.offsets.reference, data.placement));\n\n      data = runModifiers(data.instance.modifiers, data, 'flip');\n    }\n  });\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction keepTogether(data) {\n  var _data$offsets = data.offsets,\n      popper = _data$offsets.popper,\n      reference = _data$offsets.reference;\n\n  var placement = data.placement.split('-')[0];\n  var floor = Math.floor;\n  var isVertical = ['top', 'bottom'].indexOf(placement) !== -1;\n  var side = isVertical ? 'right' : 'bottom';\n  var opSide = isVertical ? 'left' : 'top';\n  var measurement = isVertical ? 'width' : 'height';\n\n  if (popper[side] < floor(reference[opSide])) {\n    data.offsets.popper[opSide] = floor(reference[opSide]) - popper[measurement];\n  }\n  if (popper[opSide] > floor(reference[side])) {\n    data.offsets.popper[opSide] = floor(reference[side]);\n  }\n\n  return data;\n}\n\n/**\n * Converts a string containing value + unit into a px value number\n * @function\n * @memberof {modifiers~offset}\n * @private\n * @argument {String} str - Value + unit string\n * @argument {String} measurement - `height` or `width`\n * @argument {Object} popperOffsets\n * @argument {Object} referenceOffsets\n * @returns {Number|String}\n * Value in pixels, or original string if no values were extracted\n */\nfunction toValue(str, measurement, popperOffsets, referenceOffsets) {\n  // separate value from unit\n  var split = str.match(/((?:\\-|\\+)?\\d*\\.?\\d*)(.*)/);\n  var value = +split[1];\n  var unit = split[2];\n\n  // If it's not a number it's an operator, I guess\n  if (!value) {\n    return str;\n  }\n\n  if (unit.indexOf('%') === 0) {\n    var element = void 0;\n    switch (unit) {\n      case '%p':\n        element = popperOffsets;\n        break;\n      case '%':\n      case '%r':\n      default:\n        element = referenceOffsets;\n    }\n\n    var rect = getClientRect(element);\n    return rect[measurement] / 100 * value;\n  } else if (unit === 'vh' || unit === 'vw') {\n    // if is a vh or vw, we calculate the size based on the viewport\n    var size = void 0;\n    if (unit === 'vh') {\n      size = Math.max(document.documentElement.clientHeight, window.innerHeight || 0);\n    } else {\n      size = Math.max(document.documentElement.clientWidth, window.innerWidth || 0);\n    }\n    return size / 100 * value;\n  } else {\n    // if is an explicit pixel unit, we get rid of the unit and keep the value\n    // if is an implicit unit, it's px, and we return just the value\n    return value;\n  }\n}\n\n/**\n * Parse an `offset` string to extrapolate `x` and `y` numeric offsets.\n * @function\n * @memberof {modifiers~offset}\n * @private\n * @argument {String} offset\n * @argument {Object} popperOffsets\n * @argument {Object} referenceOffsets\n * @argument {String} basePlacement\n * @returns {Array} a two cells array with x and y offsets in numbers\n */\nfunction parseOffset(offset, popperOffsets, referenceOffsets, basePlacement) {\n  var offsets = [0, 0];\n\n  // Use height if placement is left or right and index is 0 otherwise use width\n  // in this way the first offset will use an axis and the second one\n  // will use the other one\n  var useHeight = ['right', 'left'].indexOf(basePlacement) !== -1;\n\n  // Split the offset string to obtain a list of values and operands\n  // The regex addresses values with the plus or minus sign in front (+10, -20, etc)\n  var fragments = offset.split(/(\\+|\\-)/).map(function (frag) {\n    return frag.trim();\n  });\n\n  // Detect if the offset string contains a pair of values or a single one\n  // they could be separated by comma or space\n  var divider = fragments.indexOf(find(fragments, function (frag) {\n    return frag.search(/,|\\s/) !== -1;\n  }));\n\n  if (fragments[divider] && fragments[divider].indexOf(',') === -1) {\n    console.warn('Offsets separated by white space(s) are deprecated, use a comma (,) instead.');\n  }\n\n  // If divider is found, we divide the list of values and operands to divide\n  // them by ofset X and Y.\n  var splitRegex = /\\s*,\\s*|\\s+/;\n  var ops = divider !== -1 ? [fragments.slice(0, divider).concat([fragments[divider].split(splitRegex)[0]]), [fragments[divider].split(splitRegex)[1]].concat(fragments.slice(divider + 1))] : [fragments];\n\n  // Convert the values with units to absolute pixels to allow our computations\n  ops = ops.map(function (op, index) {\n    // Most of the units rely on the orientation of the popper\n    var measurement = (index === 1 ? !useHeight : useHeight) ? 'height' : 'width';\n    var mergeWithPrevious = false;\n    return op\n    // This aggregates any `+` or `-` sign that aren't considered operators\n    // e.g.: 10 + +5 => [10, +, +5]\n    .reduce(function (a, b) {\n      if (a[a.length - 1] === '' && ['+', '-'].indexOf(b) !== -1) {\n        a[a.length - 1] = b;\n        mergeWithPrevious = true;\n        return a;\n      } else if (mergeWithPrevious) {\n        a[a.length - 1] += b;\n        mergeWithPrevious = false;\n        return a;\n      } else {\n        return a.concat(b);\n      }\n    }, [])\n    // Here we convert the string values into number values (in px)\n    .map(function (str) {\n      return toValue(str, measurement, popperOffsets, referenceOffsets);\n    });\n  });\n\n  // Loop trough the offsets arrays and execute the operations\n  ops.forEach(function (op, index) {\n    op.forEach(function (frag, index2) {\n      if (isNumeric(frag)) {\n        offsets[index] += frag * (op[index2 - 1] === '-' ? -1 : 1);\n      }\n    });\n  });\n  return offsets;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @argument {Number|String} options.offset=0\n * The offset value as described in the modifier description\n * @returns {Object} The data object, properly modified\n */\nfunction offset(data, _ref) {\n  var offset = _ref.offset;\n  var placement = data.placement,\n      _data$offsets = data.offsets,\n      popper = _data$offsets.popper,\n      reference = _data$offsets.reference;\n\n  var basePlacement = placement.split('-')[0];\n\n  var offsets = void 0;\n  if (isNumeric(+offset)) {\n    offsets = [+offset, 0];\n  } else {\n    offsets = parseOffset(offset, popper, reference, basePlacement);\n  }\n\n  if (basePlacement === 'left') {\n    popper.top += offsets[0];\n    popper.left -= offsets[1];\n  } else if (basePlacement === 'right') {\n    popper.top += offsets[0];\n    popper.left += offsets[1];\n  } else if (basePlacement === 'top') {\n    popper.left += offsets[0];\n    popper.top -= offsets[1];\n  } else if (basePlacement === 'bottom') {\n    popper.left += offsets[0];\n    popper.top += offsets[1];\n  }\n\n  data.popper = popper;\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction preventOverflow(data, options) {\n  var boundariesElement = options.boundariesElement || getOffsetParent(data.instance.popper);\n\n  // If offsetParent is the reference element, we really want to\n  // go one step up and use the next offsetParent as reference to\n  // avoid to make this modifier completely useless and look like broken\n  if (data.instance.reference === boundariesElement) {\n    boundariesElement = getOffsetParent(boundariesElement);\n  }\n\n  var boundaries = getBoundaries(data.instance.popper, data.instance.reference, options.padding, boundariesElement);\n  options.boundaries = boundaries;\n\n  var order = options.priority;\n  var popper = data.offsets.popper;\n\n  var check = {\n    primary: function primary(placement) {\n      var value = popper[placement];\n      if (popper[placement] < boundaries[placement] && !options.escapeWithReference) {\n        value = Math.max(popper[placement], boundaries[placement]);\n      }\n      return defineProperty({}, placement, value);\n    },\n    secondary: function secondary(placement) {\n      var mainSide = placement === 'right' ? 'left' : 'top';\n      var value = popper[mainSide];\n      if (popper[placement] > boundaries[placement] && !options.escapeWithReference) {\n        value = Math.min(popper[mainSide], boundaries[placement] - (placement === 'right' ? popper.width : popper.height));\n      }\n      return defineProperty({}, mainSide, value);\n    }\n  };\n\n  order.forEach(function (placement) {\n    var side = ['left', 'top'].indexOf(placement) !== -1 ? 'primary' : 'secondary';\n    popper = _extends({}, popper, check[side](placement));\n  });\n\n  data.offsets.popper = popper;\n\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction shift(data) {\n  var placement = data.placement;\n  var basePlacement = placement.split('-')[0];\n  var shiftvariation = placement.split('-')[1];\n\n  // if shift shiftvariation is specified, run the modifier\n  if (shiftvariation) {\n    var _data$offsets = data.offsets,\n        reference = _data$offsets.reference,\n        popper = _data$offsets.popper;\n\n    var isVertical = ['bottom', 'top'].indexOf(basePlacement) !== -1;\n    var side = isVertical ? 'left' : 'top';\n    var measurement = isVertical ? 'width' : 'height';\n\n    var shiftOffsets = {\n      start: defineProperty({}, side, reference[side]),\n      end: defineProperty({}, side, reference[side] + reference[measurement] - popper[measurement])\n    };\n\n    data.offsets.popper = _extends({}, popper, shiftOffsets[shiftvariation]);\n  }\n\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction hide(data) {\n  if (!isModifierRequired(data.instance.modifiers, 'hide', 'preventOverflow')) {\n    return data;\n  }\n\n  var refRect = data.offsets.reference;\n  var bound = find(data.instance.modifiers, function (modifier) {\n    return modifier.name === 'preventOverflow';\n  }).boundaries;\n\n  if (refRect.bottom < bound.top || refRect.left > bound.right || refRect.top > bound.bottom || refRect.right < bound.left) {\n    // Avoid unnecessary DOM access if visibility hasn't changed\n    if (data.hide === true) {\n      return data;\n    }\n\n    data.hide = true;\n    data.attributes['x-out-of-boundaries'] = '';\n  } else {\n    // Avoid unnecessary DOM access if visibility hasn't changed\n    if (data.hide === false) {\n      return data;\n    }\n\n    data.hide = false;\n    data.attributes['x-out-of-boundaries'] = false;\n  }\n\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction inner(data) {\n  var placement = data.placement;\n  var basePlacement = placement.split('-')[0];\n  var _data$offsets = data.offsets,\n      popper = _data$offsets.popper,\n      reference = _data$offsets.reference;\n\n  var isHoriz = ['left', 'right'].indexOf(basePlacement) !== -1;\n\n  var subtractLength = ['top', 'left'].indexOf(basePlacement) === -1;\n\n  popper[isHoriz ? 'left' : 'top'] = reference[basePlacement] - (subtractLength ? popper[isHoriz ? 'width' : 'height'] : 0);\n\n  data.placement = getOppositePlacement(placement);\n  data.offsets.popper = getClientRect(popper);\n\n  return data;\n}\n\n/**\n * Modifier function, each modifier can have a function of this type assigned\n * to its `fn` property.<br />\n * These functions will be called on each update, this means that you must\n * make sure they are performant enough to avoid performance bottlenecks.\n *\n * @function ModifierFn\n * @argument {dataObject} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {dataObject} The data object, properly modified\n */\n\n/**\n * Modifiers are plugins used to alter the behavior of your poppers.<br />\n * Popper.js uses a set of 9 modifiers to provide all the basic functionalities\n * needed by the library.\n *\n * Usually you don't want to override the `order`, `fn` and `onLoad` props.\n * All the other properties are configurations that could be tweaked.\n * @namespace modifiers\n */\nvar modifiers = {\n  /**\n   * Modifier used to shift the popper on the start or end of its reference\n   * element.<br />\n   * It will read the variation of the `placement` property.<br />\n   * It can be one either `-end` or `-start`.\n   * @memberof modifiers\n   * @inner\n   */\n  shift: {\n    /** @prop {number} order=100 - Index used to define the order of execution */\n    order: 100,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: shift\n  },\n\n  /**\n   * The `offset` modifier can shift your popper on both its axis.\n   *\n   * It accepts the following units:\n   * - `px` or unitless, interpreted as pixels\n   * - `%` or `%r`, percentage relative to the length of the reference element\n   * - `%p`, percentage relative to the length of the popper element\n   * - `vw`, CSS viewport width unit\n   * - `vh`, CSS viewport height unit\n   *\n   * For length is intended the main axis relative to the placement of the popper.<br />\n   * This means that if the placement is `top` or `bottom`, the length will be the\n   * `width`. In case of `left` or `right`, it will be the height.\n   *\n   * You can provide a single value (as `Number` or `String`), or a pair of values\n   * as `String` divided by a comma or one (or more) white spaces.<br />\n   * The latter is a deprecated method because it leads to confusion and will be\n   * removed in v2.<br />\n   * Additionally, it accepts additions and subtractions between different units.\n   * Note that multiplications and divisions aren't supported.\n   *\n   * Valid examples are:\n   * ```\n   * 10\n   * '10%'\n   * '10, 10'\n   * '10%, 10'\n   * '10 + 10%'\n   * '10 - 5vh + 3%'\n   * '-10px + 5vh, 5px - 6%'\n   * ```\n   * > **NB**: If you desire to apply offsets to your poppers in a way that may make them overlap\n   * > with their reference element, unfortunately, you will have to disable the `flip` modifier.\n   * > More on this [reading this issue](https://github.com/FezVrasta/popper.js/issues/373)\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  offset: {\n    /** @prop {number} order=200 - Index used to define the order of execution */\n    order: 200,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: offset,\n    /** @prop {Number|String} offset=0\n     * The offset value as described in the modifier description\n     */\n    offset: 0\n  },\n\n  /**\n   * Modifier used to prevent the popper from being positioned outside the boundary.\n   *\n   * An scenario exists where the reference itself is not within the boundaries.<br />\n   * We can say it has \"escaped the boundaries\" — or just \"escaped\".<br />\n   * In this case we need to decide whether the popper should either:\n   *\n   * - detach from the reference and remain \"trapped\" in the boundaries, or\n   * - if it should ignore the boundary and \"escape with its reference\"\n   *\n   * When `escapeWithReference` is set to`true` and reference is completely\n   * outside its boundaries, the popper will overflow (or completely leave)\n   * the boundaries in order to remain attached to the edge of the reference.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  preventOverflow: {\n    /** @prop {number} order=300 - Index used to define the order of execution */\n    order: 300,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: preventOverflow,\n    /**\n     * @prop {Array} [priority=['left','right','top','bottom']]\n     * Popper will try to prevent overflow following these priorities by default,\n     * then, it could overflow on the left and on top of the `boundariesElement`\n     */\n    priority: ['left', 'right', 'top', 'bottom'],\n    /**\n     * @prop {number} padding=5\n     * Amount of pixel used to define a minimum distance between the boundaries\n     * and the popper this makes sure the popper has always a little padding\n     * between the edges of its container\n     */\n    padding: 5,\n    /**\n     * @prop {String|HTMLElement} boundariesElement='scrollParent'\n     * Boundaries used by the modifier, can be `scrollParent`, `window`,\n     * `viewport` or any DOM element.\n     */\n    boundariesElement: 'scrollParent'\n  },\n\n  /**\n   * Modifier used to make sure the reference and its popper stay near eachothers\n   * without leaving any gap between the two. Expecially useful when the arrow is\n   * enabled and you want to assure it to point to its reference element.\n   * It cares only about the first axis, you can still have poppers with margin\n   * between the popper and its reference element.\n   * @memberof modifiers\n   * @inner\n   */\n  keepTogether: {\n    /** @prop {number} order=400 - Index used to define the order of execution */\n    order: 400,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: keepTogether\n  },\n\n  /**\n   * This modifier is used to move the `arrowElement` of the popper to make\n   * sure it is positioned between the reference element and its popper element.\n   * It will read the outer size of the `arrowElement` node to detect how many\n   * pixels of conjuction are needed.\n   *\n   * It has no effect if no `arrowElement` is provided.\n   * @memberof modifiers\n   * @inner\n   */\n  arrow: {\n    /** @prop {number} order=500 - Index used to define the order of execution */\n    order: 500,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: arrow,\n    /** @prop {String|HTMLElement} element='[x-arrow]' - Selector or node used as arrow */\n    element: '[x-arrow]'\n  },\n\n  /**\n   * Modifier used to flip the popper's placement when it starts to overlap its\n   * reference element.\n   *\n   * Requires the `preventOverflow` modifier before it in order to work.\n   *\n   * **NOTE:** this modifier will interrupt the current update cycle and will\n   * restart it if it detects the need to flip the placement.\n   * @memberof modifiers\n   * @inner\n   */\n  flip: {\n    /** @prop {number} order=600 - Index used to define the order of execution */\n    order: 600,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: flip,\n    /**\n     * @prop {String|Array} behavior='flip'\n     * The behavior used to change the popper's placement. It can be one of\n     * `flip`, `clockwise`, `counterclockwise` or an array with a list of valid\n     * placements (with optional variations).\n     */\n    behavior: 'flip',\n    /**\n     * @prop {number} padding=5\n     * The popper will flip if it hits the edges of the `boundariesElement`\n     */\n    padding: 5,\n    /**\n     * @prop {String|HTMLElement} boundariesElement='viewport'\n     * The element which will define the boundaries of the popper position,\n     * the popper will never be placed outside of the defined boundaries\n     * (except if keepTogether is enabled)\n     */\n    boundariesElement: 'viewport'\n  },\n\n  /**\n   * Modifier used to make the popper flow toward the inner of the reference element.\n   * By default, when this modifier is disabled, the popper will be placed outside\n   * the reference element.\n   * @memberof modifiers\n   * @inner\n   */\n  inner: {\n    /** @prop {number} order=700 - Index used to define the order of execution */\n    order: 700,\n    /** @prop {Boolean} enabled=false - Whether the modifier is enabled or not */\n    enabled: false,\n    /** @prop {ModifierFn} */\n    fn: inner\n  },\n\n  /**\n   * Modifier used to hide the popper when its reference element is outside of the\n   * popper boundaries. It will set a `x-out-of-boundaries` attribute which can\n   * be used to hide with a CSS selector the popper when its reference is\n   * out of boundaries.\n   *\n   * Requires the `preventOverflow` modifier before it in order to work.\n   * @memberof modifiers\n   * @inner\n   */\n  hide: {\n    /** @prop {number} order=800 - Index used to define the order of execution */\n    order: 800,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: hide\n  },\n\n  /**\n   * Computes the style that will be applied to the popper element to gets\n   * properly positioned.\n   *\n   * Note that this modifier will not touch the DOM, it just prepares the styles\n   * so that `applyStyle` modifier can apply it. This separation is useful\n   * in case you need to replace `applyStyle` with a custom implementation.\n   *\n   * This modifier has `850` as `order` value to maintain backward compatibility\n   * with previous versions of Popper.js. Expect the modifiers ordering method\n   * to change in future major versions of the library.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  computeStyle: {\n    /** @prop {number} order=850 - Index used to define the order of execution */\n    order: 850,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: computeStyle,\n    /**\n     * @prop {Boolean} gpuAcceleration=true\n     * If true, it uses the CSS 3d transformation to position the popper.\n     * Otherwise, it will use the `top` and `left` properties.\n     */\n    gpuAcceleration: true,\n    /**\n     * @prop {string} [x='bottom']\n     * Where to anchor the X axis (`bottom` or `top`). AKA X offset origin.\n     * Change this if your popper should grow in a direction different from `bottom`\n     */\n    x: 'bottom',\n    /**\n     * @prop {string} [x='left']\n     * Where to anchor the Y axis (`left` or `right`). AKA Y offset origin.\n     * Change this if your popper should grow in a direction different from `right`\n     */\n    y: 'right'\n  },\n\n  /**\n   * Applies the computed styles to the popper element.\n   *\n   * All the DOM manipulations are limited to this modifier. This is useful in case\n   * you want to integrate Popper.js inside a framework or view library and you\n   * want to delegate all the DOM manipulations to it.\n   *\n   * Note that if you disable this modifier, you must make sure the popper element\n   * has its position set to `absolute` before Popper.js can do its work!\n   *\n   * Just disable this modifier and define you own to achieve the desired effect.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  applyStyle: {\n    /** @prop {number} order=900 - Index used to define the order of execution */\n    order: 900,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: applyStyle,\n    /** @prop {Function} */\n    onLoad: applyStyleOnLoad,\n    /**\n     * @deprecated since version 1.10.0, the property moved to `computeStyle` modifier\n     * @prop {Boolean} gpuAcceleration=true\n     * If true, it uses the CSS 3d transformation to position the popper.\n     * Otherwise, it will use the `top` and `left` properties.\n     */\n    gpuAcceleration: undefined\n  }\n};\n\n/**\n * The `dataObject` is an object containing all the informations used by Popper.js\n * this object get passed to modifiers and to the `onCreate` and `onUpdate` callbacks.\n * @name dataObject\n * @property {Object} data.instance The Popper.js instance\n * @property {String} data.placement Placement applied to popper\n * @property {String} data.originalPlacement Placement originally defined on init\n * @property {Boolean} data.flipped True if popper has been flipped by flip modifier\n * @property {Boolean} data.hide True if the reference element is out of boundaries, useful to know when to hide the popper.\n * @property {HTMLElement} data.arrowElement Node used as arrow by arrow modifier\n * @property {Object} data.styles Any CSS property defined here will be applied to the popper, it expects the JavaScript nomenclature (eg. `marginBottom`)\n * @property {Object} data.arrowStyles Any CSS property defined here will be applied to the popper arrow, it expects the JavaScript nomenclature (eg. `marginBottom`)\n * @property {Object} data.boundaries Offsets of the popper boundaries\n * @property {Object} data.offsets The measurements of popper, reference and arrow elements.\n * @property {Object} data.offsets.popper `top`, `left`, `width`, `height` values\n * @property {Object} data.offsets.reference `top`, `left`, `width`, `height` values\n * @property {Object} data.offsets.arrow] `top` and `left` offsets, only one of them will be different from 0\n */\n\n/**\n * Default options provided to Popper.js constructor.<br />\n * These can be overriden using the `options` argument of Popper.js.<br />\n * To override an option, simply pass as 3rd argument an object with the same\n * structure of this object, example:\n * ```\n * new Popper(ref, pop, {\n *   modifiers: {\n *     preventOverflow: { enabled: false }\n *   }\n * })\n * ```\n * @type {Object}\n * @static\n * @memberof Popper\n */\nvar Defaults = {\n  /**\n   * Popper's placement\n   * @prop {Popper.placements} placement='bottom'\n   */\n  placement: 'bottom',\n\n  /**\n   * Whether events (resize, scroll) are initially enabled\n   * @prop {Boolean} eventsEnabled=true\n   */\n  eventsEnabled: true,\n\n  /**\n   * Set to true if you want to automatically remove the popper when\n   * you call the `destroy` method.\n   * @prop {Boolean} removeOnDestroy=false\n   */\n  removeOnDestroy: false,\n\n  /**\n   * Callback called when the popper is created.<br />\n   * By default, is set to no-op.<br />\n   * Access Popper.js instance with `data.instance`.\n   * @prop {onCreate}\n   */\n  onCreate: function onCreate() {},\n\n  /**\n   * Callback called when the popper is updated, this callback is not called\n   * on the initialization/creation of the popper, but only on subsequent\n   * updates.<br />\n   * By default, is set to no-op.<br />\n   * Access Popper.js instance with `data.instance`.\n   * @prop {onUpdate}\n   */\n  onUpdate: function onUpdate() {},\n\n  /**\n   * List of modifiers used to modify the offsets before they are applied to the popper.\n   * They provide most of the functionalities of Popper.js\n   * @prop {modifiers}\n   */\n  modifiers: modifiers\n};\n\n/**\n * @callback onCreate\n * @param {dataObject} data\n */\n\n/**\n * @callback onUpdate\n * @param {dataObject} data\n */\n\n// Utils\n// Methods\nvar Popper = function () {\n  /**\n   * Create a new Popper.js instance\n   * @class Popper\n   * @param {HTMLElement|referenceObject} reference - The reference element used to position the popper\n   * @param {HTMLElement} popper - The HTML element used as popper.\n   * @param {Object} options - Your custom options to override the ones defined in [Defaults](#defaults)\n   * @return {Object} instance - The generated Popper.js instance\n   */\n  function Popper(reference, popper) {\n    var _this = this;\n\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    classCallCheck(this, Popper);\n\n    this.scheduleUpdate = function () {\n      return requestAnimationFrame(_this.update);\n    };\n\n    // make update() debounced, so that it only runs at most once-per-tick\n    this.update = debounce(this.update.bind(this));\n\n    // with {} we create a new object with the options inside it\n    this.options = _extends({}, Popper.Defaults, options);\n\n    // init state\n    this.state = {\n      isDestroyed: false,\n      isCreated: false,\n      scrollParents: []\n    };\n\n    // get reference and popper elements (allow jQuery wrappers)\n    this.reference = reference.jquery ? reference[0] : reference;\n    this.popper = popper.jquery ? popper[0] : popper;\n\n    // Deep merge modifiers options\n    this.options.modifiers = {};\n    Object.keys(_extends({}, Popper.Defaults.modifiers, options.modifiers)).forEach(function (name) {\n      _this.options.modifiers[name] = _extends({}, Popper.Defaults.modifiers[name] || {}, options.modifiers ? options.modifiers[name] : {});\n    });\n\n    // Refactoring modifiers' list (Object => Array)\n    this.modifiers = Object.keys(this.options.modifiers).map(function (name) {\n      return _extends({\n        name: name\n      }, _this.options.modifiers[name]);\n    })\n    // sort the modifiers by order\n    .sort(function (a, b) {\n      return a.order - b.order;\n    });\n\n    // modifiers have the ability to execute arbitrary code when Popper.js get inited\n    // such code is executed in the same order of its modifier\n    // they could add new properties to their options configuration\n    // BE AWARE: don't add options to `options.modifiers.name` but to `modifierOptions`!\n    this.modifiers.forEach(function (modifierOptions) {\n      if (modifierOptions.enabled && isFunction(modifierOptions.onLoad)) {\n        modifierOptions.onLoad(_this.reference, _this.popper, _this.options, modifierOptions, _this.state);\n      }\n    });\n\n    // fire the first update to position the popper in the right place\n    this.update();\n\n    var eventsEnabled = this.options.eventsEnabled;\n    if (eventsEnabled) {\n      // setup event listeners, they will take care of update the position in specific situations\n      this.enableEventListeners();\n    }\n\n    this.state.eventsEnabled = eventsEnabled;\n  }\n\n  // We can't use class properties because they don't get listed in the\n  // class prototype and break stuff like Sinon stubs\n\n\n  createClass(Popper, [{\n    key: 'update',\n    value: function update$$1() {\n      return update.call(this);\n    }\n  }, {\n    key: 'destroy',\n    value: function destroy$$1() {\n      return destroy.call(this);\n    }\n  }, {\n    key: 'enableEventListeners',\n    value: function enableEventListeners$$1() {\n      return enableEventListeners.call(this);\n    }\n  }, {\n    key: 'disableEventListeners',\n    value: function disableEventListeners$$1() {\n      return disableEventListeners.call(this);\n    }\n\n    /**\n     * Schedule an update, it will run on the next UI update available\n     * @method scheduleUpdate\n     * @memberof Popper\n     */\n\n\n    /**\n     * Collection of utilities useful when writing custom modifiers.\n     * Starting from version 1.7, this method is available only if you\n     * include `popper-utils.js` before `popper.js`.\n     *\n     * **DEPRECATION**: This way to access PopperUtils is deprecated\n     * and will be removed in v2! Use the PopperUtils module directly instead.\n     * Due to the high instability of the methods contained in Utils, we can't\n     * guarantee them to follow semver. Use them at your own risk!\n     * @static\n     * @private\n     * @type {Object}\n     * @deprecated since version 1.8\n     * @member Utils\n     * @memberof Popper\n     */\n\n  }]);\n  return Popper;\n}();\n\n/**\n * The `referenceObject` is an object that provides an interface compatible with Popper.js\n * and lets you use it as replacement of a real DOM node.<br />\n * You can use this method to position a popper relatively to a set of coordinates\n * in case you don't have a DOM node to use as reference.\n *\n * ```\n * new Popper(referenceObject, popperNode);\n * ```\n *\n * NB: This feature isn't supported in Internet Explorer 10\n * @name referenceObject\n * @property {Function} data.getBoundingClientRect\n * A function that returns a set of coordinates compatible with the native `getBoundingClientRect` method.\n * @property {number} data.clientWidth\n * An ES6 getter that will return the width of the virtual reference element.\n * @property {number} data.clientHeight\n * An ES6 getter that will return the height of the virtual reference element.\n */\n\n\nPopper.Utils = (typeof window !== 'undefined' ? window : global).PopperUtils;\nPopper.placements = placements;\nPopper.Defaults = Defaults;\n\nexport default Popper;\n//# sourceMappingURL=popper.js.map\n", "import $ from 'jquery'\nimport Popper from 'popper.js'\nimport Util from './util'\n\n\n/**\n * --------------------------------------------------------------------------\n * <PERSON><PERSON>p (v4.0.0-beta.2): dropdown.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst Dropdown = (() => {\n\n  /**\n   * Check for Popper dependency\n   * Popper - https://popper.js.org\n   */\n  if (typeof Popper === 'undefined') {\n    throw new Error('Bootstrap dropdown require Popper.js (https://popper.js.org)')\n  }\n\n  /**\n   * ------------------------------------------------------------------------\n   * Constants\n   * ------------------------------------------------------------------------\n   */\n\n  const NAME                     = 'dropdown'\n  const VERSION                  = '4.0.0-beta.2'\n  const DATA_KEY                 = 'bs.dropdown'\n  const EVENT_KEY                = `.${DATA_KEY}`\n  const DATA_API_KEY             = '.data-api'\n  const JQUERY_NO_CONFLICT       = $.fn[NAME]\n  const ESCAPE_KEYCODE           = 27 // KeyboardEvent.which value for Escape (Esc) key\n  const SPACE_KEYCODE            = 32 // KeyboardEvent.which value for space key\n  const TAB_KEYCODE              = 9 // KeyboardEvent.which value for tab key\n  const ARROW_UP_KEYCODE         = 38 // KeyboardEvent.which value for up arrow key\n  const ARROW_DOWN_KEYCODE       = 40 // KeyboardEvent.which value for down arrow key\n  const RIGHT_MOUSE_BUTTON_WHICH = 3 // MouseEvent.which value for the right button (assuming a right-handed mouse)\n  const REGEXP_KEYDOWN           = new RegExp(`${ARROW_UP_KEYCODE}|${ARROW_DOWN_KEYCODE}|${ESCAPE_KEYCODE}`)\n\n  const Event = {\n    HIDE             : `hide${EVENT_KEY}`,\n    HIDDEN           : `hidden${EVENT_KEY}`,\n    SHOW             : `show${EVENT_KEY}`,\n    SHOWN            : `shown${EVENT_KEY}`,\n    CLICK            : `click${EVENT_KEY}`,\n    CLICK_DATA_API   : `click${EVENT_KEY}${DATA_API_KEY}`,\n    KEYDOWN_DATA_API : `keydown${EVENT_KEY}${DATA_API_KEY}`,\n    KEYUP_DATA_API   : `keyup${EVENT_KEY}${DATA_API_KEY}`\n  }\n\n  const ClassName = {\n    DISABLED  : 'disabled',\n    SHOW      : 'show',\n    DROPUP    : 'dropup',\n    MENURIGHT : 'dropdown-menu-right',\n    MENULEFT  : 'dropdown-menu-left'\n  }\n\n  const Selector = {\n    DATA_TOGGLE   : '[data-toggle=\"dropdown\"]',\n    FORM_CHILD    : '.dropdown form',\n    MENU          : '.dropdown-menu',\n    NAVBAR_NAV    : '.navbar-nav',\n    VISIBLE_ITEMS : '.dropdown-menu .dropdown-item:not(.disabled)'\n  }\n\n  const AttachmentMap = {\n    TOP       : 'top-start',\n    TOPEND    : 'top-end',\n    BOTTOM    : 'bottom-start',\n    BOTTOMEND : 'bottom-end'\n  }\n\n  const Default = {\n    offset      : 0,\n    flip        : true\n  }\n\n  const DefaultType = {\n    offset      : '(number|string|function)',\n    flip        : 'boolean'\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Class Definition\n   * ------------------------------------------------------------------------\n   */\n\n  class Dropdown {\n\n    constructor(element, config) {\n      this._element  = element\n      this._popper   = null\n      this._config   = this._getConfig(config)\n      this._menu     = this._getMenuElement()\n      this._inNavbar = this._detectNavbar()\n\n      this._addEventListeners()\n    }\n\n\n    // getters\n\n    static get VERSION() {\n      return VERSION\n    }\n\n    static get Default() {\n      return Default\n    }\n\n    static get DefaultType() {\n      return DefaultType\n    }\n\n    // public\n\n    toggle() {\n      if (this._element.disabled || $(this._element).hasClass(ClassName.DISABLED)) {\n        return\n      }\n\n      const parent   = Dropdown._getParentFromElement(this._element)\n      const isActive = $(this._menu).hasClass(ClassName.SHOW)\n\n      Dropdown._clearMenus()\n\n      if (isActive) {\n        return\n      }\n\n      const relatedTarget = {\n        relatedTarget : this._element\n      }\n      const showEvent = $.Event(Event.SHOW, relatedTarget)\n\n      $(parent).trigger(showEvent)\n\n      if (showEvent.isDefaultPrevented()) {\n        return\n      }\n\n      let element = this._element\n      // for dropup with alignment we use the parent as popper container\n      if ($(parent).hasClass(ClassName.DROPUP)) {\n        if ($(this._menu).hasClass(ClassName.MENULEFT) || $(this._menu).hasClass(ClassName.MENURIGHT)) {\n          element = parent\n        }\n      }\n      this._popper = new Popper(element, this._menu, this._getPopperConfig())\n\n      // if this is a touch-enabled device we add extra\n      // empty mouseover listeners to the body's immediate children;\n      // only needed because of broken event delegation on iOS\n      // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n      if ('ontouchstart' in document.documentElement &&\n         !$(parent).closest(Selector.NAVBAR_NAV).length) {\n        $('body').children().on('mouseover', null, $.noop)\n      }\n\n      this._element.focus()\n      this._element.setAttribute('aria-expanded', true)\n\n      $(this._menu).toggleClass(ClassName.SHOW)\n      $(parent)\n        .toggleClass(ClassName.SHOW)\n        .trigger($.Event(Event.SHOWN, relatedTarget))\n    }\n\n    dispose() {\n      $.removeData(this._element, DATA_KEY)\n      $(this._element).off(EVENT_KEY)\n      this._element = null\n      this._menu = null\n      if (this._popper !== null) {\n        this._popper.destroy()\n      }\n      this._popper = null\n    }\n\n    update() {\n      this._inNavbar = this._detectNavbar()\n      if (this._popper !== null) {\n        this._popper.scheduleUpdate()\n      }\n    }\n\n    // private\n\n    _addEventListeners() {\n      $(this._element).on(Event.CLICK, (event) => {\n        event.preventDefault()\n        event.stopPropagation()\n        this.toggle()\n      })\n    }\n\n    _getConfig(config) {\n      config = $.extend(\n        {},\n        this.constructor.Default,\n        $(this._element).data(),\n        config\n      )\n\n      Util.typeCheckConfig(\n        NAME,\n        config,\n        this.constructor.DefaultType\n      )\n\n      return config\n    }\n\n    _getMenuElement() {\n      if (!this._menu) {\n        const parent = Dropdown._getParentFromElement(this._element)\n        this._menu = $(parent).find(Selector.MENU)[0]\n      }\n      return this._menu\n    }\n\n    _getPlacement() {\n      const $parentDropdown = $(this._element).parent()\n      let placement = AttachmentMap.BOTTOM\n\n      // Handle dropup\n      if ($parentDropdown.hasClass(ClassName.DROPUP)) {\n        placement = AttachmentMap.TOP\n        if ($(this._menu).hasClass(ClassName.MENURIGHT)) {\n          placement = AttachmentMap.TOPEND\n        }\n      } else if ($(this._menu).hasClass(ClassName.MENURIGHT)) {\n        placement = AttachmentMap.BOTTOMEND\n      }\n      return placement\n    }\n\n    _detectNavbar() {\n      return $(this._element).closest('.navbar').length > 0\n    }\n\n    _getPopperConfig() {\n      const offsetConf = {}\n      if (typeof this._config.offset === 'function') {\n        offsetConf.fn = (data) => {\n          data.offsets = $.extend({}, data.offsets, this._config.offset(data.offsets) || {})\n          return data\n        }\n      } else {\n        offsetConf.offset = this._config.offset\n      }\n      const popperConfig = {\n        placement : this._getPlacement(),\n        modifiers : {\n          offset : offsetConf,\n          flip : {\n            enabled : this._config.flip\n          }\n        }\n      }\n\n      // Disable Popper.js for Dropdown in Navbar\n      if (this._inNavbar) {\n        popperConfig.modifiers.applyStyle = {\n          enabled: !this._inNavbar\n        }\n      }\n      return popperConfig\n    }\n\n    // static\n\n    static _jQueryInterface(config) {\n      return this.each(function () {\n        let data = $(this).data(DATA_KEY)\n        const _config = typeof config === 'object' ? config : null\n\n        if (!data) {\n          data = new Dropdown(this, _config)\n          $(this).data(DATA_KEY, data)\n        }\n\n        if (typeof config === 'string') {\n          if (typeof data[config] === 'undefined') {\n            throw new Error(`No method named \"${config}\"`)\n          }\n          data[config]()\n        }\n      })\n    }\n\n    static _clearMenus(event) {\n      if (event && (event.which === RIGHT_MOUSE_BUTTON_WHICH ||\n        event.type === 'keyup' && event.which !== TAB_KEYCODE)) {\n        return\n      }\n\n      const toggles = $.makeArray($(Selector.DATA_TOGGLE))\n      for (let i = 0; i < toggles.length; i++) {\n        const parent        = Dropdown._getParentFromElement(toggles[i])\n        const context       = $(toggles[i]).data(DATA_KEY)\n        const relatedTarget = {\n          relatedTarget : toggles[i]\n        }\n\n        if (!context) {\n          continue\n        }\n\n        const dropdownMenu = context._menu\n        if (!$(parent).hasClass(ClassName.SHOW)) {\n          continue\n        }\n\n        if (event && (event.type === 'click' &&\n            /input|textarea/i.test(event.target.tagName) || event.type === 'keyup' && event.which === TAB_KEYCODE)\n            && $.contains(parent, event.target)) {\n          continue\n        }\n\n        const hideEvent = $.Event(Event.HIDE, relatedTarget)\n        $(parent).trigger(hideEvent)\n        if (hideEvent.isDefaultPrevented()) {\n          continue\n        }\n\n        // if this is a touch-enabled device we remove the extra\n        // empty mouseover listeners we added for iOS support\n        if ('ontouchstart' in document.documentElement) {\n          $('body').children().off('mouseover', null, $.noop)\n        }\n\n        toggles[i].setAttribute('aria-expanded', 'false')\n\n        $(dropdownMenu).removeClass(ClassName.SHOW)\n        $(parent)\n          .removeClass(ClassName.SHOW)\n          .trigger($.Event(Event.HIDDEN, relatedTarget))\n      }\n    }\n\n    static _getParentFromElement(element) {\n      let parent\n      const selector = Util.getSelectorFromElement(element)\n\n      if (selector) {\n        parent = $(selector)[0]\n      }\n\n      return parent || element.parentNode\n    }\n\n    static _dataApiKeydownHandler(event) {\n      if (!REGEXP_KEYDOWN.test(event.which) || /button/i.test(event.target.tagName) && event.which === SPACE_KEYCODE ||\n         /input|textarea/i.test(event.target.tagName)) {\n        return\n      }\n\n      event.preventDefault()\n      event.stopPropagation()\n\n      if (this.disabled || $(this).hasClass(ClassName.DISABLED)) {\n        return\n      }\n\n      const parent   = Dropdown._getParentFromElement(this)\n      const isActive = $(parent).hasClass(ClassName.SHOW)\n\n      if (!isActive && (event.which !== ESCAPE_KEYCODE || event.which !== SPACE_KEYCODE) ||\n           isActive && (event.which === ESCAPE_KEYCODE || event.which === SPACE_KEYCODE)) {\n\n        if (event.which === ESCAPE_KEYCODE) {\n          const toggle = $(parent).find(Selector.DATA_TOGGLE)[0]\n          $(toggle).trigger('focus')\n        }\n\n        $(this).trigger('click')\n        return\n      }\n\n      const items = $(parent).find(Selector.VISIBLE_ITEMS).get()\n\n      if (!items.length) {\n        return\n      }\n\n      let index = items.indexOf(event.target)\n\n      if (event.which === ARROW_UP_KEYCODE && index > 0) { // up\n        index--\n      }\n\n      if (event.which === ARROW_DOWN_KEYCODE && index < items.length - 1) { // down\n        index++\n      }\n\n      if (index < 0) {\n        index = 0\n      }\n\n      items[index].focus()\n    }\n\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Data Api implementation\n   * ------------------------------------------------------------------------\n   */\n\n  $(document)\n    .on(Event.KEYDOWN_DATA_API, Selector.DATA_TOGGLE,  Dropdown._dataApiKeydownHandler)\n    .on(Event.KEYDOWN_DATA_API, Selector.MENU, Dropdown._dataApiKeydownHandler)\n    .on(`${Event.CLICK_DATA_API} ${Event.KEYUP_DATA_API}`, Dropdown._clearMenus)\n    .on(Event.CLICK_DATA_API, Selector.DATA_TOGGLE, function (event) {\n      event.preventDefault()\n      event.stopPropagation()\n      Dropdown._jQueryInterface.call($(this), 'toggle')\n    })\n    .on(Event.CLICK_DATA_API, Selector.FORM_CHILD, (e) => {\n      e.stopPropagation()\n    })\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * jQuery\n   * ------------------------------------------------------------------------\n   */\n\n  $.fn[NAME]             = Dropdown._jQueryInterface\n  $.fn[NAME].Constructor = Dropdown\n  $.fn[NAME].noConflict  = function () {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Dropdown._jQueryInterface\n  }\n\n  return Dropdown\n\n})($, Popper)\n\nexport default Dropdown\n", "import $ from 'jquery'\nimport Util from './util'\n\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.0.0-beta.2): modal.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst Modal = (() => {\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Constants\n   * ------------------------------------------------------------------------\n   */\n\n  const NAME                         = 'modal'\n  const VERSION                      = '4.0.0-beta.2'\n  const DATA_KEY                     = 'bs.modal'\n  const EVENT_KEY                    = `.${DATA_KEY}`\n  const DATA_API_KEY                 = '.data-api'\n  const JQUERY_NO_CONFLICT           = $.fn[NAME]\n  const TRANSITION_DURATION          = 300\n  const BACKDROP_TRANSITION_DURATION = 150\n  const ESCAPE_KEYCODE               = 27 // KeyboardEvent.which value for Escape (Esc) key\n\n  const Default = {\n    backdrop : true,\n    keyboard : true,\n    focus    : true,\n    show     : true\n  }\n\n  const DefaultType = {\n    backdrop : '(boolean|string)',\n    keyboard : 'boolean',\n    focus    : 'boolean',\n    show     : 'boolean'\n  }\n\n  const Event = {\n    HIDE              : `hide${EVENT_KEY}`,\n    HIDDEN            : `hidden${EVENT_KEY}`,\n    SHOW              : `show${EVENT_KEY}`,\n    SHOWN             : `shown${EVENT_KEY}`,\n    FOCUSIN           : `focusin${EVENT_KEY}`,\n    RESIZE            : `resize${EVENT_KEY}`,\n    CLICK_DISMISS     : `click.dismiss${EVENT_KEY}`,\n    KEYDOWN_DISMISS   : `keydown.dismiss${EVENT_KEY}`,\n    MOUSEUP_DISMISS   : `mouseup.dismiss${EVENT_KEY}`,\n    MOUSEDOWN_DISMISS : `mousedown.dismiss${EVENT_KEY}`,\n    CLICK_DATA_API    : `click${EVENT_KEY}${DATA_API_KEY}`\n  }\n\n  const ClassName = {\n    SCROLLBAR_MEASURER : 'modal-scrollbar-measure',\n    BACKDROP           : 'modal-backdrop',\n    OPEN               : 'modal-open',\n    FADE               : 'fade',\n    SHOW               : 'show'\n  }\n\n  const Selector = {\n    DIALOG             : '.modal-dialog',\n    DATA_TOGGLE        : '[data-toggle=\"modal\"]',\n    DATA_DISMISS       : '[data-dismiss=\"modal\"]',\n    FIXED_CONTENT      : '.fixed-top, .fixed-bottom, .is-fixed, .sticky-top',\n    STICKY_CONTENT     : '.sticky-top',\n    NAVBAR_TOGGLER     : '.navbar-toggler'\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Class Definition\n   * ------------------------------------------------------------------------\n   */\n\n  class Modal {\n\n    constructor(element, config) {\n      this._config              = this._getConfig(config)\n      this._element             = element\n      this._dialog              = $(element).find(Selector.DIALOG)[0]\n      this._backdrop            = null\n      this._isShown             = false\n      this._isBodyOverflowing   = false\n      this._ignoreBackdropClick = false\n      this._originalBodyPadding = 0\n      this._scrollbarWidth      = 0\n    }\n\n\n    // getters\n\n    static get VERSION() {\n      return VERSION\n    }\n\n    static get Default() {\n      return Default\n    }\n\n\n    // public\n\n    toggle(relatedTarget) {\n      return this._isShown ? this.hide() : this.show(relatedTarget)\n    }\n\n    show(relatedTarget) {\n      if (this._isTransitioning || this._isShown) {\n        return\n      }\n\n      if (Util.supportsTransitionEnd() && $(this._element).hasClass(ClassName.FADE)) {\n        this._isTransitioning = true\n      }\n\n      const showEvent = $.Event(Event.SHOW, {\n        relatedTarget\n      })\n\n      $(this._element).trigger(showEvent)\n\n      if (this._isShown || showEvent.isDefaultPrevented()) {\n        return\n      }\n\n      this._isShown = true\n\n      this._checkScrollbar()\n      this._setScrollbar()\n\n      this._adjustDialog()\n\n      $(document.body).addClass(ClassName.OPEN)\n\n      this._setEscapeEvent()\n      this._setResizeEvent()\n\n      $(this._element).on(\n        Event.CLICK_DISMISS,\n        Selector.DATA_DISMISS,\n        (event) => this.hide(event)\n      )\n\n      $(this._dialog).on(Event.MOUSEDOWN_DISMISS, () => {\n        $(this._element).one(Event.MOUSEUP_DISMISS, (event) => {\n          if ($(event.target).is(this._element)) {\n            this._ignoreBackdropClick = true\n          }\n        })\n      })\n\n      this._showBackdrop(() => this._showElement(relatedTarget))\n    }\n\n    hide(event) {\n      if (event) {\n        event.preventDefault()\n      }\n\n      if (this._isTransitioning || !this._isShown) {\n        return\n      }\n\n      const hideEvent = $.Event(Event.HIDE)\n\n      $(this._element).trigger(hideEvent)\n\n      if (!this._isShown || hideEvent.isDefaultPrevented()) {\n        return\n      }\n\n      this._isShown = false\n\n      const transition = Util.supportsTransitionEnd() && $(this._element).hasClass(ClassName.FADE)\n\n      if (transition) {\n        this._isTransitioning = true\n      }\n\n      this._setEscapeEvent()\n      this._setResizeEvent()\n\n      $(document).off(Event.FOCUSIN)\n\n      $(this._element).removeClass(ClassName.SHOW)\n\n      $(this._element).off(Event.CLICK_DISMISS)\n      $(this._dialog).off(Event.MOUSEDOWN_DISMISS)\n\n      if (transition) {\n\n        $(this._element)\n          .one(Util.TRANSITION_END, (event) => this._hideModal(event))\n          .emulateTransitionEnd(TRANSITION_DURATION)\n      } else {\n        this._hideModal()\n      }\n    }\n\n    dispose() {\n      $.removeData(this._element, DATA_KEY)\n\n      $(window, document, this._element, this._backdrop).off(EVENT_KEY)\n\n      this._config              = null\n      this._element             = null\n      this._dialog              = null\n      this._backdrop            = null\n      this._isShown             = null\n      this._isBodyOverflowing   = null\n      this._ignoreBackdropClick = null\n      this._scrollbarWidth      = null\n    }\n\n    handleUpdate() {\n      this._adjustDialog()\n    }\n\n    // private\n\n    _getConfig(config) {\n      config = $.extend({}, Default, config)\n      Util.typeCheckConfig(NAME, config, DefaultType)\n      return config\n    }\n\n    _showElement(relatedTarget) {\n      const transition = Util.supportsTransitionEnd() &&\n        $(this._element).hasClass(ClassName.FADE)\n\n      if (!this._element.parentNode ||\n         this._element.parentNode.nodeType !== Node.ELEMENT_NODE) {\n        // don't move modals dom position\n        document.body.appendChild(this._element)\n      }\n\n      this._element.style.display = 'block'\n      this._element.removeAttribute('aria-hidden')\n      this._element.scrollTop = 0\n\n      if (transition) {\n        Util.reflow(this._element)\n      }\n\n      $(this._element).addClass(ClassName.SHOW)\n\n      if (this._config.focus) {\n        this._enforceFocus()\n      }\n\n      const shownEvent = $.Event(Event.SHOWN, {\n        relatedTarget\n      })\n\n      const transitionComplete = () => {\n        if (this._config.focus) {\n          this._element.focus()\n        }\n        this._isTransitioning = false\n        $(this._element).trigger(shownEvent)\n      }\n\n      if (transition) {\n        $(this._dialog)\n          .one(Util.TRANSITION_END, transitionComplete)\n          .emulateTransitionEnd(TRANSITION_DURATION)\n      } else {\n        transitionComplete()\n      }\n    }\n\n    _enforceFocus() {\n      $(document)\n        .off(Event.FOCUSIN) // guard against infinite focus loop\n        .on(Event.FOCUSIN, (event) => {\n          if (document !== event.target &&\n              this._element !== event.target &&\n              !$(this._element).has(event.target).length) {\n            this._element.focus()\n          }\n        })\n    }\n\n    _setEscapeEvent() {\n      if (this._isShown && this._config.keyboard) {\n        $(this._element).on(Event.KEYDOWN_DISMISS, (event) => {\n          if (event.which === ESCAPE_KEYCODE) {\n            event.preventDefault()\n            this.hide()\n          }\n        })\n\n      } else if (!this._isShown) {\n        $(this._element).off(Event.KEYDOWN_DISMISS)\n      }\n    }\n\n    _setResizeEvent() {\n      if (this._isShown) {\n        $(window).on(Event.RESIZE, (event) => this.handleUpdate(event))\n      } else {\n        $(window).off(Event.RESIZE)\n      }\n    }\n\n    _hideModal() {\n      this._element.style.display = 'none'\n      this._element.setAttribute('aria-hidden', true)\n      this._isTransitioning = false\n      this._showBackdrop(() => {\n        $(document.body).removeClass(ClassName.OPEN)\n        this._resetAdjustments()\n        this._resetScrollbar()\n        $(this._element).trigger(Event.HIDDEN)\n      })\n    }\n\n    _removeBackdrop() {\n      if (this._backdrop) {\n        $(this._backdrop).remove()\n        this._backdrop = null\n      }\n    }\n\n    _showBackdrop(callback) {\n      const animate = $(this._element).hasClass(ClassName.FADE) ?\n        ClassName.FADE : ''\n\n      if (this._isShown && this._config.backdrop) {\n        const doAnimate = Util.supportsTransitionEnd() && animate\n\n        this._backdrop = document.createElement('div')\n        this._backdrop.className = ClassName.BACKDROP\n\n        if (animate) {\n          $(this._backdrop).addClass(animate)\n        }\n\n        $(this._backdrop).appendTo(document.body)\n\n        $(this._element).on(Event.CLICK_DISMISS, (event) => {\n          if (this._ignoreBackdropClick) {\n            this._ignoreBackdropClick = false\n            return\n          }\n          if (event.target !== event.currentTarget) {\n            return\n          }\n          if (this._config.backdrop === 'static') {\n            this._element.focus()\n          } else {\n            this.hide()\n          }\n        })\n\n        if (doAnimate) {\n          Util.reflow(this._backdrop)\n        }\n\n        $(this._backdrop).addClass(ClassName.SHOW)\n\n        if (!callback) {\n          return\n        }\n\n        if (!doAnimate) {\n          callback()\n          return\n        }\n\n        $(this._backdrop)\n          .one(Util.TRANSITION_END, callback)\n          .emulateTransitionEnd(BACKDROP_TRANSITION_DURATION)\n\n      } else if (!this._isShown && this._backdrop) {\n        $(this._backdrop).removeClass(ClassName.SHOW)\n\n        const callbackRemove = () => {\n          this._removeBackdrop()\n          if (callback) {\n            callback()\n          }\n        }\n\n        if (Util.supportsTransitionEnd() &&\n           $(this._element).hasClass(ClassName.FADE)) {\n          $(this._backdrop)\n            .one(Util.TRANSITION_END, callbackRemove)\n            .emulateTransitionEnd(BACKDROP_TRANSITION_DURATION)\n        } else {\n          callbackRemove()\n        }\n\n      } else if (callback) {\n        callback()\n      }\n    }\n\n\n    // ----------------------------------------------------------------------\n    // the following methods are used to handle overflowing modals\n    // todo (fat): these should probably be refactored out of modal.js\n    // ----------------------------------------------------------------------\n\n    _adjustDialog() {\n      const isModalOverflowing =\n        this._element.scrollHeight > document.documentElement.clientHeight\n\n      if (!this._isBodyOverflowing && isModalOverflowing) {\n        this._element.style.paddingLeft = `${this._scrollbarWidth}px`\n      }\n\n      if (this._isBodyOverflowing && !isModalOverflowing) {\n        this._element.style.paddingRight = `${this._scrollbarWidth}px`\n      }\n    }\n\n    _resetAdjustments() {\n      this._element.style.paddingLeft = ''\n      this._element.style.paddingRight = ''\n    }\n\n    _checkScrollbar() {\n      const rect = document.body.getBoundingClientRect()\n      this._isBodyOverflowing = rect.left + rect.right < window.innerWidth\n      this._scrollbarWidth = this._getScrollbarWidth()\n    }\n\n    _setScrollbar() {\n      if (this._isBodyOverflowing) {\n        // Note: DOMNode.style.paddingRight returns the actual value or '' if not set\n        //   while $(DOMNode).css('padding-right') returns the calculated value or 0 if not set\n\n        // Adjust fixed content padding\n        $(Selector.FIXED_CONTENT).each((index, element) => {\n          const actualPadding = $(element)[0].style.paddingRight\n          const calculatedPadding = $(element).css('padding-right')\n          $(element).data('padding-right', actualPadding).css('padding-right', `${parseFloat(calculatedPadding) + this._scrollbarWidth}px`)\n        })\n\n        // Adjust sticky content margin\n        $(Selector.STICKY_CONTENT).each((index, element) => {\n          const actualMargin = $(element)[0].style.marginRight\n          const calculatedMargin = $(element).css('margin-right')\n          $(element).data('margin-right', actualMargin).css('margin-right', `${parseFloat(calculatedMargin) - this._scrollbarWidth}px`)\n        })\n\n        // Adjust navbar-toggler margin\n        $(Selector.NAVBAR_TOGGLER).each((index, element) => {\n          const actualMargin = $(element)[0].style.marginRight\n          const calculatedMargin = $(element).css('margin-right')\n          $(element).data('margin-right', actualMargin).css('margin-right', `${parseFloat(calculatedMargin) + this._scrollbarWidth}px`)\n        })\n\n        // Adjust body padding\n        const actualPadding = document.body.style.paddingRight\n        const calculatedPadding = $('body').css('padding-right')\n        $('body').data('padding-right', actualPadding).css('padding-right', `${parseFloat(calculatedPadding) + this._scrollbarWidth}px`)\n      }\n    }\n\n    _resetScrollbar() {\n      // Restore fixed content padding\n      $(Selector.FIXED_CONTENT).each((index, element) => {\n        const padding = $(element).data('padding-right')\n        if (typeof padding !== 'undefined') {\n          $(element).css('padding-right', padding).removeData('padding-right')\n        }\n      })\n\n      // Restore sticky content and navbar-toggler margin\n      $(`${Selector.STICKY_CONTENT}, ${Selector.NAVBAR_TOGGLER}`).each((index, element) => {\n        const margin = $(element).data('margin-right')\n        if (typeof margin !== 'undefined') {\n          $(element).css('margin-right', margin).removeData('margin-right')\n        }\n      })\n\n      // Restore body padding\n      const padding = $('body').data('padding-right')\n      if (typeof padding !== 'undefined') {\n        $('body').css('padding-right', padding).removeData('padding-right')\n      }\n    }\n\n    _getScrollbarWidth() { // thx d.walsh\n      const scrollDiv = document.createElement('div')\n      scrollDiv.className = ClassName.SCROLLBAR_MEASURER\n      document.body.appendChild(scrollDiv)\n      const scrollbarWidth = scrollDiv.getBoundingClientRect().width - scrollDiv.clientWidth\n      document.body.removeChild(scrollDiv)\n      return scrollbarWidth\n    }\n\n\n    // static\n\n    static _jQueryInterface(config, relatedTarget) {\n      return this.each(function () {\n        let data      = $(this).data(DATA_KEY)\n        const _config = $.extend(\n          {},\n          Modal.Default,\n          $(this).data(),\n          typeof config === 'object' && config\n        )\n\n        if (!data) {\n          data = new Modal(this, _config)\n          $(this).data(DATA_KEY, data)\n        }\n\n        if (typeof config === 'string') {\n          if (typeof data[config] === 'undefined') {\n            throw new Error(`No method named \"${config}\"`)\n          }\n          data[config](relatedTarget)\n        } else if (_config.show) {\n          data.show(relatedTarget)\n        }\n      })\n    }\n\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Data Api implementation\n   * ------------------------------------------------------------------------\n   */\n\n  $(document).on(Event.CLICK_DATA_API, Selector.DATA_TOGGLE, function (event) {\n    let target\n    const selector = Util.getSelectorFromElement(this)\n\n    if (selector) {\n      target = $(selector)[0]\n    }\n\n    const config = $(target).data(DATA_KEY) ?\n      'toggle' : $.extend({}, $(target).data(), $(this).data())\n\n    if (this.tagName === 'A' || this.tagName === 'AREA') {\n      event.preventDefault()\n    }\n\n    const $target = $(target).one(Event.SHOW, (showEvent) => {\n      if (showEvent.isDefaultPrevented()) {\n        // only register focus restorer if modal will actually get shown\n        return\n      }\n\n      $target.one(Event.HIDDEN, () => {\n        if ($(this).is(':visible')) {\n          this.focus()\n        }\n      })\n    })\n\n    Modal._jQueryInterface.call($(target), config, this)\n  })\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * jQuery\n   * ------------------------------------------------------------------------\n   */\n\n  $.fn[NAME]             = Modal._jQueryInterface\n  $.fn[NAME].Constructor = Modal\n  $.fn[NAME].noConflict  = function () {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Modal._jQueryInterface\n  }\n\n  return Modal\n\n})($)\n\nexport default Modal\n", "import $ from 'jquery'\nimport Popper from 'popper.js'\nimport Util from './util'\n\n\n/**\n * --------------------------------------------------------------------------\n * <PERSON>trap (v4.0.0-beta.2): tooltip.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst Tooltip = (() => {\n\n  /**\n   * Check for Popper dependency\n   * Popper - https://popper.js.org\n   */\n  if (typeof Popper === 'undefined') {\n    throw new Error('Bootstrap tooltips require Popper.js (https://popper.js.org)')\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Constants\n   * ------------------------------------------------------------------------\n   */\n\n  const NAME                = 'tooltip'\n  const VERSION             = '4.0.0-beta.2'\n  const DATA_KEY            = 'bs.tooltip'\n  const EVENT_KEY           = `.${DATA_KEY}`\n  const JQUERY_NO_CONFLICT  = $.fn[NAME]\n  const TRANSITION_DURATION = 150\n  const CLASS_PREFIX        = 'bs-tooltip'\n  const BSCLS_PREFIX_REGEX = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\n\n  const DefaultType = {\n    animation           : 'boolean',\n    template            : 'string',\n    title               : '(string|element|function)',\n    trigger             : 'string',\n    delay               : '(number|object)',\n    html                : 'boolean',\n    selector            : '(string|boolean)',\n    placement           : '(string|function)',\n    offset              : '(number|string)',\n    container           : '(string|element|boolean)',\n    fallbackPlacement   : '(string|array)'\n  }\n\n  const AttachmentMap = {\n    AUTO   : 'auto',\n    TOP    : 'top',\n    RIGHT  : 'right',\n    BOTTOM : 'bottom',\n    LEFT   : 'left'\n  }\n\n  const Default = {\n    animation           : true,\n    template            : '<div class=\"tooltip\" role=\"tooltip\">'\n                        + '<div class=\"arrow\"></div>'\n                        + '<div class=\"tooltip-inner\"></div></div>',\n    trigger             : 'hover focus',\n    title               : '',\n    delay               : 0,\n    html                : false,\n    selector            : false,\n    placement           : 'top',\n    offset              : 0,\n    container           : false,\n    fallbackPlacement   : 'flip'\n  }\n\n  const HoverState = {\n    SHOW : 'show',\n    OUT  : 'out'\n  }\n\n  const Event = {\n    HIDE       : `hide${EVENT_KEY}`,\n    HIDDEN     : `hidden${EVENT_KEY}`,\n    SHOW       : `show${EVENT_KEY}`,\n    SHOWN      : `shown${EVENT_KEY}`,\n    INSERTED   : `inserted${EVENT_KEY}`,\n    CLICK      : `click${EVENT_KEY}`,\n    FOCUSIN    : `focusin${EVENT_KEY}`,\n    FOCUSOUT   : `focusout${EVENT_KEY}`,\n    MOUSEENTER : `mouseenter${EVENT_KEY}`,\n    MOUSELEAVE : `mouseleave${EVENT_KEY}`\n  }\n\n  const ClassName = {\n    FADE : 'fade',\n    SHOW : 'show'\n  }\n\n  const Selector = {\n    TOOLTIP       : '.tooltip',\n    TOOLTIP_INNER : '.tooltip-inner',\n    ARROW         : '.arrow'\n  }\n\n  const Trigger = {\n    HOVER  : 'hover',\n    FOCUS  : 'focus',\n    CLICK  : 'click',\n    MANUAL : 'manual'\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Class Definition\n   * ------------------------------------------------------------------------\n   */\n\n  class Tooltip {\n\n    constructor(element, config) {\n\n      // private\n      this._isEnabled     = true\n      this._timeout       = 0\n      this._hoverState    = ''\n      this._activeTrigger = {}\n      this._popper        = null\n\n      // protected\n      this.element = element\n      this.config  = this._getConfig(config)\n      this.tip     = null\n\n      this._setListeners()\n\n    }\n\n\n    // getters\n\n    static get VERSION() {\n      return VERSION\n    }\n\n    static get Default() {\n      return Default\n    }\n\n    static get NAME() {\n      return NAME\n    }\n\n    static get DATA_KEY() {\n      return DATA_KEY\n    }\n\n    static get Event() {\n      return Event\n    }\n\n    static get EVENT_KEY() {\n      return EVENT_KEY\n    }\n\n    static get DefaultType() {\n      return DefaultType\n    }\n\n\n    // public\n\n    enable() {\n      this._isEnabled = true\n    }\n\n    disable() {\n      this._isEnabled = false\n    }\n\n    toggleEnabled() {\n      this._isEnabled = !this._isEnabled\n    }\n\n    toggle(event) {\n      if (!this._isEnabled) {\n        return\n      }\n\n      if (event) {\n        const dataKey = this.constructor.DATA_KEY\n        let context = $(event.currentTarget).data(dataKey)\n\n        if (!context) {\n          context = new this.constructor(\n            event.currentTarget,\n            this._getDelegateConfig()\n          )\n          $(event.currentTarget).data(dataKey, context)\n        }\n\n        context._activeTrigger.click = !context._activeTrigger.click\n\n        if (context._isWithActiveTrigger()) {\n          context._enter(null, context)\n        } else {\n          context._leave(null, context)\n        }\n\n      } else {\n\n        if ($(this.getTipElement()).hasClass(ClassName.SHOW)) {\n          this._leave(null, this)\n          return\n        }\n\n        this._enter(null, this)\n      }\n    }\n\n    dispose() {\n      clearTimeout(this._timeout)\n\n      $.removeData(this.element, this.constructor.DATA_KEY)\n\n      $(this.element).off(this.constructor.EVENT_KEY)\n      $(this.element).closest('.modal').off('hide.bs.modal')\n\n      if (this.tip) {\n        $(this.tip).remove()\n      }\n\n      this._isEnabled     = null\n      this._timeout       = null\n      this._hoverState    = null\n      this._activeTrigger = null\n      if (this._popper !== null) {\n        this._popper.destroy()\n      }\n\n      this._popper = null\n      this.element = null\n      this.config  = null\n      this.tip     = null\n    }\n\n    show() {\n      if ($(this.element).css('display') === 'none') {\n        throw new Error('Please use show on visible elements')\n      }\n\n      const showEvent = $.Event(this.constructor.Event.SHOW)\n      if (this.isWithContent() && this._isEnabled) {\n        $(this.element).trigger(showEvent)\n\n        const isInTheDom = $.contains(\n          this.element.ownerDocument.documentElement,\n          this.element\n        )\n\n        if (showEvent.isDefaultPrevented() || !isInTheDom) {\n          return\n        }\n\n        const tip   = this.getTipElement()\n        const tipId = Util.getUID(this.constructor.NAME)\n\n        tip.setAttribute('id', tipId)\n        this.element.setAttribute('aria-describedby', tipId)\n\n        this.setContent()\n\n        if (this.config.animation) {\n          $(tip).addClass(ClassName.FADE)\n        }\n\n        const placement  = typeof this.config.placement === 'function' ?\n          this.config.placement.call(this, tip, this.element) :\n          this.config.placement\n\n        const attachment = this._getAttachment(placement)\n        this.addAttachmentClass(attachment)\n\n        const container = this.config.container === false ? document.body : $(this.config.container)\n\n        $(tip).data(this.constructor.DATA_KEY, this)\n\n        if (!$.contains(this.element.ownerDocument.documentElement, this.tip)) {\n          $(tip).appendTo(container)\n        }\n\n        $(this.element).trigger(this.constructor.Event.INSERTED)\n\n        this._popper = new Popper(this.element, tip, {\n          placement: attachment,\n          modifiers: {\n            offset: {\n              offset: this.config.offset\n            },\n            flip: {\n              behavior: this.config.fallbackPlacement\n            },\n            arrow: {\n              element: Selector.ARROW\n            }\n          },\n          onCreate: (data) => {\n            if (data.originalPlacement !== data.placement) {\n              this._handlePopperPlacementChange(data)\n            }\n          },\n          onUpdate : (data) => {\n            this._handlePopperPlacementChange(data)\n          }\n        })\n\n        $(tip).addClass(ClassName.SHOW)\n\n        // if this is a touch-enabled device we add extra\n        // empty mouseover listeners to the body's immediate children;\n        // only needed because of broken event delegation on iOS\n        // https://www.quirksmode.org/blog/archives/2014/02/mouse_event_bub.html\n        if ('ontouchstart' in document.documentElement) {\n          $('body').children().on('mouseover', null, $.noop)\n        }\n\n        const complete = () => {\n          if (this.config.animation) {\n            this._fixTransition()\n          }\n          const prevHoverState = this._hoverState\n          this._hoverState     = null\n\n          $(this.element).trigger(this.constructor.Event.SHOWN)\n\n          if (prevHoverState === HoverState.OUT) {\n            this._leave(null, this)\n          }\n        }\n\n        if (Util.supportsTransitionEnd() && $(this.tip).hasClass(ClassName.FADE)) {\n          $(this.tip)\n            .one(Util.TRANSITION_END, complete)\n            .emulateTransitionEnd(Tooltip._TRANSITION_DURATION)\n        } else {\n          complete()\n        }\n      }\n    }\n\n    hide(callback) {\n      const tip       = this.getTipElement()\n      const hideEvent = $.Event(this.constructor.Event.HIDE)\n      const complete  = () => {\n        if (this._hoverState !== HoverState.SHOW && tip.parentNode) {\n          tip.parentNode.removeChild(tip)\n        }\n\n        this._cleanTipClass()\n        this.element.removeAttribute('aria-describedby')\n        $(this.element).trigger(this.constructor.Event.HIDDEN)\n        if (this._popper !== null) {\n          this._popper.destroy()\n        }\n\n        if (callback) {\n          callback()\n        }\n      }\n\n      $(this.element).trigger(hideEvent)\n\n      if (hideEvent.isDefaultPrevented()) {\n        return\n      }\n\n      $(tip).removeClass(ClassName.SHOW)\n\n      // if this is a touch-enabled device we remove the extra\n      // empty mouseover listeners we added for iOS support\n      if ('ontouchstart' in document.documentElement) {\n        $('body').children().off('mouseover', null, $.noop)\n      }\n\n      this._activeTrigger[Trigger.CLICK] = false\n      this._activeTrigger[Trigger.FOCUS] = false\n      this._activeTrigger[Trigger.HOVER] = false\n\n      if (Util.supportsTransitionEnd() &&\n          $(this.tip).hasClass(ClassName.FADE)) {\n\n        $(tip)\n          .one(Util.TRANSITION_END, complete)\n          .emulateTransitionEnd(TRANSITION_DURATION)\n\n      } else {\n        complete()\n      }\n\n      this._hoverState = ''\n\n    }\n\n    update() {\n      if (this._popper !== null) {\n        this._popper.scheduleUpdate()\n      }\n    }\n\n    // protected\n\n    isWithContent() {\n      return Boolean(this.getTitle())\n    }\n\n    addAttachmentClass(attachment) {\n      $(this.getTipElement()).addClass(`${CLASS_PREFIX}-${attachment}`)\n    }\n\n    getTipElement() {\n      this.tip = this.tip || $(this.config.template)[0]\n      return this.tip\n    }\n\n    setContent() {\n      const $tip = $(this.getTipElement())\n      this.setElementContent($tip.find(Selector.TOOLTIP_INNER), this.getTitle())\n      $tip.removeClass(`${ClassName.FADE} ${ClassName.SHOW}`)\n    }\n\n    setElementContent($element, content) {\n      const html = this.config.html\n      if (typeof content === 'object' && (content.nodeType || content.jquery)) {\n        // content is a DOM node or a jQuery\n        if (html) {\n          if (!$(content).parent().is($element)) {\n            $element.empty().append(content)\n          }\n        } else {\n          $element.text($(content).text())\n        }\n      } else {\n        $element[html ? 'html' : 'text'](content)\n      }\n    }\n\n    getTitle() {\n      let title = this.element.getAttribute('data-original-title')\n\n      if (!title) {\n        title = typeof this.config.title === 'function' ?\n          this.config.title.call(this.element) :\n          this.config.title\n      }\n\n      return title\n    }\n\n\n    // private\n\n    _getAttachment(placement) {\n      return AttachmentMap[placement.toUpperCase()]\n    }\n\n    _setListeners() {\n      const triggers = this.config.trigger.split(' ')\n\n      triggers.forEach((trigger) => {\n        if (trigger === 'click') {\n          $(this.element).on(\n            this.constructor.Event.CLICK,\n            this.config.selector,\n            (event) => this.toggle(event)\n          )\n\n        } else if (trigger !== Trigger.MANUAL) {\n          const eventIn  = trigger === Trigger.HOVER ?\n            this.constructor.Event.MOUSEENTER :\n            this.constructor.Event.FOCUSIN\n          const eventOut = trigger === Trigger.HOVER ?\n            this.constructor.Event.MOUSELEAVE :\n            this.constructor.Event.FOCUSOUT\n\n          $(this.element)\n            .on(\n              eventIn,\n              this.config.selector,\n              (event) => this._enter(event)\n            )\n            .on(\n              eventOut,\n              this.config.selector,\n              (event) => this._leave(event)\n            )\n        }\n\n        $(this.element).closest('.modal').on(\n          'hide.bs.modal',\n          () => this.hide()\n        )\n      })\n\n      if (this.config.selector) {\n        this.config = $.extend({}, this.config, {\n          trigger  : 'manual',\n          selector : ''\n        })\n      } else {\n        this._fixTitle()\n      }\n    }\n\n    _fixTitle() {\n      const titleType = typeof this.element.getAttribute('data-original-title')\n      if (this.element.getAttribute('title') ||\n         titleType !== 'string') {\n        this.element.setAttribute(\n          'data-original-title',\n          this.element.getAttribute('title') || ''\n        )\n        this.element.setAttribute('title', '')\n      }\n    }\n\n    _enter(event, context) {\n      const dataKey = this.constructor.DATA_KEY\n\n      context = context || $(event.currentTarget).data(dataKey)\n\n      if (!context) {\n        context = new this.constructor(\n          event.currentTarget,\n          this._getDelegateConfig()\n        )\n        $(event.currentTarget).data(dataKey, context)\n      }\n\n      if (event) {\n        context._activeTrigger[\n          event.type === 'focusin' ? Trigger.FOCUS : Trigger.HOVER\n        ] = true\n      }\n\n      if ($(context.getTipElement()).hasClass(ClassName.SHOW) ||\n         context._hoverState === HoverState.SHOW) {\n        context._hoverState = HoverState.SHOW\n        return\n      }\n\n      clearTimeout(context._timeout)\n\n      context._hoverState = HoverState.SHOW\n\n      if (!context.config.delay || !context.config.delay.show) {\n        context.show()\n        return\n      }\n\n      context._timeout = setTimeout(() => {\n        if (context._hoverState === HoverState.SHOW) {\n          context.show()\n        }\n      }, context.config.delay.show)\n    }\n\n    _leave(event, context) {\n      const dataKey = this.constructor.DATA_KEY\n\n      context = context || $(event.currentTarget).data(dataKey)\n\n      if (!context) {\n        context = new this.constructor(\n          event.currentTarget,\n          this._getDelegateConfig()\n        )\n        $(event.currentTarget).data(dataKey, context)\n      }\n\n      if (event) {\n        context._activeTrigger[\n          event.type === 'focusout' ? Trigger.FOCUS : Trigger.HOVER\n        ] = false\n      }\n\n      if (context._isWithActiveTrigger()) {\n        return\n      }\n\n      clearTimeout(context._timeout)\n\n      context._hoverState = HoverState.OUT\n\n      if (!context.config.delay || !context.config.delay.hide) {\n        context.hide()\n        return\n      }\n\n      context._timeout = setTimeout(() => {\n        if (context._hoverState === HoverState.OUT) {\n          context.hide()\n        }\n      }, context.config.delay.hide)\n    }\n\n    _isWithActiveTrigger() {\n      for (const trigger in this._activeTrigger) {\n        if (this._activeTrigger[trigger]) {\n          return true\n        }\n      }\n\n      return false\n    }\n\n    _getConfig(config) {\n      config = $.extend(\n        {},\n        this.constructor.Default,\n        $(this.element).data(),\n        config\n      )\n\n      if (typeof config.delay === 'number') {\n        config.delay = {\n          show : config.delay,\n          hide : config.delay\n        }\n      }\n\n      if (typeof config.title === 'number') {\n        config.title = config.title.toString()\n      }\n\n      if (typeof config.content === 'number') {\n        config.content = config.content.toString()\n      }\n\n      Util.typeCheckConfig(\n        NAME,\n        config,\n        this.constructor.DefaultType\n      )\n\n      return config\n    }\n\n    _getDelegateConfig() {\n      const config = {}\n\n      if (this.config) {\n        for (const key in this.config) {\n          if (this.constructor.Default[key] !== this.config[key]) {\n            config[key] = this.config[key]\n          }\n        }\n      }\n\n      return config\n    }\n\n    _cleanTipClass() {\n      const $tip = $(this.getTipElement())\n      const tabClass = $tip.attr('class').match(BSCLS_PREFIX_REGEX)\n      if (tabClass !== null && tabClass.length > 0) {\n        $tip.removeClass(tabClass.join(''))\n      }\n    }\n\n    _handlePopperPlacementChange(data) {\n      this._cleanTipClass()\n      this.addAttachmentClass(this._getAttachment(data.placement))\n    }\n\n    _fixTransition() {\n      const tip                 = this.getTipElement()\n      const initConfigAnimation = this.config.animation\n      if (tip.getAttribute('x-placement') !== null) {\n        return\n      }\n      $(tip).removeClass(ClassName.FADE)\n      this.config.animation = false\n      this.hide()\n      this.show()\n      this.config.animation = initConfigAnimation\n    }\n\n    // static\n\n    static _jQueryInterface(config) {\n      return this.each(function () {\n        let data      = $(this).data(DATA_KEY)\n        const _config = typeof config === 'object' && config\n\n        if (!data && /dispose|hide/.test(config)) {\n          return\n        }\n\n        if (!data) {\n          data = new Tooltip(this, _config)\n          $(this).data(DATA_KEY, data)\n        }\n\n        if (typeof config === 'string') {\n          if (typeof data[config] === 'undefined') {\n            throw new Error(`No method named \"${config}\"`)\n          }\n          data[config]()\n        }\n      })\n    }\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * jQuery\n   * ------------------------------------------------------------------------\n   */\n\n  $.fn[NAME]             = Tooltip._jQueryInterface\n  $.fn[NAME].Constructor = Tooltip\n  $.fn[NAME].noConflict  = function () {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Tooltip._jQueryInterface\n  }\n\n  return Tooltip\n\n})($, Popper)\n\nexport default Tooltip\n", "import $ from 'jquery'\nimport Tooltip from './tooltip'\n\n\n/**\n * --------------------------------------------------------------------------\n * <PERSON>trap (v4.0.0-beta.2): popover.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst Popover = (() => {\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Constants\n   * ------------------------------------------------------------------------\n   */\n\n  const NAME                = 'popover'\n  const VERSION             = '4.0.0-beta.2'\n  const DATA_KEY            = 'bs.popover'\n  const EVENT_KEY           = `.${DATA_KEY}`\n  const JQUERY_NO_CONFLICT  = $.fn[NAME]\n  const CLASS_PREFIX        = 'bs-popover'\n  const BSCLS_PREFIX_REGEX  = new RegExp(`(^|\\\\s)${CLASS_PREFIX}\\\\S+`, 'g')\n\n  const Default = $.extend({}, Tooltip.Default, {\n    placement : 'right',\n    trigger   : 'click',\n    content   : '',\n    template  : '<div class=\"popover\" role=\"tooltip\">'\n              + '<div class=\"arrow\"></div>'\n              + '<h3 class=\"popover-header\"></h3>'\n              + '<div class=\"popover-body\"></div></div>'\n  })\n\n  const DefaultType = $.extend({}, Tooltip.DefaultType, {\n    content : '(string|element|function)'\n  })\n\n  const ClassName = {\n    FADE : 'fade',\n    SHOW : 'show'\n  }\n\n  const Selector = {\n    TITLE   : '.popover-header',\n    CONTENT : '.popover-body'\n  }\n\n  const Event = {\n    HIDE       : `hide${EVENT_KEY}`,\n    HIDDEN     : `hidden${EVENT_KEY}`,\n    SHOW       : `show${EVENT_KEY}`,\n    SHOWN      : `shown${EVENT_KEY}`,\n    INSERTED   : `inserted${EVENT_KEY}`,\n    CLICK      : `click${EVENT_KEY}`,\n    FOCUSIN    : `focusin${EVENT_KEY}`,\n    FOCUSOUT   : `focusout${EVENT_KEY}`,\n    MOUSEENTER : `mouseenter${EVENT_KEY}`,\n    MOUSELEAVE : `mouseleave${EVENT_KEY}`\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Class Definition\n   * ------------------------------------------------------------------------\n   */\n\n  class Popover extends Tooltip {\n\n\n    // getters\n\n    static get VERSION() {\n      return VERSION\n    }\n\n    static get Default() {\n      return Default\n    }\n\n    static get NAME() {\n      return NAME\n    }\n\n    static get DATA_KEY() {\n      return DATA_KEY\n    }\n\n    static get Event() {\n      return Event\n    }\n\n    static get EVENT_KEY() {\n      return EVENT_KEY\n    }\n\n    static get DefaultType() {\n      return DefaultType\n    }\n\n\n    // overrides\n\n    isWithContent() {\n      return this.getTitle() || this._getContent()\n    }\n\n    addAttachmentClass(attachment) {\n      $(this.getTipElement()).addClass(`${CLASS_PREFIX}-${attachment}`)\n    }\n\n    getTipElement() {\n      this.tip = this.tip || $(this.config.template)[0]\n      return this.tip\n    }\n\n    setContent() {\n      const $tip = $(this.getTipElement())\n\n      // we use append for html objects to maintain js events\n      this.setElementContent($tip.find(Selector.TITLE), this.getTitle())\n      this.setElementContent($tip.find(Selector.CONTENT), this._getContent())\n\n      $tip.removeClass(`${ClassName.FADE} ${ClassName.SHOW}`)\n    }\n\n    // private\n\n    _getContent() {\n      return this.element.getAttribute('data-content')\n        || (typeof this.config.content === 'function' ?\n              this.config.content.call(this.element) :\n              this.config.content)\n    }\n\n    _cleanTipClass() {\n      const $tip = $(this.getTipElement())\n      const tabClass = $tip.attr('class').match(BSCLS_PREFIX_REGEX)\n      if (tabClass !== null && tabClass.length > 0) {\n        $tip.removeClass(tabClass.join(''))\n      }\n    }\n\n\n    // static\n\n    static _jQueryInterface(config) {\n      return this.each(function () {\n        let data      = $(this).data(DATA_KEY)\n        const _config = typeof config === 'object' ? config : null\n\n        if (!data && /destroy|hide/.test(config)) {\n          return\n        }\n\n        if (!data) {\n          data = new Popover(this, _config)\n          $(this).data(DATA_KEY, data)\n        }\n\n        if (typeof config === 'string') {\n          if (typeof data[config] === 'undefined') {\n            throw new Error(`No method named \"${config}\"`)\n          }\n          data[config]()\n        }\n      })\n    }\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * jQuery\n   * ------------------------------------------------------------------------\n   */\n\n  $.fn[NAME]             = Popover._jQueryInterface\n  $.fn[NAME].Constructor = Popover\n  $.fn[NAME].noConflict  = function () {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Popover._jQueryInterface\n  }\n\n  return Popover\n\n})($)\n\nexport default Popover\n", "import $ from 'jquery'\nimport Util from './util'\n\n\n/**\n * --------------------------------------------------------------------------\n * <PERSON>trap (v4.0.0-beta.2): scrollspy.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst ScrollSpy = (() => {\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Constants\n   * ------------------------------------------------------------------------\n   */\n\n  const NAME               = 'scrollspy'\n  const VERSION            = '4.0.0-beta.2'\n  const DATA_KEY           = 'bs.scrollspy'\n  const EVENT_KEY          = `.${DATA_KEY}`\n  const DATA_API_KEY       = '.data-api'\n  const JQUERY_NO_CONFLICT = $.fn[NAME]\n\n  const Default = {\n    offset : 10,\n    method : 'auto',\n    target : ''\n  }\n\n  const DefaultType = {\n    offset : 'number',\n    method : 'string',\n    target : '(string|element)'\n  }\n\n  const Event = {\n    ACTIVATE      : `activate${EVENT_KEY}`,\n    SCROLL        : `scroll${EVENT_KEY}`,\n    LOAD_DATA_API : `load${EVENT_KEY}${DATA_API_KEY}`\n  }\n\n  const ClassName = {\n    DROPDOWN_ITEM : 'dropdown-item',\n    DROPDOWN_MENU : 'dropdown-menu',\n    ACTIVE        : 'active'\n  }\n\n  const Selector = {\n    DATA_SPY        : '[data-spy=\"scroll\"]',\n    ACTIVE          : '.active',\n    NAV_LIST_GROUP  : '.nav, .list-group',\n    NAV_LINKS       : '.nav-link',\n    NAV_ITEMS       : '.nav-item',\n    LIST_ITEMS      : '.list-group-item',\n    DROPDOWN        : '.dropdown',\n    DROPDOWN_ITEMS  : '.dropdown-item',\n    DROPDOWN_TOGGLE : '.dropdown-toggle'\n  }\n\n  const OffsetMethod = {\n    OFFSET   : 'offset',\n    POSITION : 'position'\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Class Definition\n   * ------------------------------------------------------------------------\n   */\n\n  class ScrollSpy {\n\n    constructor(element, config) {\n      this._element       = element\n      this._scrollElement = element.tagName === 'BODY' ? window : element\n      this._config        = this._getConfig(config)\n      this._selector      = `${this._config.target} ${Selector.NAV_LINKS},`\n                          + `${this._config.target} ${Selector.LIST_ITEMS},`\n                          + `${this._config.target} ${Selector.DROPDOWN_ITEMS}`\n      this._offsets       = []\n      this._targets       = []\n      this._activeTarget  = null\n      this._scrollHeight  = 0\n\n      $(this._scrollElement).on(Event.SCROLL, (event) => this._process(event))\n\n      this.refresh()\n      this._process()\n    }\n\n\n    // getters\n\n    static get VERSION() {\n      return VERSION\n    }\n\n    static get Default() {\n      return Default\n    }\n\n\n    // public\n\n    refresh() {\n      const autoMethod = this._scrollElement !== this._scrollElement.window ?\n        OffsetMethod.POSITION : OffsetMethod.OFFSET\n\n      const offsetMethod = this._config.method === 'auto' ?\n        autoMethod : this._config.method\n\n      const offsetBase = offsetMethod === OffsetMethod.POSITION ?\n        this._getScrollTop() : 0\n\n      this._offsets = []\n      this._targets = []\n\n      this._scrollHeight = this._getScrollHeight()\n\n      const targets = $.makeArray($(this._selector))\n\n      targets\n        .map((element) => {\n          let target\n          const targetSelector = Util.getSelectorFromElement(element)\n\n          if (targetSelector) {\n            target = $(targetSelector)[0]\n          }\n\n          if (target) {\n            const targetBCR = target.getBoundingClientRect()\n            if (targetBCR.width || targetBCR.height) {\n              // todo (fat): remove sketch reliance on jQuery position/offset\n              return [\n                $(target)[offsetMethod]().top + offsetBase,\n                targetSelector\n              ]\n            }\n          }\n          return null\n        })\n        .filter((item)  => item)\n        .sort((a, b)    => a[0] - b[0])\n        .forEach((item) => {\n          this._offsets.push(item[0])\n          this._targets.push(item[1])\n        })\n    }\n\n    dispose() {\n      $.removeData(this._element, DATA_KEY)\n      $(this._scrollElement).off(EVENT_KEY)\n\n      this._element       = null\n      this._scrollElement = null\n      this._config        = null\n      this._selector      = null\n      this._offsets       = null\n      this._targets       = null\n      this._activeTarget  = null\n      this._scrollHeight  = null\n    }\n\n\n    // private\n\n    _getConfig(config) {\n      config = $.extend({}, Default, config)\n\n      if (typeof config.target !== 'string') {\n        let id = $(config.target).attr('id')\n        if (!id) {\n          id = Util.getUID(NAME)\n          $(config.target).attr('id', id)\n        }\n        config.target = `#${id}`\n      }\n\n      Util.typeCheckConfig(NAME, config, DefaultType)\n\n      return config\n    }\n\n    _getScrollTop() {\n      return this._scrollElement === window ?\n          this._scrollElement.pageYOffset : this._scrollElement.scrollTop\n    }\n\n    _getScrollHeight() {\n      return this._scrollElement.scrollHeight || Math.max(\n        document.body.scrollHeight,\n        document.documentElement.scrollHeight\n      )\n    }\n\n    _getOffsetHeight() {\n      return this._scrollElement === window ?\n          window.innerHeight : this._scrollElement.getBoundingClientRect().height\n    }\n\n    _process() {\n      const scrollTop    = this._getScrollTop() + this._config.offset\n      const scrollHeight = this._getScrollHeight()\n      const maxScroll    = this._config.offset\n        + scrollHeight\n        - this._getOffsetHeight()\n\n      if (this._scrollHeight !== scrollHeight) {\n        this.refresh()\n      }\n\n      if (scrollTop >= maxScroll) {\n        const target = this._targets[this._targets.length - 1]\n\n        if (this._activeTarget !== target) {\n          this._activate(target)\n        }\n        return\n      }\n\n      if (this._activeTarget && scrollTop < this._offsets[0] && this._offsets[0] > 0) {\n        this._activeTarget = null\n        this._clear()\n        return\n      }\n\n      for (let i = this._offsets.length; i--;) {\n        const isActiveTarget = this._activeTarget !== this._targets[i]\n            && scrollTop >= this._offsets[i]\n            && (typeof this._offsets[i + 1] === 'undefined' ||\n                scrollTop < this._offsets[i + 1])\n\n        if (isActiveTarget) {\n          this._activate(this._targets[i])\n        }\n      }\n    }\n\n    _activate(target) {\n      this._activeTarget = target\n\n      this._clear()\n\n      let queries = this._selector.split(',')\n      // eslint-disable-next-line arrow-body-style\n      queries     = queries.map((selector) => {\n        return `${selector}[data-target=\"${target}\"],` +\n               `${selector}[href=\"${target}\"]`\n      })\n\n      const $link = $(queries.join(','))\n\n      if ($link.hasClass(ClassName.DROPDOWN_ITEM)) {\n        $link.closest(Selector.DROPDOWN).find(Selector.DROPDOWN_TOGGLE).addClass(ClassName.ACTIVE)\n        $link.addClass(ClassName.ACTIVE)\n      } else {\n        // Set triggered link as active\n        $link.addClass(ClassName.ACTIVE)\n        // Set triggered links parents as active\n        // With both <ul> and <nav> markup a parent is the previous sibling of any nav ancestor\n        $link.parents(Selector.NAV_LIST_GROUP).prev(`${Selector.NAV_LINKS}, ${Selector.LIST_ITEMS}`).addClass(ClassName.ACTIVE)\n        // Handle special case when .nav-link is inside .nav-item\n        $link.parents(Selector.NAV_LIST_GROUP).prev(Selector.NAV_ITEMS).children(Selector.NAV_LINKS).addClass(ClassName.ACTIVE)\n      }\n\n      $(this._scrollElement).trigger(Event.ACTIVATE, {\n        relatedTarget: target\n      })\n    }\n\n    _clear() {\n      $(this._selector).filter(Selector.ACTIVE).removeClass(ClassName.ACTIVE)\n    }\n\n\n    // static\n\n    static _jQueryInterface(config) {\n      return this.each(function () {\n        let data      = $(this).data(DATA_KEY)\n        const _config = typeof config === 'object' && config\n\n        if (!data) {\n          data = new ScrollSpy(this, _config)\n          $(this).data(DATA_KEY, data)\n        }\n\n        if (typeof config === 'string') {\n          if (typeof data[config] === 'undefined') {\n            throw new Error(`No method named \"${config}\"`)\n          }\n          data[config]()\n        }\n      })\n    }\n\n\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Data Api implementation\n   * ------------------------------------------------------------------------\n   */\n\n  $(window).on(Event.LOAD_DATA_API, () => {\n    const scrollSpys = $.makeArray($(Selector.DATA_SPY))\n\n    for (let i = scrollSpys.length; i--;) {\n      const $spy = $(scrollSpys[i])\n      ScrollSpy._jQueryInterface.call($spy, $spy.data())\n    }\n  })\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * jQuery\n   * ------------------------------------------------------------------------\n   */\n\n  $.fn[NAME]             = ScrollSpy._jQueryInterface\n  $.fn[NAME].Constructor = ScrollSpy\n  $.fn[NAME].noConflict  = function () {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return ScrollSpy._jQueryInterface\n  }\n\n  return ScrollSpy\n\n})($)\n\nexport default ScrollSpy\n", "import $ from 'jquery'\nimport Util from './util'\n\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.0.0-beta.2): tab.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\nconst Tab = (() => {\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Constants\n   * ------------------------------------------------------------------------\n   */\n\n  const NAME                = 'tab'\n  const VERSION             = '4.0.0-beta.2'\n  const DATA_KEY            = 'bs.tab'\n  const EVENT_KEY           = `.${DATA_KEY}`\n  const DATA_API_KEY        = '.data-api'\n  const JQUERY_NO_CONFLICT  = $.fn[NAME]\n  const TRANSITION_DURATION = 150\n\n  const Event = {\n    HIDE           : `hide${EVENT_KEY}`,\n    HIDDEN         : `hidden${EVENT_KEY}`,\n    SHOW           : `show${EVENT_KEY}`,\n    SHOWN          : `shown${EVENT_KEY}`,\n    CLICK_DATA_API : `click${EVENT_KEY}${DATA_API_KEY}`\n  }\n\n  const ClassName = {\n    DROPDOWN_MENU : 'dropdown-menu',\n    ACTIVE        : 'active',\n    DISABLED      : 'disabled',\n    FADE          : 'fade',\n    SHOW          : 'show'\n  }\n\n  const Selector = {\n    DROPDOWN              : '.dropdown',\n    NAV_LIST_GROUP        : '.nav, .list-group',\n    ACTIVE                : '.active',\n    ACTIVE_UL             : '> li > .active',\n    DATA_TOGGLE           : '[data-toggle=\"tab\"], [data-toggle=\"pill\"], [data-toggle=\"list\"]',\n    DROPDOWN_TOGGLE       : '.dropdown-toggle',\n    DROPDOWN_ACTIVE_CHILD : '> .dropdown-menu .active'\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Class Definition\n   * ------------------------------------------------------------------------\n   */\n\n  class Tab {\n\n    constructor(element) {\n      this._element = element\n    }\n\n\n    // getters\n\n    static get VERSION() {\n      return VERSION\n    }\n\n\n    // public\n\n    show() {\n      if (this._element.parentNode &&\n          this._element.parentNode.nodeType === Node.ELEMENT_NODE &&\n          $(this._element).hasClass(ClassName.ACTIVE) ||\n          $(this._element).hasClass(ClassName.DISABLED)) {\n        return\n      }\n\n      let target\n      let previous\n      const listElement = $(this._element).closest(Selector.NAV_LIST_GROUP)[0]\n      const selector    = Util.getSelectorFromElement(this._element)\n\n      if (listElement) {\n        const itemSelector = listElement.nodeName === 'UL' ? Selector.ACTIVE_UL : Selector.ACTIVE\n        previous = $.makeArray($(listElement).find(itemSelector))\n        previous = previous[previous.length - 1]\n      }\n\n      const hideEvent = $.Event(Event.HIDE, {\n        relatedTarget: this._element\n      })\n\n      const showEvent = $.Event(Event.SHOW, {\n        relatedTarget: previous\n      })\n\n      if (previous) {\n        $(previous).trigger(hideEvent)\n      }\n\n      $(this._element).trigger(showEvent)\n\n      if (showEvent.isDefaultPrevented() ||\n         hideEvent.isDefaultPrevented()) {\n        return\n      }\n\n      if (selector) {\n        target = $(selector)[0]\n      }\n\n      this._activate(\n        this._element,\n        listElement\n      )\n\n      const complete = () => {\n        const hiddenEvent = $.Event(Event.HIDDEN, {\n          relatedTarget: this._element\n        })\n\n        const shownEvent = $.Event(Event.SHOWN, {\n          relatedTarget: previous\n        })\n\n        $(previous).trigger(hiddenEvent)\n        $(this._element).trigger(shownEvent)\n      }\n\n      if (target) {\n        this._activate(target, target.parentNode, complete)\n      } else {\n        complete()\n      }\n    }\n\n    dispose() {\n      $.removeData(this._element, DATA_KEY)\n      this._element = null\n    }\n\n\n    // private\n\n    _activate(element, container, callback) {\n      let activeElements\n      if (container.nodeName === 'UL') {\n        activeElements = $(container).find(Selector.ACTIVE_UL)\n      } else {\n        activeElements = $(container).children(Selector.ACTIVE)\n      }\n\n      const active          = activeElements[0]\n      const isTransitioning = callback\n        && Util.supportsTransitionEnd()\n        && (active && $(active).hasClass(ClassName.FADE))\n\n      const complete = () => this._transitionComplete(\n        element,\n        active,\n        isTransitioning,\n        callback\n      )\n\n      if (active && isTransitioning) {\n        $(active)\n          .one(Util.TRANSITION_END, complete)\n          .emulateTransitionEnd(TRANSITION_DURATION)\n\n      } else {\n        complete()\n      }\n\n      if (active) {\n        $(active).removeClass(ClassName.SHOW)\n      }\n    }\n\n    _transitionComplete(element, active, isTransitioning, callback) {\n      if (active) {\n        $(active).removeClass(ClassName.ACTIVE)\n\n        const dropdownChild = $(active.parentNode).find(\n          Selector.DROPDOWN_ACTIVE_CHILD\n        )[0]\n\n        if (dropdownChild) {\n          $(dropdownChild).removeClass(ClassName.ACTIVE)\n        }\n\n        if (active.getAttribute('role') === 'tab') {\n          active.setAttribute('aria-selected', false)\n        }\n      }\n\n      $(element).addClass(ClassName.ACTIVE)\n      if (element.getAttribute('role') === 'tab') {\n        element.setAttribute('aria-selected', true)\n      }\n\n      if (isTransitioning) {\n        Util.reflow(element)\n        $(element).addClass(ClassName.SHOW)\n      } else {\n        $(element).removeClass(ClassName.FADE)\n      }\n\n      if (element.parentNode &&\n          $(element.parentNode).hasClass(ClassName.DROPDOWN_MENU)) {\n\n        const dropdownElement = $(element).closest(Selector.DROPDOWN)[0]\n        if (dropdownElement) {\n          $(dropdownElement).find(Selector.DROPDOWN_TOGGLE).addClass(ClassName.ACTIVE)\n        }\n\n        element.setAttribute('aria-expanded', true)\n      }\n\n      if (callback) {\n        callback()\n      }\n    }\n\n\n    // static\n\n    static _jQueryInterface(config) {\n      return this.each(function () {\n        const $this = $(this)\n        let data    = $this.data(DATA_KEY)\n\n        if (!data) {\n          data = new Tab(this)\n          $this.data(DATA_KEY, data)\n        }\n\n        if (typeof config === 'string') {\n          if (typeof data[config] === 'undefined') {\n            throw new Error(`No method named \"${config}\"`)\n          }\n          data[config]()\n        }\n      })\n    }\n\n  }\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * Data Api implementation\n   * ------------------------------------------------------------------------\n   */\n\n  $(document)\n    .on(Event.CLICK_DATA_API, Selector.DATA_TOGGLE, function (event) {\n      event.preventDefault()\n      Tab._jQueryInterface.call($(this), 'show')\n    })\n\n\n  /**\n   * ------------------------------------------------------------------------\n   * jQuery\n   * ------------------------------------------------------------------------\n   */\n\n  $.fn[NAME]             = Tab._jQueryInterface\n  $.fn[NAME].Constructor = Tab\n  $.fn[NAME].noConflict  = function () {\n    $.fn[NAME] = JQUERY_NO_CONFLICT\n    return Tab._jQueryInterface\n  }\n\n  return Tab\n\n})($)\n\nexport default Tab\n", "import $ from 'jquery'\nimport Al<PERSON> from './alert'\nimport <PERSON><PERSON> from './button'\nimport Carousel from './carousel'\nimport Collapse from './collapse'\nimport Dropdown from './dropdown'\nimport Modal from './modal'\nimport Popover from './popover'\nimport Scrollspy from './scrollspy'\nimport Tab from './tab'\nimport Tooltip from './tooltip'\nimport Util from './util'\n\n/**\n * --------------------------------------------------------------------------\n * Bootstrap (v4.0.0-alpha.6): index.js\n * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)\n * --------------------------------------------------------------------------\n */\n\n(() => {\n  if (typeof $ === 'undefined') {\n    throw new Error('Bootstrap\\'s JavaScript requires jQuery. jQuery must be included before Bootstrap\\'s JavaScript.')\n  }\n\n  const version = $.fn.jquery.split(' ')[0].split('.')\n  const minMajor = 1\n  const ltMajor  = 2\n  const minMinor = 9\n  const minPatch = 1\n  const maxMajor = 4\n\n  if (version[0] < ltMajor && version[1] < minMinor || version[0] === minMajor && version[1] === minMinor && version[2] < minPatch || version[0] >= maxMajor) {\n    throw new Error('Bootstrap\\'s JavaScript requires at least jQuery v1.9.1 but less than v4.0.0')\n  }\n})($)\n\nexport {\n  Util,\n  Alert,\n  Button,\n  Carousel,\n  Collapse,\n  Dropdown,\n  Modal,\n  Popover,\n  Scrollspy,\n  Tab,\n  Tooltip\n}\n"], "names": ["<PERSON><PERSON>", "transition", "MAX_UID", "TransitionEndEvent", "toType", "obj", "toString", "call", "match", "toLowerCase", "getSpecialTransitionEndEvent", "end", "event", "$", "target", "is", "handleObj", "handler", "apply", "arguments", "undefined", "transitionEndTest", "window", "QUnit", "el", "document", "createElement", "name", "style", "transitionEndEmulator", "duration", "called", "one", "TRANSITION_END", "triggerTransitionEnd", "setTransitionEndSupport", "fn", "emulateTransitionEnd", "supportsTransitionEnd", "special", "prefix", "Math", "random", "getElementById", "element", "selector", "getAttribute", "$selector", "find", "length", "error", "offsetHeight", "trigger", "Boolean", "nodeType", "componentName", "config", "configTypes", "property", "Object", "prototype", "hasOwnProperty", "expectedTypes", "value", "valueType", "isElement", "RegExp", "test", "Error", "toUpperCase", "<PERSON><PERSON>", "NAME", "VERSION", "DATA_KEY", "EVENT_KEY", "DATA_API_KEY", "JQUERY_NO_CONFLICT", "TRANSITION_DURATION", "Selector", "Event", "ClassName", "_element", "close", "rootElement", "_getRootElement", "customEvent", "_triggerCloseEvent", "isDefaultPrevented", "_removeElement", "dispose", "removeData", "getSelectorFromElement", "parent", "closest", "ALERT", "closeEvent", "CLOSE", "removeClass", "SHOW", "hasClass", "FADE", "_destroyElement", "detach", "CLOSED", "remove", "_jQueryInterface", "each", "$element", "data", "_handleDismiss", "alertInstance", "preventDefault", "on", "CLICK_DATA_API", "DISMISS", "<PERSON><PERSON><PERSON><PERSON>", "noConflict", "<PERSON><PERSON>", "toggle", "triggerChangeEvent", "addAriaPressed", "DATA_TOGGLE", "input", "INPUT", "type", "checked", "ACTIVE", "activeElement", "hasAttribute", "classList", "contains", "focus", "setAttribute", "toggleClass", "DATA_TOGGLE_CARROT", "button", "BUTTON", "FOCUS_BLUR_DATA_API", "FOCUS", "Carousel", "ARROW_LEFT_KEYCODE", "ARROW_RIGHT_KEYCODE", "TOUCHEVENT_COMPAT_WAIT", "<PERSON><PERSON><PERSON>", "DefaultType", "Direction", "_items", "_interval", "_activeElement", "_isPaused", "_isSliding", "touchTimeout", "_config", "_getConfig", "_indicatorsElement", "INDICATORS", "_addEventListeners", "next", "_slide", "NEXT", "nextWhenVisible", "hidden", "css", "prev", "PREV", "pause", "NEXT_PREV", "cycle", "interval", "setInterval", "visibilityState", "bind", "to", "index", "ACTIVE_ITEM", "activeIndex", "_getItemIndex", "SLID", "direction", "off", "extend", "typeCheckConfig", "keyboard", "KEYDOWN", "_keydown", "MOUSEENTER", "MOUSELEAVE", "documentElement", "TOUCHEND", "setTimeout", "tagName", "which", "makeArray", "ITEM", "indexOf", "_getItemByDirection", "isNextDirection", "isPrevDirection", "lastItemIndex", "isGoingToWrap", "wrap", "delta", "itemIndex", "_triggerSlideEvent", "relatedTarget", "eventDirectionName", "targetIndex", "fromIndex", "slideEvent", "SLIDE", "_setActiveIndicatorElement", "nextIndicator", "children", "addClass", "activeElementIndex", "nextElement", "nextElementIndex", "isCycling", "directionalClassName", "orderClassName", "LEFT", "RIGHT", "slidEvent", "reflow", "action", "slide", "_dataApiClickHandler", "CAROUSEL", "slideIndex", "DATA_SLIDE", "LOAD_DATA_API", "DATA_RIDE", "$carousel", "Collapse", "Dimension", "_isTransitioning", "_triggerArray", "id", "tab<PERSON>og<PERSON>", "i", "elem", "filter", "push", "_parent", "_getParent", "_addAriaAndCollapsedClass", "hide", "show", "actives", "activesData", "ACTIVES", "startEvent", "dimension", "_getDimension", "COLLAPSE", "COLLAPSING", "COLLAPSED", "attr", "setTransitioning", "complete", "SHOWN", "capitalizedDimension", "slice", "scrollSize", "HIDE", "getBoundingClientRect", "$elem", "HIDDEN", "isTransitioning", "<PERSON><PERSON><PERSON><PERSON>", "WIDTH", "HEIGHT", "j<PERSON>y", "_getTargetFromElement", "trigger<PERSON><PERSON>y", "isOpen", "$this", "currentTarget", "$trigger", "$target", "createClass", "_extends", "Dropdown", "<PERSON><PERSON>", "ESCAPE_KEYCODE", "SPACE_KEYCODE", "TAB_KEYCODE", "ARROW_UP_KEYCODE", "ARROW_DOWN_KEYCODE", "RIGHT_MOUSE_BUTTON_WHICH", "REGEXP_KEYDOWN", "AttachmentMap", "_popper", "_menu", "_getMenuElement", "_inNavbar", "_detectNavbar", "disabled", "DISABLED", "_getParentFromElement", "isActive", "_clearMenus", "showEvent", "DROPUP", "MENULEFT", "MENURIGHT", "_getPopperConfig", "NAVBAR_NAV", "noop", "destroy", "update", "scheduleUpdate", "CLICK", "stopPropagation", "constructor", "MENU", "_getPlacement", "$parentDropdown", "placement", "BOTTOM", "TOP", "TOPEND", "BOTTOMEND", "offsetConf", "offset", "offsets", "popperConfig", "flip", "modifiers", "applyStyle", "toggles", "context", "dropdownMenu", "hideEvent", "parentNode", "_dataApiKeydownHandler", "items", "VISIBLE_ITEMS", "get", "KEYDOWN_DATA_API", "KEYUP_DATA_API", "FORM_CHILD", "e", "Modal", "BACKDROP_TRANSITION_DURATION", "_dialog", "DIALOG", "_backdrop", "_isShown", "_isBodyOverflowing", "_ignoreBackdropClick", "_originalBodyPadding", "_scrollbarWidth", "_checkScrollbar", "_setScrollbar", "_adjustDialog", "body", "OPEN", "_setEscapeEvent", "_setResizeEvent", "CLICK_DISMISS", "DATA_DISMISS", "MOUSEDOWN_DISMISS", "MOUSEUP_DISMISS", "_showBackdrop", "_showElement", "FOCUSIN", "_hideModal", "handleUpdate", "Node", "ELEMENT_NODE", "append<PERSON><PERSON><PERSON>", "display", "removeAttribute", "scrollTop", "_enforceFocus", "shownEvent", "transitionComplete", "has", "KEYDOWN_DISMISS", "RESIZE", "_resetAdjustments", "_resetScrollbar", "_removeBackdrop", "callback", "animate", "backdrop", "doAnimate", "className", "BACKDROP", "appendTo", "callback<PERSON><PERSON><PERSON>", "isModalOverflowing", "scrollHeight", "clientHeight", "paddingLeft", "paddingRight", "rect", "left", "right", "innerWidth", "_getScrollbarWidth", "FIXED_CONTENT", "actualPadding", "calculatedPadding", "parseFloat", "STICKY_CONTENT", "<PERSON><PERSON><PERSON><PERSON>", "marginRight", "<PERSON><PERSON><PERSON><PERSON>", "NAVBAR_TOGGLER", "padding", "margin", "scrollDiv", "SCROLLBAR_MEASURER", "scrollbarWidth", "width", "clientWidth", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "CLASS_PREFIX", "BSCLS_PREFIX_REGEX", "HoverState", "<PERSON><PERSON>", "_isEnabled", "_timeout", "_hoverState", "_activeTrigger", "tip", "_setListeners", "enable", "disable", "toggle<PERSON>nabled", "dataKey", "_getDelegateConfig", "click", "_isWithActiveTrigger", "_enter", "_leave", "getTipElement", "isWithContent", "isInTheDom", "ownerDocument", "tipId", "getUID", "<PERSON><PERSON><PERSON><PERSON>", "animation", "attachment", "_getAttachment", "addAttachmentClass", "container", "INSERTED", "fallbackPlacement", "ARROW", "originalPlacement", "_handlePopperPlacementChange", "_fixTransition", "prevHoverState", "OUT", "_TRANSITION_DURATION", "_cleanTipClass", "HOVER", "getTitle", "template", "$tip", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TOOLTIP_INNER", "content", "html", "empty", "append", "text", "title", "triggers", "split", "for<PERSON>ach", "MANUAL", "eventIn", "eventOut", "FOCUSOUT", "_fixTitle", "titleType", "delay", "key", "tabClass", "join", "initConfigAnimation", "Popover", "_getContent", "TITLE", "CONTENT", "ScrollSpy", "OffsetMethod", "_scrollElement", "_selector", "NAV_LINKS", "LIST_ITEMS", "DROPDOWN_ITEMS", "_offsets", "_targets", "_activeTarget", "_scrollHeight", "SCROLL", "_process", "refresh", "autoMethod", "POSITION", "OFFSET", "offsetMethod", "method", "offsetBase", "_getScrollTop", "_getScrollHeight", "targets", "map", "targetSelector", "targetBCR", "height", "top", "item", "sort", "a", "b", "pageYOffset", "max", "_getOffsetHeight", "innerHeight", "maxScroll", "_activate", "_clear", "isActiveTarget", "queries", "$link", "DROPDOWN_ITEM", "DROPDOWN", "DROPDOWN_TOGGLE", "parents", "NAV_LIST_GROUP", "NAV_ITEMS", "ACTIVATE", "scrollSpys", "DATA_SPY", "$spy", "Tab", "previous", "listElement", "itemSelector", "nodeName", "ACTIVE_UL", "hiddenEvent", "activeElements", "active", "_transitionComplete", "dropdown<PERSON><PERSON>d", "DROPDOWN_ACTIVE_CHILD", "DROPDOWN_MENU", "dropdownElement", "version", "min<PERSON><PERSON><PERSON>", "ltMajor", "minMinor", "minPatch", "max<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;AAEA;;;;;;;AAOA,IAAMA,OAAQ,YAAM;;;;;;MASdC,aAAa,KAAjB;MAEMC,UAAU,OAAhB;MAEMC,qBAAqB;sBACN,qBADM;mBAEN,eAFM;iBAGN,+BAHM;gBAIN,eAJM;;GAA3B;;WAQSC,MAAT,CAAgBC,GAAhB,EAAqB;WACZ,GAAGC,QAAH,CAAYC,IAAZ,CAAiBF,GAAjB,EAAsBG,KAAtB,CAA4B,eAA5B,EAA6C,CAA7C,EAAgDC,WAAhD,EAAP;;;WAGOC,4BAAT,GAAwC;WAC/B;gBACKT,WAAWU,GADhB;oBAESV,WAAWU,GAFpB;YAAA,kBAGEC,KAHF,EAGS;YACRC,EAAED,MAAME,MAAR,EAAgBC,EAAhB,CAAmB,IAAnB,CAAJ,EAA8B;iBACrBH,MAAMI,SAAN,CAAgBC,OAAhB,CAAwBC,KAAxB,CAA8B,IAA9B,EAAoCC,SAApC,CAAP,CAD4B;;;eAGvBC,SAAP,CAJY;;KAHhB;;;WAYOC,iBAAT,GAA6B;QACvBC,OAAOC,KAAX,EAAkB;aACT,KAAP;;;QAGIC,KAAKC,SAASC,aAAT,CAAuB,WAAvB,CAAX;;SAEK,IAAMC,IAAX,IAAmBxB,kBAAnB,EAAuC;UACjC,OAAOqB,GAAGI,KAAH,CAASD,IAAT,CAAP,KAA0B,WAA9B,EAA2C;eAClC;eACAxB,mBAAmBwB,IAAnB;SADP;;;;WAMG,KAAP;;;WAGOE,qBAAT,CAA+BC,QAA/B,EAAyC;;;QACnCC,SAAS,KAAb;MAEE,IAAF,EAAQC,GAAR,CAAYhC,KAAKiC,cAAjB,EAAiC,YAAM;eAC5B,IAAT;KADF;eAIW,YAAM;UACX,CAACF,MAAL,EAAa;aACNG,oBAAL;;KAFJ,EAIGJ,QAJH;WAMO,IAAP;;;WAGOK,uBAAT,GAAmC;iBACpBd,mBAAb;MAEEe,EAAF,CAAKC,oBAAL,GAA4BR,qBAA5B;;QAEI7B,KAAKsC,qBAAL,EAAJ,EAAkC;QAC9B1B,KAAF,CAAQ2B,OAAR,CAAgBvC,KAAKiC,cAArB,IAAuCvB,8BAAvC;;;;;;;;;;MAWEV,OAAO;oBAEK,iBAFL;UAAA,kBAIJwC,MAJI,EAII;SACV;;kBAES,CAAC,EAAEC,KAAKC,MAAL,KAAgBxC,OAAlB,CAAX,CAFC;OAAH,QAGSuB,SAASkB,cAAT,CAAwBH,MAAxB,CAHT;;aAIOA,MAAP;KATS;0BAAA,kCAYYI,OAZZ,EAYqB;UAC1BC,WAAWD,QAAQE,YAAR,CAAqB,aAArB,CAAf;;UACI,CAACD,QAAD,IAAaA,aAAa,GAA9B,EAAmC;mBACtBD,QAAQE,YAAR,CAAqB,MAArB,KAAgC,EAA3C;;;UAGE;YACIC,YAAYlC,EAAEY,QAAF,EAAYuB,IAAZ,CAAiBH,QAAjB,CAAlB;eACOE,UAAUE,MAAV,GAAmB,CAAnB,GAAuBJ,QAAvB,GAAkC,IAAzC;OAFF,CAGE,OAAOK,KAAP,EAAc;eACP,IAAP;;KAtBO;UAAA,kBA0BJN,OA1BI,EA0BK;aACPA,QAAQO,YAAf;KA3BS;wBAAA,gCA8BUP,OA9BV,EA8BmB;QAC1BA,OAAF,EAAWQ,OAAX,CAAmBnD,WAAWU,GAA9B;KA/BS;yBAAA,mCAkCa;aACf0C,QAAQpD,UAAR,CAAP;KAnCS;aAAA,qBAsCDI,GAtCC,EAsCI;aACN,CAACA,IAAI,CAAJ,KAAUA,GAAX,EAAgBiD,QAAvB;KAvCS;mBAAA,2BA0CKC,aA1CL,EA0CoBC,MA1CpB,EA0C4BC,WA1C5B,EA0CyC;WAC7C,IAAMC,QAAX,IAAuBD,WAAvB,EAAoC;YAC9BE,OAAOC,SAAP,CAAiBC,cAAjB,CAAgCtD,IAAhC,CAAqCkD,WAArC,EAAkDC,QAAlD,CAAJ,EAAiE;cACzDI,gBAAgBL,YAAYC,QAAZ,CAAtB;cACMK,QAAgBP,OAAOE,QAAP,CAAtB;cACMM,YAAgBD,SAAS/D,KAAKiE,SAAL,CAAeF,KAAf,CAAT,GACA,SADA,GACY3D,OAAO2D,KAAP,CADlC;;cAGI,CAAC,IAAIG,MAAJ,CAAWJ,aAAX,EAA0BK,IAA1B,CAA+BH,SAA/B,CAAL,EAAgD;kBACxC,IAAII,KAAJ,CACDb,cAAcc,WAAd,EAAH,yBACWX,QADX,2BACuCM,SADvC,sCAEsBF,aAFtB,SADI,CAAN;;;;;GAnDV;;SA+DO9D,IAAP;CAxJW,CA0JVa,CA1JU,CAAb;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACLA;;;;;;;AAOA,IAAMyD,QAAS,YAAM;;;;;;MASbC,OAAsB,OAA5B;MACMC,UAAsB,cAA5B;MACMC,WAAsB,UAA5B;MACMC,kBAA0BD,QAAhC;MACME,eAAsB,WAA5B;MACMC,qBAAsB/D,EAAEuB,EAAF,CAAKmC,IAAL,CAA5B;MACMM,sBAAsB,GAA5B;MAEMC,WAAW;aACL;GADZ;MAIMC,QAAQ;qBACaL,SADb;uBAEcA,SAFd;8BAGaA,SAAzB,GAAqCC;GAHvC;MAMMK,YAAY;WACR,OADQ;UAER,MAFQ;UAGR;;;;;;;GAHV;;MAaMV,KAxCa;;;mBA0CL1B,OAAZ,EAAqB;WACdqC,QAAL,GAAgBrC,OAAhB;KA3Ce;;;;;;WAwDjBsC,KAxDiB,kBAwDXtC,OAxDW,EAwDF;gBACHA,WAAW,KAAKqC,QAA1B;;UAEME,cAAc,KAAKC,eAAL,CAAqBxC,OAArB,CAApB;;UACMyC,cAAc,KAAKC,kBAAL,CAAwBH,WAAxB,CAApB;;UAEIE,YAAYE,kBAAZ,EAAJ,EAAsC;;;;WAIjCC,cAAL,CAAoBL,WAApB;KAlEe;;WAqEjBM,OArEiB,sBAqEP;QACNC,UAAF,CAAa,KAAKT,QAAlB,EAA4BR,QAA5B;WACKQ,QAAL,GAAgB,IAAhB;KAvEe;;;WA6EjBG,eA7EiB,4BA6EDxC,OA7EC,EA6EQ;UACjBC,WAAW7C,KAAK2F,sBAAL,CAA4B/C,OAA5B,CAAjB;UACIgD,SAAa,KAAjB;;UAEI/C,QAAJ,EAAc;iBACHhC,EAAEgC,QAAF,EAAY,CAAZ,CAAT;;;UAGE,CAAC+C,MAAL,EAAa;iBACF/E,EAAE+B,OAAF,EAAWiD,OAAX,OAAuBb,UAAUc,KAAjC,EAA0C,CAA1C,CAAT;;;aAGKF,MAAP;KAzFe;;WA4FjBN,kBA5FiB,+BA4FE1C,OA5FF,EA4FW;UACpBmD,aAAalF,EAAEkE,KAAF,CAAQA,MAAMiB,KAAd,CAAnB;QAEEpD,OAAF,EAAWQ,OAAX,CAAmB2C,UAAnB;aACOA,UAAP;KAhGe;;WAmGjBP,cAnGiB,2BAmGF5C,OAnGE,EAmGO;;;QACpBA,OAAF,EAAWqD,WAAX,CAAuBjB,UAAUkB,IAAjC;;UAEI,CAAClG,KAAKsC,qBAAL,EAAD,IACA,CAACzB,EAAE+B,OAAF,EAAWuD,QAAX,CAAoBnB,UAAUoB,IAA9B,CADL,EAC0C;aACnCC,eAAL,CAAqBzD,OAArB;;;;;QAIAA,OAAF,EACGZ,GADH,CACOhC,KAAKiC,cADZ,EAC4B,UAACrB,KAAD;eAAW,MAAKyF,eAAL,CAAqBzD,OAArB,EAA8BhC,KAA9B,CAAX;OAD5B,EAEGyB,oBAFH,CAEwBwC,mBAFxB;KA5Ge;;WAiHjBwB,eAjHiB,4BAiHDzD,OAjHC,EAiHQ;QACrBA,OAAF,EACG0D,MADH,GAEGlD,OAFH,CAEW2B,MAAMwB,MAFjB,EAGGC,MAHH;KAlHe;;;UA2HVC,gBA3HU,6BA2HOjD,MA3HP,EA2He;aACvB,KAAKkD,IAAL,CAAU,YAAY;YACrBC,WAAW9F,EAAE,IAAF,CAAjB;YACI+F,OAAaD,SAASC,IAAT,CAAcnC,QAAd,CAAjB;;YAEI,CAACmC,IAAL,EAAW;iBACF,IAAItC,KAAJ,CAAU,IAAV,CAAP;mBACSsC,IAAT,CAAcnC,QAAd,EAAwBmC,IAAxB;;;YAGEpD,WAAW,OAAf,EAAwB;eACjBA,MAAL,EAAa,IAAb;;OAVG,CAAP;KA5He;;UA2IVqD,cA3IU,2BA2IKC,aA3IL,EA2IoB;aAC5B,UAAUlG,KAAV,EAAiB;YAClBA,KAAJ,EAAW;gBACHmG,cAAN;;;sBAGY7B,KAAd,CAAoB,IAApB;OALF;KA5Ie;;;;0BAiDI;eACZV,OAAP;;;;;;;;;;;;IA4GF/C,QAAF,EAAYuF,EAAZ,CACEjC,MAAMkC,cADR,EAEEnC,SAASoC,OAFX,EAGE5C,MAAMuC,cAAN,CAAqB,IAAIvC,KAAJ,EAArB,CAHF;;;;;;;IAaElC,EAAF,CAAKmC,IAAL,IAAyBD,MAAMmC,gBAA/B;IACErE,EAAF,CAAKmC,IAAL,EAAW4C,WAAX,GAAyB7C,KAAzB;;IACElC,EAAF,CAAKmC,IAAL,EAAW6C,UAAX,GAAyB,YAAY;MACjChF,EAAF,CAAKmC,IAAL,IAAaK,kBAAb;WACON,MAAMmC,gBAAb;GAFF;;SAKOnC,KAAP;CAlLY,CAoLXzD,CApLW,CAAd;;ACVA;;;;;;;AAOA,IAAMwG,SAAU,YAAM;;;;;;MASd9C,OAAsB,QAA5B;MACMC,UAAsB,cAA5B;MACMC,WAAsB,WAA5B;MACMC,kBAA0BD,QAAhC;MACME,eAAsB,WAA5B;MACMC,qBAAsB/D,EAAEuB,EAAF,CAAKmC,IAAL,CAA5B;MAEMS,YAAY;YACP,QADO;YAEP,KAFO;WAGP;GAHX;MAMMF,WAAW;wBACM,yBADN;iBAEM,yBAFN;WAGM,OAHN;YAIM,SAJN;YAKM;GALvB;MAQMC,QAAQ;8BACkBL,SAA9B,GAA0CC,YAD9B;yBAEU,UAAQD,SAAR,GAAoBC,YAApB,mBACOD,SADP,GACmBC,YADnB;;;;;;;GAFxB;;MAaM0C,MA3Cc;;;oBA6CNzE,OAAZ,EAAqB;WACdqC,QAAL,GAAgBrC,OAAhB;KA9CgB;;;;;;WA2DlB0E,MA3DkB,qBA2DT;UACHC,qBAAqB,IAAzB;UACIC,iBAAiB,IAArB;UACMrC,cAAmBtE,EAAE,KAAKoE,QAAP,EAAiBY,OAAjB,CACvBf,SAAS2C,WADc,EAEvB,CAFuB,CAAzB;;UAIItC,WAAJ,EAAiB;YACTuC,QAAQ7G,EAAE,KAAKoE,QAAP,EAAiBjC,IAAjB,CAAsB8B,SAAS6C,KAA/B,EAAsC,CAAtC,CAAd;;YAEID,KAAJ,EAAW;cACLA,MAAME,IAAN,KAAe,OAAnB,EAA4B;gBACtBF,MAAMG,OAAN,IACFhH,EAAE,KAAKoE,QAAP,EAAiBkB,QAAjB,CAA0BnB,UAAU8C,MAApC,CADF,EAC+C;mCACxB,KAArB;aAFF,MAIO;kBACCC,gBAAgBlH,EAAEsE,WAAF,EAAenC,IAAf,CAAoB8B,SAASgD,MAA7B,EAAqC,CAArC,CAAtB;;kBAEIC,aAAJ,EAAmB;kBACfA,aAAF,EAAiB9B,WAAjB,CAA6BjB,UAAU8C,MAAvC;;;;;cAKFP,kBAAJ,EAAwB;gBAClBG,MAAMM,YAAN,CAAmB,UAAnB,KACF7C,YAAY6C,YAAZ,CAAyB,UAAzB,CADE,IAEFN,MAAMO,SAAN,CAAgBC,QAAhB,CAAyB,UAAzB,CAFE,IAGF/C,YAAY8C,SAAZ,CAAsBC,QAAtB,CAA+B,UAA/B,CAHF,EAG8C;;;;kBAGxCL,OAAN,GAAgB,CAAChH,EAAE,KAAKoE,QAAP,EAAiBkB,QAAjB,CAA0BnB,UAAU8C,MAApC,CAAjB;cACEJ,KAAF,EAAStE,OAAT,CAAiB,QAAjB;;;gBAGI+E,KAAN;2BACiB,KAAjB;;;;UAKAX,cAAJ,EAAoB;aACbvC,QAAL,CAAcmD,YAAd,CAA2B,cAA3B,EACE,CAACvH,EAAE,KAAKoE,QAAP,EAAiBkB,QAAjB,CAA0BnB,UAAU8C,MAApC,CADH;;;UAIEP,kBAAJ,EAAwB;UACpB,KAAKtC,QAAP,EAAiBoD,WAAjB,CAA6BrD,UAAU8C,MAAvC;;KA3Gc;;WA+GlBrC,OA/GkB,sBA+GR;QACNC,UAAF,CAAa,KAAKT,QAAlB,EAA4BR,QAA5B;WACKQ,QAAL,GAAgB,IAAhB;KAjHgB;;;WAuHXwB,gBAvHW,6BAuHMjD,MAvHN,EAuHc;aACvB,KAAKkD,IAAL,CAAU,YAAY;YACvBE,OAAO/F,EAAE,IAAF,EAAQ+F,IAAR,CAAanC,QAAb,CAAX;;YAEI,CAACmC,IAAL,EAAW;iBACF,IAAIS,MAAJ,CAAW,IAAX,CAAP;YACE,IAAF,EAAQT,IAAR,CAAanC,QAAb,EAAuBmC,IAAvB;;;YAGEpD,WAAW,QAAf,EAAyB;eAClBA,MAAL;;OATG,CAAP;KAxHgB;;;;0BAoDG;eACZgB,OAAP;;;;;;;;;;;;IA0FF/C,QAAF,EACGuF,EADH,CACMjC,MAAMkC,cADZ,EAC4BnC,SAASwD,kBADrC,EACyD,UAAC1H,KAAD,EAAW;UAC1DmG,cAAN;QAEIwB,SAAS3H,MAAME,MAAnB;;QAEI,CAACD,EAAE0H,MAAF,EAAUpC,QAAV,CAAmBnB,UAAUwD,MAA7B,CAAL,EAA2C;eAChC3H,EAAE0H,MAAF,EAAU1C,OAAV,CAAkBf,SAAS0D,MAA3B,CAAT;;;WAGK/B,gBAAP,CAAwBlG,IAAxB,CAA6BM,EAAE0H,MAAF,CAA7B,EAAwC,QAAxC;GAVJ,EAYGvB,EAZH,CAYMjC,MAAM0D,mBAZZ,EAYiC3D,SAASwD,kBAZ1C,EAY8D,UAAC1H,KAAD,EAAW;QAC/D2H,SAAS1H,EAAED,MAAME,MAAR,EAAgB+E,OAAhB,CAAwBf,SAAS0D,MAAjC,EAAyC,CAAzC,CAAf;MACED,MAAF,EAAUF,WAAV,CAAsBrD,UAAU0D,KAAhC,EAAuC,eAAevE,IAAf,CAAoBvD,MAAMgH,IAA1B,CAAvC;GAdJ;;;;;;;IAwBExF,EAAF,CAAKmC,IAAL,IAAyB8C,OAAOZ,gBAAhC;IACErE,EAAF,CAAKmC,IAAL,EAAW4C,WAAX,GAAyBE,MAAzB;;IACEjF,EAAF,CAAKmC,IAAL,EAAW6C,UAAX,GAAyB,YAAY;MACjChF,EAAF,CAAKmC,IAAL,IAAaK,kBAAb;WACOyC,OAAOZ,gBAAd;GAFF;;SAKOY,MAAP;CA9Ka,CAgLZxG,CAhLY,CAAf;;ACJA;;;;;;;AAOA,IAAM8H,WAAY,YAAM;;;;;;MAShBpE,OAAyB,UAA/B;MACMC,UAAyB,cAA/B;MACMC,WAAyB,aAA/B;MACMC,kBAA6BD,QAAnC;MACME,eAAyB,WAA/B;MACMC,qBAAyB/D,EAAEuB,EAAF,CAAKmC,IAAL,CAA/B;MACMM,sBAAyB,GAA/B;MACM+D,qBAAyB,EAA/B,CAhBsB;;MAiBhBC,sBAAyB,EAA/B,CAjBsB;;MAkBhBC,yBAAyB,GAA/B,CAlBsB;;MAoBhBC,UAAU;cACH,IADG;cAEH,IAFG;WAGH,KAHG;WAIH,OAJG;UAKH;GALb;MAQMC,cAAc;cACP,kBADO;cAEP,SAFO;WAGP,kBAHO;WAIP,kBAJO;UAKP;GALb;MAQMC,YAAY;UACL,MADK;UAEL,MAFK;UAGL,MAHK;WAIL;GAJb;MAOMlE,QAAQ;qBACaL,SADb;mBAEYA,SAFZ;yBAGeA,SAHf;+BAIkBA,SAJlB;+BAKkBA,SALlB;2BAMgBA,SANhB;4BAOYA,SAAxB,GAAoCC,YAPxB;8BAQaD,SAAzB,GAAqCC;GARvC;MAWMK,YAAY;cACL,UADK;YAEL,QAFK;WAGL,OAHK;WAIL,qBAJK;UAKL,oBALK;UAML,oBANK;UAOL,oBAPK;UAQL;GARb;MAWMF,WAAW;YACD,SADC;iBAED,uBAFC;UAGD,gBAHC;eAID,0CAJC;gBAKD,sBALC;gBAMD,+BANC;eAOD;;;;;;;GAPhB;;MAiBM6D,QAlFgB;;;sBAoFR/F,OAAZ,EAAqBY,MAArB,EAA6B;WACtB0F,MAAL,GAA0B,IAA1B;WACKC,SAAL,GAA0B,IAA1B;WACKC,cAAL,GAA0B,IAA1B;WAEKC,SAAL,GAA0B,KAA1B;WACKC,UAAL,GAA0B,KAA1B;WAEKC,YAAL,GAA0B,IAA1B;WAEKC,OAAL,GAA0B,KAAKC,UAAL,CAAgBjG,MAAhB,CAA1B;WACKyB,QAAL,GAA0BpE,EAAE+B,OAAF,EAAW,CAAX,CAA1B;WACK8G,kBAAL,GAA0B7I,EAAE,KAAKoE,QAAP,EAAiBjC,IAAjB,CAAsB8B,SAAS6E,UAA/B,EAA2C,CAA3C,CAA1B;;WAEKC,kBAAL;KAlGkB;;;;;;WAmHpBC,IAnHoB,mBAmHb;UACD,CAAC,KAAKP,UAAV,EAAsB;aACfQ,MAAL,CAAYb,UAAUc,IAAtB;;KArHgB;;WAyHpBC,eAzHoB,8BAyHF;;;UAGZ,CAACvI,SAASwI,MAAV,IACDpJ,EAAE,KAAKoE,QAAP,EAAiBlE,EAAjB,CAAoB,UAApB,KAAmCF,EAAE,KAAKoE,QAAP,EAAiBiF,GAAjB,CAAqB,YAArB,MAAuC,QAD7E,EACwF;aACjFL,IAAL;;KA9HgB;;WAkIpBM,IAlIoB,mBAkIb;UACD,CAAC,KAAKb,UAAV,EAAsB;aACfQ,MAAL,CAAYb,UAAUmB,IAAtB;;KApIgB;;WAwIpBC,KAxIoB,kBAwIdzJ,KAxIc,EAwIP;UACP,CAACA,KAAL,EAAY;aACLyI,SAAL,GAAiB,IAAjB;;;UAGExI,EAAE,KAAKoE,QAAP,EAAiBjC,IAAjB,CAAsB8B,SAASwF,SAA/B,EAA0C,CAA1C,KACFtK,KAAKsC,qBAAL,EADF,EACgC;aACzBJ,oBAAL,CAA0B,KAAK+C,QAA/B;aACKsF,KAAL,CAAW,IAAX;;;oBAGY,KAAKpB,SAAnB;WACKA,SAAL,GAAiB,IAAjB;KApJkB;;WAuJpBoB,KAvJoB,kBAuJd3J,KAvJc,EAuJP;UACP,CAACA,KAAL,EAAY;aACLyI,SAAL,GAAiB,KAAjB;;;UAGE,KAAKF,SAAT,EAAoB;sBACJ,KAAKA,SAAnB;aACKA,SAAL,GAAiB,IAAjB;;;UAGE,KAAKK,OAAL,CAAagB,QAAb,IAAyB,CAAC,KAAKnB,SAAnC,EAA8C;aACvCF,SAAL,GAAiBsB,YACf,CAAChJ,SAASiJ,eAAT,GAA2B,KAAKV,eAAhC,GAAkD,KAAKH,IAAxD,EAA8Dc,IAA9D,CAAmE,IAAnE,CADe,EAEf,KAAKnB,OAAL,CAAagB,QAFE,CAAjB;;KAlKgB;;WAyKpBI,EAzKoB,eAyKjBC,KAzKiB,EAyKV;;;WACHzB,cAAL,GAAsBvI,EAAE,KAAKoE,QAAP,EAAiBjC,IAAjB,CAAsB8B,SAASgG,WAA/B,EAA4C,CAA5C,CAAtB;;UAEMC,cAAc,KAAKC,aAAL,CAAmB,KAAK5B,cAAxB,CAApB;;UAEIyB,QAAQ,KAAK3B,MAAL,CAAYjG,MAAZ,GAAqB,CAA7B,IAAkC4H,QAAQ,CAA9C,EAAiD;;;;UAI7C,KAAKvB,UAAT,EAAqB;UACjB,KAAKrE,QAAP,EAAiBjD,GAAjB,CAAqB+C,MAAMkG,IAA3B,EAAiC;iBAAM,MAAKL,EAAL,CAAQC,KAAR,CAAN;SAAjC;;;;UAIEE,gBAAgBF,KAApB,EAA2B;aACpBR,KAAL;aACKE,KAAL;;;;UAIIW,YAAYL,QAAQE,WAAR,GAChB9B,UAAUc,IADM,GAEhBd,UAAUmB,IAFZ;;WAIKN,MAAL,CAAYoB,SAAZ,EAAuB,KAAKhC,MAAL,CAAY2B,KAAZ,CAAvB;KAjMkB;;WAoMpBpF,OApMoB,sBAoMV;QACN,KAAKR,QAAP,EAAiBkG,GAAjB,CAAqBzG,SAArB;QACEgB,UAAF,CAAa,KAAKT,QAAlB,EAA4BR,QAA5B;WAEKyE,MAAL,GAA0B,IAA1B;WACKM,OAAL,GAA0B,IAA1B;WACKvE,QAAL,GAA0B,IAA1B;WACKkE,SAAL,GAA0B,IAA1B;WACKE,SAAL,GAA0B,IAA1B;WACKC,UAAL,GAA0B,IAA1B;WACKF,cAAL,GAA0B,IAA1B;WACKM,kBAAL,GAA0B,IAA1B;KA/MkB;;;WAqNpBD,UArNoB,uBAqNTjG,MArNS,EAqND;eACR3C,EAAEuK,MAAF,CAAS,EAAT,EAAarC,OAAb,EAAsBvF,MAAtB,CAAT;WACK6H,eAAL,CAAqB9G,IAArB,EAA2Bf,MAA3B,EAAmCwF,WAAnC;aACOxF,MAAP;KAxNkB;;WA2NpBoG,kBA3NoB,iCA2NC;;;UACf,KAAKJ,OAAL,CAAa8B,QAAjB,EAA2B;UACvB,KAAKrG,QAAP,EACG+B,EADH,CACMjC,MAAMwG,OADZ,EACqB,UAAC3K,KAAD;iBAAW,OAAK4K,QAAL,CAAc5K,KAAd,CAAX;SADrB;;;UAIE,KAAK4I,OAAL,CAAaa,KAAb,KAAuB,OAA3B,EAAoC;UAChC,KAAKpF,QAAP,EACG+B,EADH,CACMjC,MAAM0G,UADZ,EACwB,UAAC7K,KAAD;iBAAW,OAAKyJ,KAAL,CAAWzJ,KAAX,CAAX;SADxB,EAEGoG,EAFH,CAEMjC,MAAM2G,UAFZ,EAEwB,UAAC9K,KAAD;iBAAW,OAAK2J,KAAL,CAAW3J,KAAX,CAAX;SAFxB;;YAGI,kBAAkBa,SAASkK,eAA/B,EAAgD;;;;;;;;YAQ5C,KAAK1G,QAAP,EAAiB+B,EAAjB,CAAoBjC,MAAM6G,QAA1B,EAAoC,YAAM;mBACnCvB,KAAL;;gBACI,OAAKd,YAAT,EAAuB;2BACR,OAAKA,YAAlB;;;mBAEGA,YAAL,GAAoBsC,WAAW,UAACjL,KAAD;qBAAW,OAAK2J,KAAL,CAAW3J,KAAX,CAAX;aAAX,EAAyCkI,yBAAyB,OAAKU,OAAL,CAAagB,QAA/E,CAApB;WALF;;;KA7Oc;;WAwPpBgB,QAxPoB,qBAwPX5K,KAxPW,EAwPJ;UACV,kBAAkBuD,IAAlB,CAAuBvD,MAAME,MAAN,CAAagL,OAApC,CAAJ,EAAkD;;;;cAI1ClL,MAAMmL,KAAd;aACOnD,kBAAL;gBACQ7B,cAAN;eACKoD,IAAL;;;aAEGtB,mBAAL;gBACQ9B,cAAN;eACK8C,IAAL;;;;;;KApQc;;WA2QpBmB,aA3QoB,0BA2QNpI,OA3QM,EA2QG;WAChBsG,MAAL,GAAcrI,EAAEmL,SAAF,CAAYnL,EAAE+B,OAAF,EAAWgD,MAAX,GAAoB5C,IAApB,CAAyB8B,SAASmH,IAAlC,CAAZ,CAAd;aACO,KAAK/C,MAAL,CAAYgD,OAAZ,CAAoBtJ,OAApB,CAAP;KA7QkB;;WAgRpBuJ,mBAhRoB,gCAgRAjB,SAhRA,EAgRWnD,aAhRX,EAgR0B;UACtCqE,kBAAkBlB,cAAcjC,UAAUc,IAAhD;UACMsC,kBAAkBnB,cAAcjC,UAAUmB,IAAhD;;UACMW,cAAkB,KAAKC,aAAL,CAAmBjD,aAAnB,CAAxB;;UACMuE,gBAAkB,KAAKpD,MAAL,CAAYjG,MAAZ,GAAqB,CAA7C;UACMsJ,gBAAkBF,mBAAmBtB,gBAAgB,CAAnC,IACAqB,mBAAmBrB,gBAAgBuB,aAD3D;;UAGIC,iBAAiB,CAAC,KAAK/C,OAAL,CAAagD,IAAnC,EAAyC;eAChCzE,aAAP;;;UAGI0E,QAAYvB,cAAcjC,UAAUmB,IAAxB,GAA+B,CAAC,CAAhC,GAAoC,CAAtD;UACMsC,YAAY,CAAC3B,cAAc0B,KAAf,IAAwB,KAAKvD,MAAL,CAAYjG,MAAtD;aAEOyJ,cAAc,CAAC,CAAf,GACL,KAAKxD,MAAL,CAAY,KAAKA,MAAL,CAAYjG,MAAZ,GAAqB,CAAjC,CADK,GACiC,KAAKiG,MAAL,CAAYwD,SAAZ,CADxC;KA/RkB;;WAoSpBC,kBApSoB,+BAoSDC,aApSC,EAoScC,kBApSd,EAoSkC;UAC9CC,cAAc,KAAK9B,aAAL,CAAmB4B,aAAnB,CAApB;;UACMG,YAAY,KAAK/B,aAAL,CAAmBnK,EAAE,KAAKoE,QAAP,EAAiBjC,IAAjB,CAAsB8B,SAASgG,WAA/B,EAA4C,CAA5C,CAAnB,CAAlB;;UACMkC,aAAanM,EAAEkE,KAAF,CAAQA,MAAMkI,KAAd,EAAqB;oCAAA;mBAE3BJ,kBAF2B;cAGhCE,SAHgC;YAIlCD;OAJa,CAAnB;QAOE,KAAK7H,QAAP,EAAiB7B,OAAjB,CAAyB4J,UAAzB;aAEOA,UAAP;KAhTkB;;WAmTpBE,0BAnToB,uCAmTOtK,OAnTP,EAmTgB;UAC9B,KAAK8G,kBAAT,EAA6B;UACzB,KAAKA,kBAAP,EACG1G,IADH,CACQ8B,SAASgD,MADjB,EAEG7B,WAFH,CAEejB,UAAU8C,MAFzB;;YAIMqF,gBAAgB,KAAKzD,kBAAL,CAAwB0D,QAAxB,CACpB,KAAKpC,aAAL,CAAmBpI,OAAnB,CADoB,CAAtB;;YAIIuK,aAAJ,EAAmB;YACfA,aAAF,EAAiBE,QAAjB,CAA0BrI,UAAU8C,MAApC;;;KA9Tc;;WAmUpBgC,MAnUoB,mBAmUboB,SAnUa,EAmUFtI,OAnUE,EAmUO;;;UACnBmF,gBAAgBlH,EAAE,KAAKoE,QAAP,EAAiBjC,IAAjB,CAAsB8B,SAASgG,WAA/B,EAA4C,CAA5C,CAAtB;;UACMwC,qBAAqB,KAAKtC,aAAL,CAAmBjD,aAAnB,CAA3B;;UACMwF,cAAgB3K,WAAWmF,iBAC/B,KAAKoE,mBAAL,CAAyBjB,SAAzB,EAAoCnD,aAApC,CADF;;UAEMyF,mBAAmB,KAAKxC,aAAL,CAAmBuC,WAAnB,CAAzB;;UACME,YAAYpK,QAAQ,KAAK8F,SAAb,CAAlB;UAEIuE,oBAAJ;UACIC,cAAJ;UACId,kBAAJ;;UAEI3B,cAAcjC,UAAUc,IAA5B,EAAkC;+BACT/E,UAAU4I,IAAjC;yBACiB5I,UAAU+E,IAA3B;6BACqBd,UAAU2E,IAA/B;OAHF,MAIO;+BACkB5I,UAAU6I,KAAjC;yBACiB7I,UAAUoF,IAA3B;6BACqBnB,UAAU4E,KAA/B;;;UAGEN,eAAe1M,EAAE0M,WAAF,EAAepH,QAAf,CAAwBnB,UAAU8C,MAAlC,CAAnB,EAA8D;aACvDwB,UAAL,GAAkB,KAAlB;;;;UAII0D,aAAa,KAAKL,kBAAL,CAAwBY,WAAxB,EAAqCV,kBAArC,CAAnB;;UACIG,WAAWzH,kBAAX,EAAJ,EAAqC;;;;UAIjC,CAACwC,aAAD,IAAkB,CAACwF,WAAvB,EAAoC;;;;;WAK/BjE,UAAL,GAAkB,IAAlB;;UAEImE,SAAJ,EAAe;aACRpD,KAAL;;;WAGG6C,0BAAL,CAAgCK,WAAhC;;UAEMO,YAAYjN,EAAEkE,KAAF,CAAQA,MAAMkG,IAAd,EAAoB;uBACrBsC,WADqB;mBAEzBV,kBAFyB;cAG9BS,kBAH8B;YAIhCE;OAJY,CAAlB;;UAOIxN,KAAKsC,qBAAL,MACFzB,EAAE,KAAKoE,QAAP,EAAiBkB,QAAjB,CAA0BnB,UAAUiI,KAApC,CADF,EAC8C;UAE1CM,WAAF,EAAeF,QAAf,CAAwBM,cAAxB;aAEKI,MAAL,CAAYR,WAAZ;UAEExF,aAAF,EAAiBsF,QAAjB,CAA0BK,oBAA1B;UACEH,WAAF,EAAeF,QAAf,CAAwBK,oBAAxB;UAEE3F,aAAF,EACG/F,GADH,CACOhC,KAAKiC,cADZ,EAC4B,YAAM;YAC5BsL,WAAF,EACGtH,WADH,CACkByH,oBADlB,SAC0CC,cAD1C,EAEGN,QAFH,CAEYrI,UAAU8C,MAFtB;YAIEC,aAAF,EAAiB9B,WAAjB,CAAgCjB,UAAU8C,MAA1C,SAAoD6F,cAApD,SAAsED,oBAAtE;iBAEKpE,UAAL,GAAkB,KAAlB;qBAEW;mBAAMzI,EAAE,OAAKoE,QAAP,EAAiB7B,OAAjB,CAAyB0K,SAAzB,CAAN;WAAX,EAAsD,CAAtD;SAVJ,EAaGzL,oBAbH,CAawBwC,mBAbxB;OAVF,MAyBO;UACHkD,aAAF,EAAiB9B,WAAjB,CAA6BjB,UAAU8C,MAAvC;UACEyF,WAAF,EAAeF,QAAf,CAAwBrI,UAAU8C,MAAlC;aAEKwB,UAAL,GAAkB,KAAlB;UACE,KAAKrE,QAAP,EAAiB7B,OAAjB,CAAyB0K,SAAzB;;;UAGEL,SAAJ,EAAe;aACRlD,KAAL;;KAzZgB;;;aAgab9D,gBAhaa,6BAgaIjD,MAhaJ,EAgaY;aACvB,KAAKkD,IAAL,CAAU,YAAY;YACvBE,OAAY/F,EAAE,IAAF,EAAQ+F,IAAR,CAAanC,QAAb,CAAhB;;YACM+E,UAAU3I,EAAEuK,MAAF,CAAS,EAAT,EAAarC,OAAb,EAAsBlI,EAAE,IAAF,EAAQ+F,IAAR,EAAtB,CAAhB;;YAEI,OAAOpD,MAAP,KAAkB,QAAtB,EAAgC;YAC5B4H,MAAF,CAAS5B,OAAT,EAAkBhG,MAAlB;;;YAGIwK,SAAS,OAAOxK,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsCgG,QAAQyE,KAA7D;;YAEI,CAACrH,IAAL,EAAW;iBACF,IAAI+B,QAAJ,CAAa,IAAb,EAAmBa,OAAnB,CAAP;YACE,IAAF,EAAQ5C,IAAR,CAAanC,QAAb,EAAuBmC,IAAvB;;;YAGE,OAAOpD,MAAP,KAAkB,QAAtB,EAAgC;eACzBoH,EAAL,CAAQpH,MAAR;SADF,MAEO,IAAI,OAAOwK,MAAP,KAAkB,QAAtB,EAAgC;cACjC,OAAOpH,KAAKoH,MAAL,CAAP,KAAwB,WAA5B,EAAyC;kBACjC,IAAI5J,KAAJ,wBAA8B4J,MAA9B,QAAN;;;eAEGA,MAAL;SAJK,MAKA,IAAIxE,QAAQgB,QAAZ,EAAsB;eACtBH,KAAL;eACKE,KAAL;;OAxBG,CAAP;KAjakB;;aA8bb2D,oBA9ba,iCA8bQtN,KA9bR,EA8be;UAC3BiC,WAAW7C,KAAK2F,sBAAL,CAA4B,IAA5B,CAAjB;;UAEI,CAAC9C,QAAL,EAAe;;;;UAIT/B,SAASD,EAAEgC,QAAF,EAAY,CAAZ,CAAf;;UAEI,CAAC/B,MAAD,IAAW,CAACD,EAAEC,MAAF,EAAUqF,QAAV,CAAmBnB,UAAUmJ,QAA7B,CAAhB,EAAwD;;;;UAIlD3K,SAAa3C,EAAEuK,MAAF,CAAS,EAAT,EAAavK,EAAEC,MAAF,EAAU8F,IAAV,EAAb,EAA+B/F,EAAE,IAAF,EAAQ+F,IAAR,EAA/B,CAAnB;UACMwH,aAAa,KAAKtL,YAAL,CAAkB,eAAlB,CAAnB;;UAEIsL,UAAJ,EAAgB;eACP5D,QAAP,GAAkB,KAAlB;;;eAGO/D,gBAAT,CAA0BlG,IAA1B,CAA+BM,EAAEC,MAAF,CAA/B,EAA0C0C,MAA1C;;UAEI4K,UAAJ,EAAgB;UACZtN,MAAF,EAAU8F,IAAV,CAAenC,QAAf,EAAyBmG,EAAzB,CAA4BwD,UAA5B;;;YAGIrH,cAAN;KAxdkB;;;;0BAwGC;eACZvC,OAAP;;;;0BAGmB;eACZuE,OAAP;;;;;;;;;;;;IAuXFtH,QAAF,EACGuF,EADH,CACMjC,MAAMkC,cADZ,EAC4BnC,SAASuJ,UADrC,EACiD1F,SAASuF,oBAD1D;IAGE5M,MAAF,EAAU0F,EAAV,CAAajC,MAAMuJ,aAAnB,EAAkC,YAAM;MACpCxJ,SAASyJ,SAAX,EAAsB7H,IAAtB,CAA2B,YAAY;UAC/B8H,YAAY3N,EAAE,IAAF,CAAlB;;eACS4F,gBAAT,CAA0BlG,IAA1B,CAA+BiO,SAA/B,EAA0CA,UAAU5H,IAAV,EAA1C;KAFF;GADF;;;;;;;IAcExE,EAAF,CAAKmC,IAAL,IAAyBoE,SAASlC,gBAAlC;IACErE,EAAF,CAAKmC,IAAL,EAAW4C,WAAX,GAAyBwB,QAAzB;;IACEvG,EAAF,CAAKmC,IAAL,EAAW6C,UAAX,GAAyB,YAAY;MACjChF,EAAF,CAAKmC,IAAL,IAAaK,kBAAb;WACO+D,SAASlC,gBAAhB;GAFF;;SAKOkC,QAAP;CA5fe,CA8fd9H,CA9fc,CAAjB;;ACPA;;;;;;;AAOA,IAAM4N,WAAY,YAAM;;;;;;MAShBlK,OAAsB,UAA5B;MACMC,UAAsB,cAA5B;MACMC,WAAsB,aAA5B;MACMC,kBAA0BD,QAAhC;MACME,eAAsB,WAA5B;MACMC,qBAAsB/D,EAAEuB,EAAF,CAAKmC,IAAL,CAA5B;MACMM,sBAAsB,GAA5B;MAEMkE,UAAU;YACL,IADK;YAEL;GAFX;MAKMC,cAAc;YACT,SADS;YAET;GAFX;MAKMjE,QAAQ;mBACYL,SADZ;qBAEaA,SAFb;mBAGYA,SAHZ;uBAIcA,SAJd;8BAKaA,SAAzB,GAAqCC;GALvC;MAQMK,YAAY;UACH,MADG;cAEH,UAFG;gBAGH,YAHG;eAIH;GAJf;MAOM0J,YAAY;WACP,OADO;YAEP;GAFX;MAKM5J,WAAW;aACD,oBADC;iBAED;;;;;;;GAFhB;;MAYM2J,QA3DgB;;;sBA6DR7L,OAAZ,EAAqBY,MAArB,EAA6B;WACtBmL,gBAAL,GAAwB,KAAxB;WACK1J,QAAL,GAAwBrC,OAAxB;WACK4G,OAAL,GAAwB,KAAKC,UAAL,CAAgBjG,MAAhB,CAAxB;WACKoL,aAAL,GAAwB/N,EAAEmL,SAAF,CAAYnL,EAClC,wCAAmC+B,QAAQiM,EAA3C,4DAC0CjM,QAAQiM,EADlD,SADkC,CAAZ,CAAxB;UAIMC,aAAajO,EAAEiE,SAAS2C,WAAX,CAAnB;;WACK,IAAIsH,IAAI,CAAb,EAAgBA,IAAID,WAAW7L,MAA/B,EAAuC8L,GAAvC,EAA4C;YACpCC,OAAOF,WAAWC,CAAX,CAAb;YACMlM,WAAW7C,KAAK2F,sBAAL,CAA4BqJ,IAA5B,CAAjB;;YACInM,aAAa,IAAb,IAAqBhC,EAAEgC,QAAF,EAAYoM,MAAZ,CAAmBrM,OAAnB,EAA4BK,MAA5B,GAAqC,CAA9D,EAAiE;eAC1D2L,aAAL,CAAmBM,IAAnB,CAAwBF,IAAxB;;;;WAICG,OAAL,GAAe,KAAK3F,OAAL,CAAa5D,MAAb,GAAsB,KAAKwJ,UAAL,EAAtB,GAA0C,IAAzD;;UAEI,CAAC,KAAK5F,OAAL,CAAa5D,MAAlB,EAA0B;aACnByJ,yBAAL,CAA+B,KAAKpK,QAApC,EAA8C,KAAK2J,aAAnD;;;UAGE,KAAKpF,OAAL,CAAalC,MAAjB,EAAyB;aAClBA,MAAL;;KArFgB;;;;;;WAuGpBA,MAvGoB,qBAuGX;UACHzG,EAAE,KAAKoE,QAAP,EAAiBkB,QAAjB,CAA0BnB,UAAUkB,IAApC,CAAJ,EAA+C;aACxCoJ,IAAL;OADF,MAEO;aACAC,IAAL;;KA3GgB;;WA+GpBA,IA/GoB,mBA+Gb;;;UACD,KAAKZ,gBAAL,IACF9N,EAAE,KAAKoE,QAAP,EAAiBkB,QAAjB,CAA0BnB,UAAUkB,IAApC,CADF,EAC6C;;;;UAIzCsJ,OAAJ;UACIC,WAAJ;;UAEI,KAAKN,OAAT,EAAkB;kBACNtO,EAAEmL,SAAF,CAAYnL,EAAE,KAAKsO,OAAP,EAAgB/B,QAAhB,GAA2BA,QAA3B,CAAoCtI,SAAS4K,OAA7C,CAAZ,CAAV;;YACI,CAACF,QAAQvM,MAAb,EAAqB;oBACT,IAAV;;;;UAIAuM,OAAJ,EAAa;sBACG3O,EAAE2O,OAAF,EAAW5I,IAAX,CAAgBnC,QAAhB,CAAd;;YACIgL,eAAeA,YAAYd,gBAA/B,EAAiD;;;;;UAK7CgB,aAAa9O,EAAEkE,KAAF,CAAQA,MAAMmB,IAAd,CAAnB;QACE,KAAKjB,QAAP,EAAiB7B,OAAjB,CAAyBuM,UAAzB;;UACIA,WAAWpK,kBAAX,EAAJ,EAAqC;;;;UAIjCiK,OAAJ,EAAa;iBACF/I,gBAAT,CAA0BlG,IAA1B,CAA+BM,EAAE2O,OAAF,CAA/B,EAA2C,MAA3C;;YACI,CAACC,WAAL,EAAkB;YACdD,OAAF,EAAW5I,IAAX,CAAgBnC,QAAhB,EAA0B,IAA1B;;;;UAIEmL,YAAY,KAAKC,aAAL,EAAlB;;QAEE,KAAK5K,QAAP,EACGgB,WADH,CACejB,UAAU8K,QADzB,EAEGzC,QAFH,CAEYrI,UAAU+K,UAFtB;WAIK9K,QAAL,CAAcrD,KAAd,CAAoBgO,SAApB,IAAiC,CAAjC;;UAEI,KAAKhB,aAAL,CAAmB3L,MAAvB,EAA+B;UAC3B,KAAK2L,aAAP,EACG3I,WADH,CACejB,UAAUgL,SADzB,EAEGC,IAFH,CAEQ,eAFR,EAEyB,IAFzB;;;WAKGC,gBAAL,CAAsB,IAAtB;;UAEMC,WAAW,SAAXA,QAAW,GAAM;UACnB,MAAKlL,QAAP,EACGgB,WADH,CACejB,UAAU+K,UADzB,EAEG1C,QAFH,CAEYrI,UAAU8K,QAFtB,EAGGzC,QAHH,CAGYrI,UAAUkB,IAHtB;cAKKjB,QAAL,CAAcrD,KAAd,CAAoBgO,SAApB,IAAiC,EAAjC;;cAEKM,gBAAL,CAAsB,KAAtB;;UAEE,MAAKjL,QAAP,EAAiB7B,OAAjB,CAAyB2B,MAAMqL,KAA/B;OAVF;;UAaI,CAACpQ,KAAKsC,qBAAL,EAAL,EAAmC;;;;;UAK7B+N,uBAAuBT,UAAU,CAAV,EAAavL,WAAb,KAA6BuL,UAAUU,KAAV,CAAgB,CAAhB,CAA1D;UACMC,wBAAgCF,oBAAtC;QAEE,KAAKpL,QAAP,EACGjD,GADH,CACOhC,KAAKiC,cADZ,EAC4BkO,QAD5B,EAEG9N,oBAFH,CAEwBwC,mBAFxB;WAIKI,QAAL,CAAcrD,KAAd,CAAoBgO,SAApB,IAAoC,KAAK3K,QAAL,CAAcsL,UAAd,CAApC;KA5LkB;;WA+LpBjB,IA/LoB,mBA+Lb;;;UACD,KAAKX,gBAAL,IACF,CAAC9N,EAAE,KAAKoE,QAAP,EAAiBkB,QAAjB,CAA0BnB,UAAUkB,IAApC,CADH,EAC8C;;;;UAIxCyJ,aAAa9O,EAAEkE,KAAF,CAAQA,MAAMyL,IAAd,CAAnB;QACE,KAAKvL,QAAP,EAAiB7B,OAAjB,CAAyBuM,UAAzB;;UACIA,WAAWpK,kBAAX,EAAJ,EAAqC;;;;UAI/BqK,YAAkB,KAAKC,aAAL,EAAxB;;WAEK5K,QAAL,CAAcrD,KAAd,CAAoBgO,SAApB,IAAoC,KAAK3K,QAAL,CAAcwL,qBAAd,GAAsCb,SAAtC,CAApC;WAEK7B,MAAL,CAAY,KAAK9I,QAAjB;QAEE,KAAKA,QAAP,EACGoI,QADH,CACYrI,UAAU+K,UADtB,EAEG9J,WAFH,CAEejB,UAAU8K,QAFzB,EAGG7J,WAHH,CAGejB,UAAUkB,IAHzB;;UAKI,KAAK0I,aAAL,CAAmB3L,MAAvB,EAA+B;aACxB,IAAI8L,IAAI,CAAb,EAAgBA,IAAI,KAAKH,aAAL,CAAmB3L,MAAvC,EAA+C8L,GAA/C,EAAoD;cAC5C3L,UAAU,KAAKwL,aAAL,CAAmBG,CAAnB,CAAhB;cACMlM,WAAW7C,KAAK2F,sBAAL,CAA4BvC,OAA5B,CAAjB;;cACIP,aAAa,IAAjB,EAAuB;gBACf6N,QAAQ7P,EAAEgC,QAAF,CAAd;;gBACI,CAAC6N,MAAMvK,QAAN,CAAenB,UAAUkB,IAAzB,CAAL,EAAqC;gBACjC9C,OAAF,EAAWiK,QAAX,CAAoBrI,UAAUgL,SAA9B,EACMC,IADN,CACW,eADX,EAC4B,KAD5B;;;;;;WAOHC,gBAAL,CAAsB,IAAtB;;UAEMC,WAAW,SAAXA,QAAW,GAAM;eAChBD,gBAAL,CAAsB,KAAtB;;UACE,OAAKjL,QAAP,EACGgB,WADH,CACejB,UAAU+K,UADzB,EAEG1C,QAFH,CAEYrI,UAAU8K,QAFtB,EAGG1M,OAHH,CAGW2B,MAAM4L,MAHjB;OAFF;;WAQK1L,QAAL,CAAcrD,KAAd,CAAoBgO,SAApB,IAAiC,EAAjC;;UAEI,CAAC5P,KAAKsC,qBAAL,EAAL,EAAmC;;;;;QAKjC,KAAK2C,QAAP,EACGjD,GADH,CACOhC,KAAKiC,cADZ,EAC4BkO,QAD5B,EAEG9N,oBAFH,CAEwBwC,mBAFxB;KArPkB;;WA0PpBqL,gBA1PoB,6BA0PHU,eA1PG,EA0Pc;WAC3BjC,gBAAL,GAAwBiC,eAAxB;KA3PkB;;WA8PpBnL,OA9PoB,sBA8PV;QACNC,UAAF,CAAa,KAAKT,QAAlB,EAA4BR,QAA5B;WAEK+E,OAAL,GAAwB,IAAxB;WACK2F,OAAL,GAAwB,IAAxB;WACKlK,QAAL,GAAwB,IAAxB;WACK2J,aAAL,GAAwB,IAAxB;WACKD,gBAAL,GAAwB,IAAxB;KArQkB;;;WA2QpBlF,UA3QoB,uBA2QTjG,MA3QS,EA2QD;eACR3C,EAAEuK,MAAF,CAAS,EAAT,EAAarC,OAAb,EAAsBvF,MAAtB,CAAT;aACO8D,MAAP,GAAgBjE,QAAQG,OAAO8D,MAAf,CAAhB,CAFiB;;WAGZ+D,eAAL,CAAqB9G,IAArB,EAA2Bf,MAA3B,EAAmCwF,WAAnC;aACOxF,MAAP;KA/QkB;;WAkRpBqM,aAlRoB,4BAkRJ;UACRgB,WAAWhQ,EAAE,KAAKoE,QAAP,EAAiBkB,QAAjB,CAA0BuI,UAAUoC,KAApC,CAAjB;aACOD,WAAWnC,UAAUoC,KAArB,GAA6BpC,UAAUqC,MAA9C;KApRkB;;WAuRpB3B,UAvRoB,yBAuRP;;;UACPxJ,SAAS,IAAb;;UACI5F,KAAKiE,SAAL,CAAe,KAAKuF,OAAL,CAAa5D,MAA5B,CAAJ,EAAyC;iBAC9B,KAAK4D,OAAL,CAAa5D,MAAtB,CADuC;;YAInC,OAAO,KAAK4D,OAAL,CAAa5D,MAAb,CAAoBoL,MAA3B,KAAsC,WAA1C,EAAuD;mBAC5C,KAAKxH,OAAL,CAAa5D,MAAb,CAAoB,CAApB,CAAT;;OALJ,MAOO;iBACI/E,EAAE,KAAK2I,OAAL,CAAa5D,MAAf,EAAuB,CAAvB,CAAT;;;UAGI/C,yDACqC,KAAK2G,OAAL,CAAa5D,MADlD,QAAN;QAGEA,MAAF,EAAU5C,IAAV,CAAeH,QAAf,EAAyB6D,IAAzB,CAA8B,UAACqI,CAAD,EAAInM,OAAJ,EAAgB;eACvCyM,yBAAL,CACEZ,SAASwC,qBAAT,CAA+BrO,OAA/B,CADF,EAEE,CAACA,OAAD,CAFF;OADF;aAOOgD,MAAP;KA9SkB;;WAiTpByJ,yBAjToB,sCAiTMzM,OAjTN,EAiTesO,YAjTf,EAiT6B;UAC3CtO,OAAJ,EAAa;YACLuO,SAAStQ,EAAE+B,OAAF,EAAWuD,QAAX,CAAoBnB,UAAUkB,IAA9B,CAAf;;YAEIgL,aAAajO,MAAjB,EAAyB;YACrBiO,YAAF,EACG7I,WADH,CACerD,UAAUgL,SADzB,EACoC,CAACmB,MADrC,EAEGlB,IAFH,CAEQ,eAFR,EAEyBkB,MAFzB;;;KAtTc;;;aAgUbF,qBAhUa,kCAgUSrO,OAhUT,EAgUkB;UAC9BC,WAAW7C,KAAK2F,sBAAL,CAA4B/C,OAA5B,CAAjB;aACOC,WAAWhC,EAAEgC,QAAF,EAAY,CAAZ,CAAX,GAA4B,IAAnC;KAlUkB;;aAqUb4D,gBArUa,6BAqUIjD,MArUJ,EAqUY;aACvB,KAAKkD,IAAL,CAAU,YAAY;YACrB0K,QAAUvQ,EAAE,IAAF,CAAhB;YACI+F,OAAYwK,MAAMxK,IAAN,CAAWnC,QAAX,CAAhB;;YACM+E,UAAU3I,EAAEuK,MAAF,CACd,EADc,EAEdrC,OAFc,EAGdqI,MAAMxK,IAAN,EAHc,EAId,OAAOpD,MAAP,KAAkB,QAAlB,IAA8BA,MAJhB,CAAhB;;YAOI,CAACoD,IAAD,IAAS4C,QAAQlC,MAAjB,IAA2B,YAAYnD,IAAZ,CAAiBX,MAAjB,CAA/B,EAAyD;kBAC/C8D,MAAR,GAAiB,KAAjB;;;YAGE,CAACV,IAAL,EAAW;iBACF,IAAI6H,QAAJ,CAAa,IAAb,EAAmBjF,OAAnB,CAAP;gBACM5C,IAAN,CAAWnC,QAAX,EAAqBmC,IAArB;;;YAGE,OAAOpD,MAAP,KAAkB,QAAtB,EAAgC;cAC1B,OAAOoD,KAAKpD,MAAL,CAAP,KAAwB,WAA5B,EAAyC;kBACjC,IAAIY,KAAJ,wBAA8BZ,MAA9B,QAAN;;;eAEGA,MAAL;;OAvBG,CAAP;KAtUkB;;;;0BA4FC;eACZgB,OAAP;;;;0BAGmB;eACZuE,OAAP;;;;;;;;;;;;IA0QFtH,QAAF,EAAYuF,EAAZ,CAAejC,MAAMkC,cAArB,EAAqCnC,SAAS2C,WAA9C,EAA2D,UAAU7G,KAAV,EAAiB;;QAEtEA,MAAMyQ,aAAN,CAAoBvF,OAApB,KAAgC,GAApC,EAAyC;YACjC/E,cAAN;;;QAGIuK,WAAWzQ,EAAE,IAAF,CAAjB;QACMgC,WAAW7C,KAAK2F,sBAAL,CAA4B,IAA5B,CAAjB;MACE9C,QAAF,EAAY6D,IAAZ,CAAiB,YAAY;UACrB6K,UAAU1Q,EAAE,IAAF,CAAhB;UACM+F,OAAU2K,QAAQ3K,IAAR,CAAanC,QAAb,CAAhB;UACMjB,SAAUoD,OAAO,QAAP,GAAkB0K,SAAS1K,IAAT,EAAlC;;eACSH,gBAAT,CAA0BlG,IAA1B,CAA+BgR,OAA/B,EAAwC/N,MAAxC;KAJF;GARF;;;;;;;IAuBEpB,EAAF,CAAKmC,IAAL,IAAyBkK,SAAShI,gBAAlC;IACErE,EAAF,CAAKmC,IAAL,EAAW4C,WAAX,GAAyBsH,QAAzB;;IACErM,EAAF,CAAKmC,IAAL,EAAW6C,UAAX,GAAyB,YAAY;MACjChF,EAAF,CAAKmC,IAAL,IAAaK,kBAAb;WACO6J,SAAShI,gBAAhB;GAFF;;SAKOgI,QAAP;CAzYe,CA2Yd5N,CA3Yc,CAAjB;;ACXA;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,IAAI,WAAW,GAAG,CAAC,aAAa,EAAE,sCAAsC,CAAC,CAAC;;;;;;;;;AAS1E,IAAI,QAAQ,IAAI,UAAU,EAAE,EAAE;EAC5B,OAAO,WAAW,CAAC,IAAI,CAAC,UAAU,IAAI,EAAE;IACtC,OAAO,CAAC,EAAE,IAAI,EAAE,EAAE,QAAQ,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;GACjD,CAAC,CAAC;CACJ,CAAC,CAAC;;AAEH,IAAI,SAAS,GAAG,OAAO,MAAM,KAAK,WAAW,CAAC;AAC9C,IAAI,qBAAqB,GAAG,CAAC,MAAM,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;AAC3D,IAAI,eAAe,GAAG,CAAC,CAAC;AACxB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,qBAAqB,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;EACxD,IAAI,SAAS,IAAI,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE;IAC3E,eAAe,GAAG,CAAC,CAAC;IACpB,MAAM;GACP;CACF;;AAED,SAAS,iBAAiB,CAAC,EAAE,EAAE;EAC7B,IAAI,SAAS,GAAG,KAAK,CAAC;EACtB,IAAI,CAAC,GAAG,CAAC,CAAC;EACV,IAAI,IAAI,GAAG,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;;;;;EAK1C,IAAI,QAAQ,GAAG,IAAI,gBAAgB,CAAC,YAAY;IAC9C,EAAE,EAAE,CAAC;IACL,SAAS,GAAG,KAAK,CAAC;GACnB,CAAC,CAAC;;EAEH,QAAQ,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC;;EAE7C,OAAO,YAAY;IACjB,IAAI,CAAC,SAAS,EAAE;MACd,SAAS,GAAG,IAAI,CAAC;MACjB,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;MAChC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;KACX;GACF,CAAC;CACH;;AAED,SAAS,YAAY,CAAC,EAAE,EAAE;EACxB,IAAI,SAAS,GAAG,KAAK,CAAC;EACtB,OAAO,YAAY;IACjB,IAAI,CAAC,SAAS,EAAE;MACd,SAAS,GAAG,IAAI,CAAC;MACjB,UAAU,CAAC,YAAY;QACrB,SAAS,GAAG,KAAK,CAAC;QAClB,EAAE,EAAE,CAAC;OACN,EAAE,eAAe,CAAC,CAAC;KACrB;GACF,CAAC;CACH;;;;;;AAMD,IAAI,8BAA8B,GAAG,SAAS,IAAI,QAAQ,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;;;;;;;;;;;AAWpF,IAAI,QAAQ,GAAG,8BAA8B,GAAG,iBAAiB,GAAG,YAAY,CAAC;;;;;;;;;AASjF,SAAS,UAAU,CAAC,eAAe,EAAE;EACnC,IAAI,OAAO,GAAG,EAAE,CAAC;EACjB,OAAO,eAAe,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,mBAAmB,CAAC;CAC1F;;;;;;;;;AASD,SAAS,wBAAwB,CAAC,OAAO,EAAE,QAAQ,EAAE;EACnD,IAAI,OAAO,CAAC,QAAQ,KAAK,CAAC,EAAE;IAC1B,OAAO,EAAE,CAAC;GACX;;EAED,IAAI,GAAG,GAAG,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;EACjD,OAAO,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC,GAAG,GAAG,CAAC;CACvC;;;;;;;;;AASD,SAAS,aAAa,CAAC,OAAO,EAAE;EAC9B,IAAI,OAAO,CAAC,QAAQ,KAAK,MAAM,EAAE;IAC/B,OAAO,OAAO,CAAC;GAChB;EACD,OAAO,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;CAC3C;;;;;;;;;AASD,SAAS,eAAe,CAAC,OAAO,EAAE;;EAEhC,IAAI,CAAC,OAAO,IAAI,CAAC,MAAM,EAAE,MAAM,EAAE,WAAW,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;IAC9E,OAAO,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;GAC7B;;;;EAID,IAAI,qBAAqB,GAAG,wBAAwB,CAAC,OAAO,CAAC;MACzD,QAAQ,GAAG,qBAAqB,CAAC,QAAQ;MACzC,SAAS,GAAG,qBAAqB,CAAC,SAAS;MAC3C,SAAS,GAAG,qBAAqB,CAAC,SAAS,CAAC;;EAEhD,IAAI,eAAe,CAAC,IAAI,CAAC,QAAQ,GAAG,SAAS,GAAG,SAAS,CAAC,EAAE;IAC1D,OAAO,OAAO,CAAC;GAChB;;EAED,OAAO,eAAe,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC;CAChD;;;;;;;;;AASD,SAAS,eAAe,CAAC,OAAO,EAAE;;EAEhC,IAAI,YAAY,GAAG,OAAO,IAAI,OAAO,CAAC,YAAY,CAAC;EACnD,IAAI,QAAQ,GAAG,YAAY,IAAI,YAAY,CAAC,QAAQ,CAAC;;EAErD,IAAI,CAAC,QAAQ,IAAI,QAAQ,KAAK,MAAM,IAAI,QAAQ,KAAK,MAAM,EAAE;IAC3D,OAAO,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC;GACxC;;;;EAID,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,wBAAwB,CAAC,YAAY,EAAE,UAAU,CAAC,KAAK,QAAQ,EAAE;IAC5H,OAAO,eAAe,CAAC,YAAY,CAAC,CAAC;GACtC;;EAED,OAAO,YAAY,CAAC;CACrB;;AAED,SAAS,iBAAiB,CAAC,OAAO,EAAE;EAClC,IAAI,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;;EAEhC,IAAI,QAAQ,KAAK,MAAM,EAAE;IACvB,OAAO,KAAK,CAAC;GACd;EACD,OAAO,QAAQ,KAAK,MAAM,IAAI,eAAe,CAAC,OAAO,CAAC,iBAAiB,CAAC,KAAK,OAAO,CAAC;CACtF;;;;;;;;;AASD,SAAS,OAAO,CAAC,IAAI,EAAE;EACrB,IAAI,IAAI,CAAC,UAAU,KAAK,IAAI,EAAE;IAC5B,OAAO,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;GACjC;;EAED,OAAO,IAAI,CAAC;CACb;;;;;;;;;;AAUD,SAAS,sBAAsB,CAAC,QAAQ,EAAE,QAAQ,EAAE;;EAElD,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,QAAQ,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE;IACtE,OAAO,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC;GACxC;;;EAGD,IAAI,KAAK,GAAG,QAAQ,CAAC,uBAAuB,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,2BAA2B,CAAC;EAC1F,IAAI,KAAK,GAAG,KAAK,GAAG,QAAQ,GAAG,QAAQ,CAAC;EACxC,IAAI,GAAG,GAAG,KAAK,GAAG,QAAQ,GAAG,QAAQ,CAAC;;;EAGtC,IAAI,KAAK,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC;EACnC,KAAK,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;EACzB,KAAK,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;EACrB,IAAI,uBAAuB,GAAG,KAAK,CAAC,uBAAuB,CAAC;;;;EAI5D,IAAI,QAAQ,KAAK,uBAAuB,IAAI,QAAQ,KAAK,uBAAuB,IAAI,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE;IACvG,IAAI,iBAAiB,CAAC,uBAAuB,CAAC,EAAE;MAC9C,OAAO,uBAAuB,CAAC;KAChC;;IAED,OAAO,eAAe,CAAC,uBAAuB,CAAC,CAAC;GACjD;;;EAGD,IAAI,YAAY,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;EACrC,IAAI,YAAY,CAAC,IAAI,EAAE;IACrB,OAAO,sBAAsB,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;GAC5D,MAAM;IACL,OAAO,sBAAsB,CAAC,QAAQ,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC;GACjE;CACF;;;;;;;;;;AAUD,SAAS,SAAS,CAAC,OAAO,EAAE;EAC1B,IAAI,IAAI,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,KAAK,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;;EAErF,IAAI,SAAS,GAAG,IAAI,KAAK,KAAK,GAAG,WAAW,GAAG,YAAY,CAAC;EAC5D,IAAI,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;;EAEhC,IAAI,QAAQ,KAAK,MAAM,IAAI,QAAQ,KAAK,MAAM,EAAE;IAC9C,IAAI,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC;IAC3C,IAAI,gBAAgB,GAAG,MAAM,CAAC,QAAQ,CAAC,gBAAgB,IAAI,IAAI,CAAC;IAChE,OAAO,gBAAgB,CAAC,SAAS,CAAC,CAAC;GACpC;;EAED,OAAO,OAAO,CAAC,SAAS,CAAC,CAAC;CAC3B;;;;;;;;;;;AAWD,SAAS,aAAa,CAAC,IAAI,EAAE,OAAO,EAAE;EACpC,IAAI,QAAQ,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,KAAK,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;;EAEzF,IAAI,SAAS,GAAG,SAAS,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;EAC1C,IAAI,UAAU,GAAG,SAAS,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;EAC5C,IAAI,QAAQ,GAAG,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;EACjC,IAAI,CAAC,GAAG,IAAI,SAAS,GAAG,QAAQ,CAAC;EACjC,IAAI,CAAC,MAAM,IAAI,SAAS,GAAG,QAAQ,CAAC;EACpC,IAAI,CAAC,IAAI,IAAI,UAAU,GAAG,QAAQ,CAAC;EACnC,IAAI,CAAC,KAAK,IAAI,UAAU,GAAG,QAAQ,CAAC;EACpC,OAAO,IAAI,CAAC;CACb;;;;;;;;;;;;AAYD,SAAS,cAAc,CAAC,MAAM,EAAE,IAAI,EAAE;EACpC,IAAI,KAAK,GAAG,IAAI,KAAK,GAAG,GAAG,MAAM,GAAG,KAAK,CAAC;EAC1C,IAAI,KAAK,GAAG,KAAK,KAAK,MAAM,GAAG,OAAO,GAAG,QAAQ,CAAC;;EAElD,OAAO,CAAC,MAAM,CAAC,QAAQ,GAAG,KAAK,GAAG,OAAO,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,GAAG,KAAK,GAAG,OAAO,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;CAChH;;;;;;;;AAQD,IAAI,MAAM,GAAG,SAAS,CAAC;;AAEvB,IAAI,QAAQ,GAAG,YAAY;EACzB,IAAI,MAAM,KAAK,SAAS,EAAE;IACxB,MAAM,GAAG,SAAS,CAAC,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;GACzD;EACD,OAAO,MAAM,CAAC;CACf,CAAC;;AAEF,SAAS,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,aAAa,EAAE;EAChD,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,EAAE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,EAAE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,EAAE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,EAAE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,GAAG,aAAa,CAAC,QAAQ,IAAI,IAAI,KAAK,QAAQ,GAAG,KAAK,GAAG,MAAM,CAAC,CAAC,GAAG,aAAa,CAAC,QAAQ,IAAI,IAAI,KAAK,QAAQ,GAAG,QAAQ,GAAG,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;CAClT;;AAED,SAAS,cAAc,GAAG;EACxB,IAAI,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;EAChC,IAAI,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC;EAC3C,IAAI,aAAa,GAAG,QAAQ,EAAE,IAAI,MAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;;EAEhE,OAAO;IACL,MAAM,EAAE,OAAO,CAAC,QAAQ,EAAE,IAAI,EAAE,IAAI,EAAE,aAAa,CAAC;IACpD,KAAK,EAAE,OAAO,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,aAAa,CAAC;GACnD,CAAC;CACH;;AAED,IAAI,cAAc,GAAG,UAAU,QAAQ,EAAE,WAAW,EAAE;EACpD,IAAI,EAAE,QAAQ,YAAY,WAAW,CAAC,EAAE;IACtC,MAAM,IAAI,SAAS,CAAC,mCAAmC,CAAC,CAAC;GAC1D;CACF,CAAC;;AAEF,IAAI2Q,aAAW,GAAG,YAAY;EAC5B,SAAS,gBAAgB,CAAC,MAAM,EAAE,KAAK,EAAE;IACvC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;MACrC,IAAI,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;MAC1B,UAAU,CAAC,UAAU,GAAG,UAAU,CAAC,UAAU,IAAI,KAAK,CAAC;MACvD,UAAU,CAAC,YAAY,GAAG,IAAI,CAAC;MAC/B,IAAI,OAAO,IAAI,UAAU,EAAE,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC;MACtD,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,UAAU,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;KAC3D;GACF;;EAED,OAAO,UAAU,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE;IACrD,IAAI,UAAU,EAAE,gBAAgB,CAAC,WAAW,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;IACpE,IAAI,WAAW,EAAE,gBAAgB,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;IAC5D,OAAO,WAAW,CAAC;GACpB,CAAC;CACH,EAAE,CAAC;;;;;;AAMJ,IAAI,cAAc,GAAG,UAAU,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE;EAC9C,IAAI,GAAG,IAAI,GAAG,EAAE;IACd,MAAM,CAAC,cAAc,CAAC,GAAG,EAAE,GAAG,EAAE;MAC9B,KAAK,EAAE,KAAK;MACZ,UAAU,EAAE,IAAI;MAChB,YAAY,EAAE,IAAI;MAClB,QAAQ,EAAE,IAAI;KACf,CAAC,CAAC;GACJ,MAAM;IACL,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC;GAClB;;EAED,OAAO,GAAG,CAAC;CACZ,CAAC;;AAEF,IAAIC,UAAQ,GAAG,MAAM,CAAC,MAAM,IAAI,UAAU,MAAM,EAAE;EAChD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;IACzC,IAAI,MAAM,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;;IAE1B,KAAK,IAAI,GAAG,IAAI,MAAM,EAAE;MACtB,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,EAAE;QACrD,MAAM,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;OAC3B;KACF;GACF;;EAED,OAAO,MAAM,CAAC;CACf,CAAC;;;;;;;;;AASF,SAAS,aAAa,CAAC,OAAO,EAAE;EAC9B,OAAOA,UAAQ,CAAC,EAAE,EAAE,OAAO,EAAE;IAC3B,KAAK,EAAE,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,KAAK;IACnC,MAAM,EAAE,OAAO,CAAC,GAAG,GAAG,OAAO,CAAC,MAAM;GACrC,CAAC,CAAC;CACJ;;;;;;;;;AASD,SAAS,qBAAqB,CAAC,OAAO,EAAE;EACtC,IAAI,IAAI,GAAG,EAAE,CAAC;;;;;EAKd,IAAI,QAAQ,EAAE,EAAE;IACd,IAAI;MACF,IAAI,GAAG,OAAO,CAAC,qBAAqB,EAAE,CAAC;MACvC,IAAI,SAAS,GAAG,SAAS,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;MAC1C,IAAI,UAAU,GAAG,SAAS,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;MAC5C,IAAI,CAAC,GAAG,IAAI,SAAS,CAAC;MACtB,IAAI,CAAC,IAAI,IAAI,UAAU,CAAC;MACxB,IAAI,CAAC,MAAM,IAAI,SAAS,CAAC;MACzB,IAAI,CAAC,KAAK,IAAI,UAAU,CAAC;KAC1B,CAAC,OAAO,GAAG,EAAE,EAAE;GACjB,MAAM;IACL,IAAI,GAAG,OAAO,CAAC,qBAAqB,EAAE,CAAC;GACxC;;EAED,IAAI,MAAM,GAAG;IACX,IAAI,EAAE,IAAI,CAAC,IAAI;IACf,GAAG,EAAE,IAAI,CAAC,GAAG;IACb,KAAK,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI;IAC7B,MAAM,EAAE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG;GAC/B,CAAC;;;EAGF,IAAI,KAAK,GAAG,OAAO,CAAC,QAAQ,KAAK,MAAM,GAAG,cAAc,EAAE,GAAG,EAAE,CAAC;EAChE,IAAI,KAAK,GAAG,KAAK,CAAC,KAAK,IAAI,OAAO,CAAC,WAAW,IAAI,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC;EAC7E,IAAI,MAAM,GAAG,KAAK,CAAC,MAAM,IAAI,OAAO,CAAC,YAAY,IAAI,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC;;EAEhF,IAAI,cAAc,GAAG,OAAO,CAAC,WAAW,GAAG,KAAK,CAAC;EACjD,IAAI,aAAa,GAAG,OAAO,CAAC,YAAY,GAAG,MAAM,CAAC;;;;EAIlD,IAAI,cAAc,IAAI,aAAa,EAAE;IACnC,IAAI,MAAM,GAAG,wBAAwB,CAAC,OAAO,CAAC,CAAC;IAC/C,cAAc,IAAI,cAAc,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IAC9C,aAAa,IAAI,cAAc,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;;IAE7C,MAAM,CAAC,KAAK,IAAI,cAAc,CAAC;IAC/B,MAAM,CAAC,MAAM,IAAI,aAAa,CAAC;GAChC;;EAED,OAAO,aAAa,CAAC,MAAM,CAAC,CAAC;CAC9B;;AAED,SAAS,oCAAoC,CAAC,QAAQ,EAAE,MAAM,EAAE;EAC9D,IAAI,MAAM,GAAG,QAAQ,EAAE,CAAC;EACxB,IAAI,MAAM,GAAG,MAAM,CAAC,QAAQ,KAAK,MAAM,CAAC;EACxC,IAAI,YAAY,GAAG,qBAAqB,CAAC,QAAQ,CAAC,CAAC;EACnD,IAAI,UAAU,GAAG,qBAAqB,CAAC,MAAM,CAAC,CAAC;EAC/C,IAAI,YAAY,GAAG,eAAe,CAAC,QAAQ,CAAC,CAAC;;EAE7C,IAAI,MAAM,GAAG,wBAAwB,CAAC,MAAM,CAAC,CAAC;EAC9C,IAAI,cAAc,GAAG,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;EAC3D,IAAI,eAAe,GAAG,CAAC,MAAM,CAAC,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;;EAE7D,IAAI,OAAO,GAAG,aAAa,CAAC;IAC1B,GAAG,EAAE,YAAY,CAAC,GAAG,GAAG,UAAU,CAAC,GAAG,GAAG,cAAc;IACvD,IAAI,EAAE,YAAY,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI,GAAG,eAAe;IAC3D,KAAK,EAAE,YAAY,CAAC,KAAK;IACzB,MAAM,EAAE,YAAY,CAAC,MAAM;GAC5B,CAAC,CAAC;EACH,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC;EACtB,OAAO,CAAC,UAAU,GAAG,CAAC,CAAC;;;;;;EAMvB,IAAI,CAAC,MAAM,IAAI,MAAM,EAAE;IACrB,IAAI,SAAS,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IACjD,IAAI,UAAU,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEnD,OAAO,CAAC,GAAG,IAAI,cAAc,GAAG,SAAS,CAAC;IAC1C,OAAO,CAAC,MAAM,IAAI,cAAc,GAAG,SAAS,CAAC;IAC7C,OAAO,CAAC,IAAI,IAAI,eAAe,GAAG,UAAU,CAAC;IAC7C,OAAO,CAAC,KAAK,IAAI,eAAe,GAAG,UAAU,CAAC;;;IAG9C,OAAO,CAAC,SAAS,GAAG,SAAS,CAAC;IAC9B,OAAO,CAAC,UAAU,GAAG,UAAU,CAAC;GACjC;;EAED,IAAI,MAAM,GAAG,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,GAAG,MAAM,KAAK,YAAY,IAAI,YAAY,CAAC,QAAQ,KAAK,MAAM,EAAE;IACxG,OAAO,GAAG,aAAa,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;GAC1C;;EAED,OAAO,OAAO,CAAC;CAChB;;AAED,SAAS,6CAA6C,CAAC,OAAO,EAAE;EAC9D,IAAI,IAAI,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC;EAC3C,IAAI,cAAc,GAAG,oCAAoC,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;EACzE,IAAI,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,MAAM,CAAC,UAAU,IAAI,CAAC,CAAC,CAAC;EAC/D,IAAI,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,EAAE,MAAM,CAAC,WAAW,IAAI,CAAC,CAAC,CAAC;;EAElE,IAAI,SAAS,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;EAChC,IAAI,UAAU,GAAG,SAAS,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;;EAEzC,IAAI,MAAM,GAAG;IACX,GAAG,EAAE,SAAS,GAAG,cAAc,CAAC,GAAG,GAAG,cAAc,CAAC,SAAS;IAC9D,IAAI,EAAE,UAAU,GAAG,cAAc,CAAC,IAAI,GAAG,cAAc,CAAC,UAAU;IAClE,KAAK,EAAE,KAAK;IACZ,MAAM,EAAE,MAAM;GACf,CAAC;;EAEF,OAAO,aAAa,CAAC,MAAM,CAAC,CAAC;CAC9B;;;;;;;;;;AAUD,SAAS,OAAO,CAAC,OAAO,EAAE;EACxB,IAAI,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;EAChC,IAAI,QAAQ,KAAK,MAAM,IAAI,QAAQ,KAAK,MAAM,EAAE;IAC9C,OAAO,KAAK,CAAC;GACd;EACD,IAAI,wBAAwB,CAAC,OAAO,EAAE,UAAU,CAAC,KAAK,OAAO,EAAE;IAC7D,OAAO,IAAI,CAAC;GACb;EACD,OAAO,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC;CACxC;;;;;;;;;;;;AAYD,SAAS,aAAa,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,iBAAiB,EAAE;;EAEpE,IAAI,UAAU,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC;EACrC,IAAI,YAAY,GAAG,sBAAsB,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;;;EAG7D,IAAI,iBAAiB,KAAK,UAAU,EAAE;IACpC,UAAU,GAAG,6CAA6C,CAAC,YAAY,CAAC,CAAC;GAC1E,MAAM;;IAEL,IAAI,cAAc,GAAG,KAAK,CAAC,CAAC;IAC5B,IAAI,iBAAiB,KAAK,cAAc,EAAE;MACxC,cAAc,GAAG,eAAe,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC;MACxD,IAAI,cAAc,CAAC,QAAQ,KAAK,MAAM,EAAE;QACtC,cAAc,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC;OAClD;KACF,MAAM,IAAI,iBAAiB,KAAK,QAAQ,EAAE;MACzC,cAAc,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC;KAClD,MAAM;MACL,cAAc,GAAG,iBAAiB,CAAC;KACpC;;IAED,IAAI,OAAO,GAAG,oCAAoC,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;;;IAGjF,IAAI,cAAc,CAAC,QAAQ,KAAK,MAAM,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE;MAChE,IAAI,eAAe,GAAG,cAAc,EAAE;UAClC,MAAM,GAAG,eAAe,CAAC,MAAM;UAC/B,KAAK,GAAG,eAAe,CAAC,KAAK,CAAC;;MAElC,UAAU,CAAC,GAAG,IAAI,OAAO,CAAC,GAAG,GAAG,OAAO,CAAC,SAAS,CAAC;MAClD,UAAU,CAAC,MAAM,GAAG,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC;MACzC,UAAU,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,UAAU,CAAC;MACrD,UAAU,CAAC,KAAK,GAAG,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC;KACzC,MAAM;;MAEL,UAAU,GAAG,OAAO,CAAC;KACtB;GACF;;;EAGD,UAAU,CAAC,IAAI,IAAI,OAAO,CAAC;EAC3B,UAAU,CAAC,GAAG,IAAI,OAAO,CAAC;EAC1B,UAAU,CAAC,KAAK,IAAI,OAAO,CAAC;EAC5B,UAAU,CAAC,MAAM,IAAI,OAAO,CAAC;;EAE7B,OAAO,UAAU,CAAC;CACnB;;AAED,SAAS,OAAO,CAAC,IAAI,EAAE;EACrB,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK;MAClB,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;;EAEzB,OAAO,KAAK,GAAG,MAAM,CAAC;CACvB;;;;;;;;;;;AAWD,SAAS,oBAAoB,CAAC,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,iBAAiB,EAAE;EACtF,IAAI,OAAO,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,KAAK,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;;EAEpF,IAAI,SAAS,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;IACpC,OAAO,SAAS,CAAC;GAClB;;EAED,IAAI,UAAU,GAAG,aAAa,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,iBAAiB,CAAC,CAAC;;EAE9E,IAAI,KAAK,GAAG;IACV,GAAG,EAAE;MACH,KAAK,EAAE,UAAU,CAAC,KAAK;MACvB,MAAM,EAAE,OAAO,CAAC,GAAG,GAAG,UAAU,CAAC,GAAG;KACrC;IACD,KAAK,EAAE;MACL,KAAK,EAAE,UAAU,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK;MACvC,MAAM,EAAE,UAAU,CAAC,MAAM;KAC1B;IACD,MAAM,EAAE;MACN,KAAK,EAAE,UAAU,CAAC,KAAK;MACvB,MAAM,EAAE,UAAU,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM;KAC3C;IACD,IAAI,EAAE;MACJ,KAAK,EAAE,OAAO,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI;MACrC,MAAM,EAAE,UAAU,CAAC,MAAM;KAC1B;GACF,CAAC;;EAEF,IAAI,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,UAAU,GAAG,EAAE;IACtD,OAAOA,UAAQ,CAAC;MACd,GAAG,EAAE,GAAG;KACT,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE;MACb,IAAI,EAAE,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;KAC1B,CAAC,CAAC;GACJ,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE;IACtB,OAAO,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC;GACxB,CAAC,CAAC;;EAEH,IAAI,aAAa,GAAG,WAAW,CAAC,MAAM,CAAC,UAAU,KAAK,EAAE;IACtD,IAAI,KAAK,GAAG,KAAK,CAAC,KAAK;QACnB,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;IAC1B,OAAO,KAAK,IAAI,MAAM,CAAC,WAAW,IAAI,MAAM,IAAI,MAAM,CAAC,YAAY,CAAC;GACrE,CAAC,CAAC;;EAEH,IAAI,iBAAiB,GAAG,aAAa,CAAC,MAAM,GAAG,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;;EAE7F,IAAI,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;;EAExC,OAAO,iBAAiB,IAAI,SAAS,GAAG,GAAG,GAAG,SAAS,GAAG,EAAE,CAAC,CAAC;CAC/D;;;;;;;;;;;AAWD,SAAS,mBAAmB,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE;EACrD,IAAI,kBAAkB,GAAG,sBAAsB,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;EACnE,OAAO,oCAAoC,CAAC,SAAS,EAAE,kBAAkB,CAAC,CAAC;CAC5E;;;;;;;;;AASD,SAAS,aAAa,CAAC,OAAO,EAAE;EAC9B,IAAI,MAAM,GAAG,MAAM,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;EAC9C,IAAI,CAAC,GAAG,UAAU,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,UAAU,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC;EACvE,IAAI,CAAC,GAAG,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,GAAG,UAAU,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;EACvE,IAAI,MAAM,GAAG;IACX,KAAK,EAAE,OAAO,CAAC,WAAW,GAAG,CAAC;IAC9B,MAAM,EAAE,OAAO,CAAC,YAAY,GAAG,CAAC;GACjC,CAAC;EACF,OAAO,MAAM,CAAC;CACf;;;;;;;;;AASD,SAAS,oBAAoB,CAAC,SAAS,EAAE;EACvC,IAAI,IAAI,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,QAAQ,EAAE,CAAC;EAC1E,OAAO,SAAS,CAAC,OAAO,CAAC,wBAAwB,EAAE,UAAU,OAAO,EAAE;IACpE,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;GACtB,CAAC,CAAC;CACJ;;;;;;;;;;;;AAYD,SAAS,gBAAgB,CAAC,MAAM,EAAE,gBAAgB,EAAE,SAAS,EAAE;EAC7D,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;;;EAGpC,IAAI,UAAU,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC;;;EAGvC,IAAI,aAAa,GAAG;IAClB,KAAK,EAAE,UAAU,CAAC,KAAK;IACvB,MAAM,EAAE,UAAU,CAAC,MAAM;GAC1B,CAAC;;;EAGF,IAAI,OAAO,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;EAC1D,IAAI,QAAQ,GAAG,OAAO,GAAG,KAAK,GAAG,MAAM,CAAC;EACxC,IAAI,aAAa,GAAG,OAAO,GAAG,MAAM,GAAG,KAAK,CAAC;EAC7C,IAAI,WAAW,GAAG,OAAO,GAAG,QAAQ,GAAG,OAAO,CAAC;EAC/C,IAAI,oBAAoB,GAAG,CAAC,OAAO,GAAG,QAAQ,GAAG,OAAO,CAAC;;EAEzD,aAAa,CAAC,QAAQ,CAAC,GAAG,gBAAgB,CAAC,QAAQ,CAAC,GAAG,gBAAgB,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,UAAU,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;EACvH,IAAI,SAAS,KAAK,aAAa,EAAE;IAC/B,aAAa,CAAC,aAAa,CAAC,GAAG,gBAAgB,CAAC,aAAa,CAAC,GAAG,UAAU,CAAC,oBAAoB,CAAC,CAAC;GACnG,MAAM;IACL,aAAa,CAAC,aAAa,CAAC,GAAG,gBAAgB,CAAC,oBAAoB,CAAC,aAAa,CAAC,CAAC,CAAC;GACtF;;EAED,OAAO,aAAa,CAAC;CACtB;;;;;;;;;;;AAWD,SAAS,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE;;EAExB,IAAI,KAAK,CAAC,SAAS,CAAC,IAAI,EAAE;IACxB,OAAO,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;GACxB;;;EAGD,OAAO,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;CAC7B;;;;;;;;;;;AAWD,SAAS,SAAS,CAAC,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE;;EAEnC,IAAI,KAAK,CAAC,SAAS,CAAC,SAAS,EAAE;IAC7B,OAAO,GAAG,CAAC,SAAS,CAAC,UAAU,GAAG,EAAE;MAClC,OAAO,GAAG,CAAC,IAAI,CAAC,KAAK,KAAK,CAAC;KAC5B,CAAC,CAAC;GACJ;;;EAGD,IAAI,KAAK,GAAG,IAAI,CAAC,GAAG,EAAE,UAAU,GAAG,EAAE;IACnC,OAAO,GAAG,CAAC,IAAI,CAAC,KAAK,KAAK,CAAC;GAC5B,CAAC,CAAC;EACH,OAAO,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;CAC3B;;;;;;;;;;;;AAYD,SAAS,YAAY,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE;EAC3C,IAAI,cAAc,GAAG,IAAI,KAAK,SAAS,GAAG,SAAS,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,SAAS,CAAC,SAAS,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;;EAE7G,cAAc,CAAC,OAAO,CAAC,UAAU,QAAQ,EAAE;IACzC,IAAI,QAAQ,CAAC,QAAQ,EAAE;MACrB,OAAO,CAAC,IAAI,CAAC,uDAAuD,CAAC,CAAC;KACvE;IACD,IAAI,EAAE,GAAG,QAAQ,CAAC,QAAQ,IAAI,QAAQ,CAAC,EAAE,CAAC;IAC1C,IAAI,QAAQ,CAAC,OAAO,IAAI,UAAU,CAAC,EAAE,CAAC,EAAE;;;;MAItC,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;MACzD,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;;MAE/D,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;KAC3B;GACF,CAAC,CAAC;;EAEH,OAAO,IAAI,CAAC;CACb;;;;;;;;;AASD,SAAS,MAAM,GAAG;;EAEhB,IAAI,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE;IAC1B,OAAO;GACR;;EAED,IAAI,IAAI,GAAG;IACT,QAAQ,EAAE,IAAI;IACd,MAAM,EAAE,EAAE;IACV,WAAW,EAAE,EAAE;IACf,UAAU,EAAE,EAAE;IACd,OAAO,EAAE,KAAK;IACd,OAAO,EAAE,EAAE;GACZ,CAAC;;;EAGF,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,mBAAmB,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;;;;;EAKtF,IAAI,CAAC,SAAS,GAAG,oBAAoB,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;;;EAGvM,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,SAAS,CAAC;;;EAGxC,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,gBAAgB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;EAC5F,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,GAAG,UAAU,CAAC;;;EAG1C,IAAI,GAAG,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;;;;EAI1C,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE;IACzB,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC;IAC5B,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;GAC7B,MAAM;IACL,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;GAC7B;CACF;;;;;;;;AAQD,SAAS,iBAAiB,CAAC,SAAS,EAAE,YAAY,EAAE;EAClD,OAAO,SAAS,CAAC,IAAI,CAAC,UAAU,IAAI,EAAE;IACpC,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI;QAChB,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;IAC3B,OAAO,OAAO,IAAI,IAAI,KAAK,YAAY,CAAC;GACzC,CAAC,CAAC;CACJ;;;;;;;;;AASD,SAAS,wBAAwB,CAAC,QAAQ,EAAE;EAC1C,IAAI,QAAQ,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;EACnD,IAAI,SAAS,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;;EAErE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;IAC5C,IAAI,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;IACzB,IAAI,OAAO,GAAG,MAAM,GAAG,EAAE,GAAG,MAAM,GAAG,SAAS,GAAG,QAAQ,CAAC;IAC1D,IAAI,OAAO,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,WAAW,EAAE;MAC9D,OAAO,OAAO,CAAC;KAChB;GACF;EACD,OAAO,IAAI,CAAC;CACb;;;;;;;AAOD,SAAS,OAAO,GAAG;EACjB,IAAI,CAAC,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC;;;EAG9B,IAAI,iBAAiB,CAAC,IAAI,CAAC,SAAS,EAAE,YAAY,CAAC,EAAE;IACnD,IAAI,CAAC,MAAM,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;IAC3C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,GAAG,EAAE,CAAC;IAC5B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,QAAQ,GAAG,EAAE,CAAC;IAChC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,EAAE,CAAC;IAC3B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wBAAwB,CAAC,WAAW,CAAC,CAAC,GAAG,EAAE,CAAC;GAC/D;;EAED,IAAI,CAAC,qBAAqB,EAAE,CAAC;;;;EAI7B,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,EAAE;IAChC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;GACjD;EACD,OAAO,IAAI,CAAC;CACb;;AAED,SAAS,qBAAqB,CAAC,YAAY,EAAE,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE;EAC3E,IAAI,MAAM,GAAG,YAAY,CAAC,QAAQ,KAAK,MAAM,CAAC;EAC9C,IAAI,MAAM,GAAG,MAAM,GAAG,MAAM,GAAG,YAAY,CAAC;EAC5C,MAAM,CAAC,gBAAgB,CAAC,KAAK,EAAE,QAAQ,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;;EAE5D,IAAI,CAAC,MAAM,EAAE;IACX,qBAAqB,CAAC,eAAe,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,KAAK,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC;GAC3F;EACD,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;CAC5B;;;;;;;;AAQD,SAAS,mBAAmB,CAAC,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE,WAAW,EAAE;;EAEnE,KAAK,CAAC,WAAW,GAAG,WAAW,CAAC;EAChC,MAAM,CAAC,gBAAgB,CAAC,QAAQ,EAAE,KAAK,CAAC,WAAW,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;;;EAGxE,IAAI,aAAa,GAAG,eAAe,CAAC,SAAS,CAAC,CAAC;EAC/C,qBAAqB,CAAC,aAAa,EAAE,QAAQ,EAAE,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,aAAa,CAAC,CAAC;EACvF,KAAK,CAAC,aAAa,GAAG,aAAa,CAAC;EACpC,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC;;EAE3B,OAAO,KAAK,CAAC;CACd;;;;;;;;AAQD,SAAS,oBAAoB,GAAG;EAC9B,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE;IAC7B,IAAI,CAAC,KAAK,GAAG,mBAAmB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;GACjG;CACF;;;;;;;;AAQD,SAAS,oBAAoB,CAAC,SAAS,EAAE,KAAK,EAAE;;EAE9C,MAAM,CAAC,mBAAmB,CAAC,QAAQ,EAAE,KAAK,CAAC,WAAW,CAAC,CAAC;;;EAGxD,KAAK,CAAC,aAAa,CAAC,OAAO,CAAC,UAAU,MAAM,EAAE;IAC5C,MAAM,CAAC,mBAAmB,CAAC,QAAQ,EAAE,KAAK,CAAC,WAAW,CAAC,CAAC;GACzD,CAAC,CAAC;;;EAGH,KAAK,CAAC,WAAW,GAAG,IAAI,CAAC;EACzB,KAAK,CAAC,aAAa,GAAG,EAAE,CAAC;EACzB,KAAK,CAAC,aAAa,GAAG,IAAI,CAAC;EAC3B,KAAK,CAAC,aAAa,GAAG,KAAK,CAAC;EAC5B,OAAO,KAAK,CAAC;CACd;;;;;;;;;AASD,SAAS,qBAAqB,GAAG;EAC/B,IAAI,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE;IAC5B,MAAM,CAAC,oBAAoB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IACjD,IAAI,CAAC,KAAK,GAAG,oBAAoB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;GAC/D;CACF;;;;;;;;;AASD,SAAS,SAAS,CAAC,CAAC,EAAE;EACpB,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,QAAQ,CAAC,CAAC,CAAC,CAAC;CACzD;;;;;;;;;;AAUD,SAAS,SAAS,CAAC,OAAO,EAAE,MAAM,EAAE;EAClC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;IAC1C,IAAI,IAAI,GAAG,EAAE,CAAC;;IAEd,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,EAAE;MACzG,IAAI,GAAG,IAAI,CAAC;KACb;IACD,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;GAC3C,CAAC,CAAC;CACJ;;;;;;;;;;AAUD,SAAS,aAAa,CAAC,OAAO,EAAE,UAAU,EAAE;EAC1C,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;IAC9C,IAAI,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;IAC7B,IAAI,KAAK,KAAK,KAAK,EAAE;MACnB,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;KAC9C,MAAM;MACL,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;KAC/B;GACF,CAAC,CAAC;CACJ;;;;;;;;;;;AAWD,SAAS,UAAU,CAAC,IAAI,EAAE;;;;;EAKxB,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;;;;EAI7C,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;;;EAGrD,IAAI,IAAI,CAAC,YAAY,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,MAAM,EAAE;IAC7D,SAAS,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;GAChD;;EAED,OAAO,IAAI,CAAC;CACb;;;;;;;;;;;;AAYD,SAAS,gBAAgB,CAAC,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,eAAe,EAAE,KAAK,EAAE;;EAE5E,IAAI,gBAAgB,GAAG,mBAAmB,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,CAAC,CAAC;;;;;EAKrE,IAAI,SAAS,GAAG,oBAAoB,CAAC,OAAO,CAAC,SAAS,EAAE,gBAAgB,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,iBAAiB,EAAE,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;;EAEvK,MAAM,CAAC,YAAY,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;;;;EAI9C,SAAS,CAAC,MAAM,EAAE,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC,CAAC;;EAE5C,OAAO,OAAO,CAAC;CAChB;;;;;;;;;AASD,SAAS,YAAY,CAAC,IAAI,EAAE,OAAO,EAAE;EACnC,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC;MACb,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC;EAClB,IAAI,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;;;;EAIjC,IAAI,2BAA2B,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,UAAU,QAAQ,EAAE;IAClF,OAAO,QAAQ,CAAC,IAAI,KAAK,YAAY,CAAC;GACvC,CAAC,CAAC,eAAe,CAAC;EACnB,IAAI,2BAA2B,KAAK,SAAS,EAAE;IAC7C,OAAO,CAAC,IAAI,CAAC,+HAA+H,CAAC,CAAC;GAC/I;EACD,IAAI,eAAe,GAAG,2BAA2B,KAAK,SAAS,GAAG,2BAA2B,GAAG,OAAO,CAAC,eAAe,CAAC;;EAExH,IAAI,YAAY,GAAG,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;EACzD,IAAI,gBAAgB,GAAG,qBAAqB,CAAC,YAAY,CAAC,CAAC;;;EAG3D,IAAI,MAAM,GAAG;IACX,QAAQ,EAAE,MAAM,CAAC,QAAQ;GAC1B,CAAC;;;EAGF,IAAI,OAAO,GAAG;IACZ,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC;IAC7B,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC;IAC3B,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC;IACjC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC;GAChC,CAAC;;EAEF,IAAI,KAAK,GAAG,CAAC,KAAK,QAAQ,GAAG,KAAK,GAAG,QAAQ,CAAC;EAC9C,IAAI,KAAK,GAAG,CAAC,KAAK,OAAO,GAAG,MAAM,GAAG,OAAO,CAAC;;;;;EAK7C,IAAI,gBAAgB,GAAG,wBAAwB,CAAC,WAAW,CAAC,CAAC;;;;;;;;;;;EAW7D,IAAI,IAAI,GAAG,KAAK,CAAC;MACb,GAAG,GAAG,KAAK,CAAC,CAAC;EACjB,IAAI,KAAK,KAAK,QAAQ,EAAE;IACtB,GAAG,GAAG,CAAC,gBAAgB,CAAC,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;GACjD,MAAM;IACL,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC;GACnB;EACD,IAAI,KAAK,KAAK,OAAO,EAAE;IACrB,IAAI,GAAG,CAAC,gBAAgB,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;GAChD,MAAM;IACL,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;GACrB;EACD,IAAI,eAAe,IAAI,gBAAgB,EAAE;IACvC,MAAM,CAAC,gBAAgB,CAAC,GAAG,cAAc,GAAG,IAAI,GAAG,MAAM,GAAG,GAAG,GAAG,QAAQ,CAAC;IAC3E,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAClB,MAAM,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAClB,MAAM,CAAC,UAAU,GAAG,WAAW,CAAC;GACjC,MAAM;;IAEL,IAAI,SAAS,GAAG,KAAK,KAAK,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;IAC5C,IAAI,UAAU,GAAG,KAAK,KAAK,OAAO,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;IAC5C,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,SAAS,CAAC;IAChC,MAAM,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,UAAU,CAAC;IAClC,MAAM,CAAC,UAAU,GAAG,KAAK,GAAG,IAAI,GAAG,KAAK,CAAC;GAC1C;;;EAGD,IAAI,UAAU,GAAG;IACf,aAAa,EAAE,IAAI,CAAC,SAAS;GAC9B,CAAC;;;EAGF,IAAI,CAAC,UAAU,GAAGA,UAAQ,CAAC,EAAE,EAAE,UAAU,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;EAC5D,IAAI,CAAC,MAAM,GAAGA,UAAQ,CAAC,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;EAChD,IAAI,CAAC,WAAW,GAAGA,UAAQ,CAAC,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;;EAEtE,OAAO,IAAI,CAAC;CACb;;;;;;;;;;;;AAYD,SAAS,kBAAkB,CAAC,SAAS,EAAE,cAAc,EAAE,aAAa,EAAE;EACpE,IAAI,UAAU,GAAG,IAAI,CAAC,SAAS,EAAE,UAAU,IAAI,EAAE;IAC/C,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;IACrB,OAAO,IAAI,KAAK,cAAc,CAAC;GAChC,CAAC,CAAC;;EAEH,IAAI,UAAU,GAAG,CAAC,CAAC,UAAU,IAAI,SAAS,CAAC,IAAI,CAAC,UAAU,QAAQ,EAAE;IAClE,OAAO,QAAQ,CAAC,IAAI,KAAK,aAAa,IAAI,QAAQ,CAAC,OAAO,IAAI,QAAQ,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC;GACjG,CAAC,CAAC;;EAEH,IAAI,CAAC,UAAU,EAAE;IACf,IAAI,WAAW,GAAG,GAAG,GAAG,cAAc,GAAG,GAAG,CAAC;IAC7C,IAAI,SAAS,GAAG,GAAG,GAAG,aAAa,GAAG,GAAG,CAAC;IAC1C,OAAO,CAAC,IAAI,CAAC,SAAS,GAAG,2BAA2B,GAAG,WAAW,GAAG,2DAA2D,GAAG,WAAW,GAAG,GAAG,CAAC,CAAC;GACvJ;EACD,OAAO,UAAU,CAAC;CACnB;;;;;;;;;AASD,SAAS,KAAK,CAAC,IAAI,EAAE,OAAO,EAAE;;EAE5B,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,OAAO,EAAE,cAAc,CAAC,EAAE;IACzE,OAAO,IAAI,CAAC;GACb;;EAED,IAAI,YAAY,GAAG,OAAO,CAAC,OAAO,CAAC;;;EAGnC,IAAI,OAAO,YAAY,KAAK,QAAQ,EAAE;IACpC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;;;IAGhE,IAAI,CAAC,YAAY,EAAE;MACjB,OAAO,IAAI,CAAC;KACb;GACF,MAAM;;;IAGL,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE;MAChD,OAAO,CAAC,IAAI,CAAC,+DAA+D,CAAC,CAAC;MAC9E,OAAO,IAAI,CAAC;KACb;GACF;;EAED,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7C,IAAI,aAAa,GAAG,IAAI,CAAC,OAAO;MAC5B,MAAM,GAAG,aAAa,CAAC,MAAM;MAC7B,SAAS,GAAG,aAAa,CAAC,SAAS,CAAC;;EAExC,IAAI,UAAU,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;;EAE7D,IAAI,GAAG,GAAG,UAAU,GAAG,QAAQ,GAAG,OAAO,CAAC;EAC1C,IAAI,eAAe,GAAG,UAAU,GAAG,KAAK,GAAG,MAAM,CAAC;EAClD,IAAI,IAAI,GAAG,eAAe,CAAC,WAAW,EAAE,CAAC;EACzC,IAAI,OAAO,GAAG,UAAU,GAAG,MAAM,GAAG,KAAK,CAAC;EAC1C,IAAI,MAAM,GAAG,UAAU,GAAG,QAAQ,GAAG,OAAO,CAAC;EAC7C,IAAI,gBAAgB,GAAG,aAAa,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,CAAC;;;;;;;;EAQxD,IAAI,SAAS,CAAC,MAAM,CAAC,GAAG,gBAAgB,GAAG,MAAM,CAAC,IAAI,CAAC,EAAE;IACvD,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,SAAS,CAAC,MAAM,CAAC,GAAG,gBAAgB,CAAC,CAAC;GACpF;;EAED,IAAI,SAAS,CAAC,IAAI,CAAC,GAAG,gBAAgB,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE;IACvD,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,SAAS,CAAC,IAAI,CAAC,GAAG,gBAAgB,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;GAClF;;;EAGD,IAAI,MAAM,GAAG,SAAS,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,gBAAgB,GAAG,CAAC,CAAC;;;;EAIzE,IAAI,gBAAgB,GAAG,wBAAwB,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,QAAQ,GAAG,eAAe,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;EACpH,IAAI,SAAS,GAAG,MAAM,GAAG,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,GAAG,gBAAgB,CAAC;;;EAGrF,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,gBAAgB,EAAE,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;;EAE7E,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;EACjC,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,EAAE,CAAC;EACxB,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;EACjD,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;;EAEjC,OAAO,IAAI,CAAC;CACb;;;;;;;;;AASD,SAAS,oBAAoB,CAAC,SAAS,EAAE;EACvC,IAAI,SAAS,KAAK,KAAK,EAAE;IACvB,OAAO,OAAO,CAAC;GAChB,MAAM,IAAI,SAAS,KAAK,OAAO,EAAE;IAChC,OAAO,KAAK,CAAC;GACd;EACD,OAAO,SAAS,CAAC;CAClB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiCD,IAAI,UAAU,GAAG,CAAC,YAAY,EAAE,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,KAAK,EAAE,SAAS,EAAE,aAAa,EAAE,OAAO,EAAE,WAAW,EAAE,YAAY,EAAE,QAAQ,EAAE,cAAc,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY,CAAC,CAAC;;;AAGlM,IAAI,eAAe,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;;;;;;;;;;;;AAY1C,SAAS,SAAS,CAAC,SAAS,EAAE;EAC5B,IAAI,OAAO,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,KAAK,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;;EAExF,IAAI,KAAK,GAAG,eAAe,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;EAC/C,IAAI,GAAG,GAAG,eAAe,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;EACnF,OAAO,OAAO,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,GAAG,CAAC;CACtC;;AAED,IAAI,SAAS,GAAG;EACd,IAAI,EAAE,MAAM;EACZ,SAAS,EAAE,WAAW;EACtB,gBAAgB,EAAE,kBAAkB;CACrC,CAAC;;;;;;;;;AASF,SAAS,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE;;EAE3B,IAAI,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,OAAO,CAAC,EAAE;IACvD,OAAO,IAAI,CAAC;GACb;;EAED,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,iBAAiB,EAAE;;IAE7D,OAAO,IAAI,CAAC;GACb;;EAED,IAAI,UAAU,GAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,iBAAiB,CAAC,CAAC;;EAE1H,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7C,IAAI,iBAAiB,GAAG,oBAAoB,CAAC,SAAS,CAAC,CAAC;EACxD,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;;EAEnD,IAAI,SAAS,GAAG,EAAE,CAAC;;EAEnB,QAAQ,OAAO,CAAC,QAAQ;IACtB,KAAK,SAAS,CAAC,IAAI;MACjB,SAAS,GAAG,CAAC,SAAS,EAAE,iBAAiB,CAAC,CAAC;MAC3C,MAAM;IACR,KAAK,SAAS,CAAC,SAAS;MACtB,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC,CAAC;MACjC,MAAM;IACR,KAAK,SAAS,CAAC,gBAAgB;MAC7B,SAAS,GAAG,SAAS,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;MACvC,MAAM;IACR;MACE,SAAS,GAAG,OAAO,CAAC,QAAQ,CAAC;GAChC;;EAED,SAAS,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE,KAAK,EAAE;IACvC,IAAI,SAAS,KAAK,IAAI,IAAI,SAAS,CAAC,MAAM,KAAK,KAAK,GAAG,CAAC,EAAE;MACxD,OAAO,IAAI,CAAC;KACb;;IAED,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACzC,iBAAiB,GAAG,oBAAoB,CAAC,SAAS,CAAC,CAAC;;IAEpD,IAAI,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;IACxC,IAAI,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;;;IAGxC,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;IACvB,IAAI,WAAW,GAAG,SAAS,KAAK,MAAM,IAAI,KAAK,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,SAAS,KAAK,OAAO,IAAI,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,SAAS,KAAK,KAAK,IAAI,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,SAAS,KAAK,QAAQ,IAAI,KAAK,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;;IAE7U,IAAI,aAAa,GAAG,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IACvE,IAAI,cAAc,GAAG,KAAK,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;IAC1E,IAAI,YAAY,GAAG,KAAK,CAAC,aAAa,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;IACpE,IAAI,eAAe,GAAG,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;;IAE7E,IAAI,mBAAmB,GAAG,SAAS,KAAK,MAAM,IAAI,aAAa,IAAI,SAAS,KAAK,OAAO,IAAI,cAAc,IAAI,SAAS,KAAK,KAAK,IAAI,YAAY,IAAI,SAAS,KAAK,QAAQ,IAAI,eAAe,CAAC;;;IAG/L,IAAI,UAAU,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;IAC7D,IAAI,gBAAgB,GAAG,CAAC,CAAC,OAAO,CAAC,cAAc,KAAK,UAAU,IAAI,SAAS,KAAK,OAAO,IAAI,aAAa,IAAI,UAAU,IAAI,SAAS,KAAK,KAAK,IAAI,cAAc,IAAI,CAAC,UAAU,IAAI,SAAS,KAAK,OAAO,IAAI,YAAY,IAAI,CAAC,UAAU,IAAI,SAAS,KAAK,KAAK,IAAI,eAAe,CAAC,CAAC;;IAElR,IAAI,WAAW,IAAI,mBAAmB,IAAI,gBAAgB,EAAE;;MAE1D,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;;MAEpB,IAAI,WAAW,IAAI,mBAAmB,EAAE;QACtC,SAAS,GAAG,SAAS,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;OAClC;;MAED,IAAI,gBAAgB,EAAE;QACpB,SAAS,GAAG,oBAAoB,CAAC,SAAS,CAAC,CAAC;OAC7C;;MAED,IAAI,CAAC,SAAS,GAAG,SAAS,IAAI,SAAS,GAAG,GAAG,GAAG,SAAS,GAAG,EAAE,CAAC,CAAC;;;;MAIhE,IAAI,CAAC,OAAO,CAAC,MAAM,GAAGA,UAAQ,CAAC,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;;MAExI,IAAI,GAAG,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,IAAI,EAAE,MAAM,CAAC,CAAC;KAC5D;GACF,CAAC,CAAC;EACH,OAAO,IAAI,CAAC;CACb;;;;;;;;;AASD,SAAS,YAAY,CAAC,IAAI,EAAE;EAC1B,IAAI,aAAa,GAAG,IAAI,CAAC,OAAO;MAC5B,MAAM,GAAG,aAAa,CAAC,MAAM;MAC7B,SAAS,GAAG,aAAa,CAAC,SAAS,CAAC;;EAExC,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7C,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;EACvB,IAAI,UAAU,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;EAC7D,IAAI,IAAI,GAAG,UAAU,GAAG,OAAO,GAAG,QAAQ,CAAC;EAC3C,IAAI,MAAM,GAAG,UAAU,GAAG,MAAM,GAAG,KAAK,CAAC;EACzC,IAAI,WAAW,GAAG,UAAU,GAAG,OAAO,GAAG,QAAQ,CAAC;;EAElD,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE;IAC3C,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC;GAC9E;EACD,IAAI,MAAM,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE;IAC3C,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;GACtD;;EAED,OAAO,IAAI,CAAC;CACb;;;;;;;;;;;;;;AAcD,SAAS,OAAO,CAAC,GAAG,EAAE,WAAW,EAAE,aAAa,EAAE,gBAAgB,EAAE;;EAElE,IAAI,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAC;EACnD,IAAI,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;EACtB,IAAI,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;;;EAGpB,IAAI,CAAC,KAAK,EAAE;IACV,OAAO,GAAG,CAAC;GACZ;;EAED,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;IAC3B,IAAI,OAAO,GAAG,KAAK,CAAC,CAAC;IACrB,QAAQ,IAAI;MACV,KAAK,IAAI;QACP,OAAO,GAAG,aAAa,CAAC;QACxB,MAAM;MACR,KAAK,GAAG,CAAC;MACT,KAAK,IAAI,CAAC;MACV;QACE,OAAO,GAAG,gBAAgB,CAAC;KAC9B;;IAED,IAAI,IAAI,GAAG,aAAa,CAAC,OAAO,CAAC,CAAC;IAClC,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC;GACxC,MAAM,IAAI,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,EAAE;;IAEzC,IAAI,IAAI,GAAG,KAAK,CAAC,CAAC;IAClB,IAAI,IAAI,KAAK,IAAI,EAAE;MACjB,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,eAAe,CAAC,YAAY,EAAE,MAAM,CAAC,WAAW,IAAI,CAAC,CAAC,CAAC;KACjF,MAAM;MACL,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,eAAe,CAAC,WAAW,EAAE,MAAM,CAAC,UAAU,IAAI,CAAC,CAAC,CAAC;KAC/E;IACD,OAAO,IAAI,GAAG,GAAG,GAAG,KAAK,CAAC;GAC3B,MAAM;;;IAGL,OAAO,KAAK,CAAC;GACd;CACF;;;;;;;;;;;;;AAaD,SAAS,WAAW,CAAC,MAAM,EAAE,aAAa,EAAE,gBAAgB,EAAE,aAAa,EAAE;EAC3E,IAAI,OAAO,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;;;;EAKrB,IAAI,SAAS,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;;;;EAIhE,IAAI,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,UAAU,IAAI,EAAE;IAC1D,OAAO,IAAI,CAAC,IAAI,EAAE,CAAC;GACpB,CAAC,CAAC;;;;EAIH,IAAI,OAAO,GAAG,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,UAAU,IAAI,EAAE;IAC9D,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;GACnC,CAAC,CAAC,CAAC;;EAEJ,IAAI,SAAS,CAAC,OAAO,CAAC,IAAI,SAAS,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;IAChE,OAAO,CAAC,IAAI,CAAC,8EAA8E,CAAC,CAAC;GAC9F;;;;EAID,IAAI,UAAU,GAAG,aAAa,CAAC;EAC/B,IAAI,GAAG,GAAG,OAAO,KAAK,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;;;EAGzM,GAAG,GAAG,GAAG,CAAC,GAAG,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE;;IAEjC,IAAI,WAAW,GAAG,CAAC,KAAK,KAAK,CAAC,GAAG,CAAC,SAAS,GAAG,SAAS,IAAI,QAAQ,GAAG,OAAO,CAAC;IAC9E,IAAI,iBAAiB,GAAG,KAAK,CAAC;IAC9B,OAAO,EAAE;;;KAGR,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE;MACtB,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;QAC1D,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;QACpB,iBAAiB,GAAG,IAAI,CAAC;QACzB,OAAO,CAAC,CAAC;OACV,MAAM,IAAI,iBAAiB,EAAE;QAC5B,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;QACrB,iBAAiB,GAAG,KAAK,CAAC;QAC1B,OAAO,CAAC,CAAC;OACV,MAAM;QACL,OAAO,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;OACpB;KACF,EAAE,EAAE,CAAC;;KAEL,GAAG,CAAC,UAAU,GAAG,EAAE;MAClB,OAAO,OAAO,CAAC,GAAG,EAAE,WAAW,EAAE,aAAa,EAAE,gBAAgB,CAAC,CAAC;KACnE,CAAC,CAAC;GACJ,CAAC,CAAC;;;EAGH,GAAG,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,KAAK,EAAE;IAC/B,EAAE,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE,MAAM,EAAE;MACjC,IAAI,SAAS,CAAC,IAAI,CAAC,EAAE;QACnB,OAAO,CAAC,KAAK,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;OAC5D;KACF,CAAC,CAAC;GACJ,CAAC,CAAC;EACH,OAAO,OAAO,CAAC;CAChB;;;;;;;;;;;AAWD,SAAS,MAAM,CAAC,IAAI,EAAE,IAAI,EAAE;EAC1B,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;EACzB,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS;MAC1B,aAAa,GAAG,IAAI,CAAC,OAAO;MAC5B,MAAM,GAAG,aAAa,CAAC,MAAM;MAC7B,SAAS,GAAG,aAAa,CAAC,SAAS,CAAC;;EAExC,IAAI,aAAa,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;;EAE5C,IAAI,OAAO,GAAG,KAAK,CAAC,CAAC;EACrB,IAAI,SAAS,CAAC,CAAC,MAAM,CAAC,EAAE;IACtB,OAAO,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;GACxB,MAAM;IACL,OAAO,GAAG,WAAW,CAAC,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,aAAa,CAAC,CAAC;GACjE;;EAED,IAAI,aAAa,KAAK,MAAM,EAAE;IAC5B,MAAM,CAAC,GAAG,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;IACzB,MAAM,CAAC,IAAI,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;GAC3B,MAAM,IAAI,aAAa,KAAK,OAAO,EAAE;IACpC,MAAM,CAAC,GAAG,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;IACzB,MAAM,CAAC,IAAI,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;GAC3B,MAAM,IAAI,aAAa,KAAK,KAAK,EAAE;IAClC,MAAM,CAAC,IAAI,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;IAC1B,MAAM,CAAC,GAAG,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;GAC1B,MAAM,IAAI,aAAa,KAAK,QAAQ,EAAE;IACrC,MAAM,CAAC,IAAI,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;IAC1B,MAAM,CAAC,GAAG,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC;GAC1B;;EAED,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;EACrB,OAAO,IAAI,CAAC;CACb;;;;;;;;;AASD,SAAS,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE;EACtC,IAAI,iBAAiB,GAAG,OAAO,CAAC,iBAAiB,IAAI,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;;;;;EAK3F,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,KAAK,iBAAiB,EAAE;IACjD,iBAAiB,GAAG,eAAe,CAAC,iBAAiB,CAAC,CAAC;GACxD;;EAED,IAAI,UAAU,GAAG,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,OAAO,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAAC;EAClH,OAAO,CAAC,UAAU,GAAG,UAAU,CAAC;;EAEhC,IAAI,KAAK,GAAG,OAAO,CAAC,QAAQ,CAAC;EAC7B,IAAI,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;;EAEjC,IAAI,KAAK,GAAG;IACV,OAAO,EAAE,SAAS,OAAO,CAAC,SAAS,EAAE;MACnC,IAAI,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;MAC9B,IAAI,MAAM,CAAC,SAAS,CAAC,GAAG,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE;QAC7E,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC;OAC5D;MACD,OAAO,cAAc,CAAC,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;KAC7C;IACD,SAAS,EAAE,SAAS,SAAS,CAAC,SAAS,EAAE;MACvC,IAAI,QAAQ,GAAG,SAAS,KAAK,OAAO,GAAG,MAAM,GAAG,KAAK,CAAC;MACtD,IAAI,KAAK,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;MAC7B,IAAI,MAAM,CAAC,SAAS,CAAC,GAAG,UAAU,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,mBAAmB,EAAE;QAC7E,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,UAAU,CAAC,SAAS,CAAC,IAAI,SAAS,KAAK,OAAO,GAAG,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;OACpH;MACD,OAAO,cAAc,CAAC,EAAE,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC;KAC5C;GACF,CAAC;;EAEF,KAAK,CAAC,OAAO,CAAC,UAAU,SAAS,EAAE;IACjC,IAAI,IAAI,GAAG,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,GAAG,SAAS,GAAG,WAAW,CAAC;IAC/E,MAAM,GAAGA,UAAQ,CAAC,EAAE,EAAE,MAAM,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC;GACvD,CAAC,CAAC;;EAEH,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,MAAM,CAAC;;EAE7B,OAAO,IAAI,CAAC;CACb;;;;;;;;;AASD,SAAS,KAAK,CAAC,IAAI,EAAE;EACnB,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;EAC/B,IAAI,aAAa,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5C,IAAI,cAAc,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;;;EAG7C,IAAI,cAAc,EAAE;IAClB,IAAI,aAAa,GAAG,IAAI,CAAC,OAAO;QAC5B,SAAS,GAAG,aAAa,CAAC,SAAS;QACnC,MAAM,GAAG,aAAa,CAAC,MAAM,CAAC;;IAElC,IAAI,UAAU,GAAG,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;IACjE,IAAI,IAAI,GAAG,UAAU,GAAG,MAAM,GAAG,KAAK,CAAC;IACvC,IAAI,WAAW,GAAG,UAAU,GAAG,OAAO,GAAG,QAAQ,CAAC;;IAElD,IAAI,YAAY,GAAG;MACjB,KAAK,EAAE,cAAc,CAAC,EAAE,EAAE,IAAI,EAAE,SAAS,CAAC,IAAI,CAAC,CAAC;MAChD,GAAG,EAAE,cAAc,CAAC,EAAE,EAAE,IAAI,EAAE,SAAS,CAAC,IAAI,CAAC,GAAG,SAAS,CAAC,WAAW,CAAC,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC;KAC9F,CAAC;;IAEF,IAAI,CAAC,OAAO,CAAC,MAAM,GAAGA,UAAQ,CAAC,EAAE,EAAE,MAAM,EAAE,YAAY,CAAC,cAAc,CAAC,CAAC,CAAC;GAC1E;;EAED,OAAO,IAAI,CAAC;CACb;;;;;;;;;AASD,SAAS,IAAI,CAAC,IAAI,EAAE;EAClB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,MAAM,EAAE,iBAAiB,CAAC,EAAE;IAC3E,OAAO,IAAI,CAAC;GACb;;EAED,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;EACrC,IAAI,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,UAAU,QAAQ,EAAE;IAC5D,OAAO,QAAQ,CAAC,IAAI,KAAK,iBAAiB,CAAC;GAC5C,CAAC,CAAC,UAAU,CAAC;;EAEd,IAAI,OAAO,CAAC,MAAM,GAAG,KAAK,CAAC,GAAG,IAAI,OAAO,CAAC,IAAI,GAAG,KAAK,CAAC,KAAK,IAAI,OAAO,CAAC,GAAG,GAAG,KAAK,CAAC,MAAM,IAAI,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,EAAE;;IAExH,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,EAAE;MACtB,OAAO,IAAI,CAAC;KACb;;IAED,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACjB,IAAI,CAAC,UAAU,CAAC,qBAAqB,CAAC,GAAG,EAAE,CAAC;GAC7C,MAAM;;IAEL,IAAI,IAAI,CAAC,IAAI,KAAK,KAAK,EAAE;MACvB,OAAO,IAAI,CAAC;KACb;;IAED,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;IAClB,IAAI,CAAC,UAAU,CAAC,qBAAqB,CAAC,GAAG,KAAK,CAAC;GAChD;;EAED,OAAO,IAAI,CAAC;CACb;;;;;;;;;AASD,SAAS,KAAK,CAAC,IAAI,EAAE;EACnB,IAAI,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;EAC/B,IAAI,aAAa,GAAG,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;EAC5C,IAAI,aAAa,GAAG,IAAI,CAAC,OAAO;MAC5B,MAAM,GAAG,aAAa,CAAC,MAAM;MAC7B,SAAS,GAAG,aAAa,CAAC,SAAS,CAAC;;EAExC,IAAI,OAAO,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;;EAE9D,IAAI,cAAc,GAAG,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC;;EAEnE,MAAM,CAAC,OAAO,GAAG,MAAM,GAAG,KAAK,CAAC,GAAG,SAAS,CAAC,aAAa,CAAC,IAAI,cAAc,GAAG,MAAM,CAAC,OAAO,GAAG,OAAO,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;;EAE1H,IAAI,CAAC,SAAS,GAAG,oBAAoB,CAAC,SAAS,CAAC,CAAC;EACjD,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC;;EAE5C,OAAO,IAAI,CAAC;CACb;;;;;;;;;;;;;;;;;;;;;;;AAuBD,IAAI,SAAS,GAAG;;;;;;;;;EASd,KAAK,EAAE;;IAEL,KAAK,EAAE,GAAG;;IAEV,OAAO,EAAE,IAAI;;IAEb,EAAE,EAAE,KAAK;GACV;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAwCD,MAAM,EAAE;;IAEN,KAAK,EAAE,GAAG;;IAEV,OAAO,EAAE,IAAI;;IAEb,EAAE,EAAE,MAAM;;;;IAIV,MAAM,EAAE,CAAC;GACV;;;;;;;;;;;;;;;;;;;EAmBD,eAAe,EAAE;;IAEf,KAAK,EAAE,GAAG;;IAEV,OAAO,EAAE,IAAI;;IAEb,EAAE,EAAE,eAAe;;;;;;IAMnB,QAAQ,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,CAAC;;;;;;;IAO5C,OAAO,EAAE,CAAC;;;;;;IAMV,iBAAiB,EAAE,cAAc;GAClC;;;;;;;;;;;EAWD,YAAY,EAAE;;IAEZ,KAAK,EAAE,GAAG;;IAEV,OAAO,EAAE,IAAI;;IAEb,EAAE,EAAE,YAAY;GACjB;;;;;;;;;;;;EAYD,KAAK,EAAE;;IAEL,KAAK,EAAE,GAAG;;IAEV,OAAO,EAAE,IAAI;;IAEb,EAAE,EAAE,KAAK;;IAET,OAAO,EAAE,WAAW;GACrB;;;;;;;;;;;;;EAaD,IAAI,EAAE;;IAEJ,KAAK,EAAE,GAAG;;IAEV,OAAO,EAAE,IAAI;;IAEb,EAAE,EAAE,IAAI;;;;;;;IAOR,QAAQ,EAAE,MAAM;;;;;IAKhB,OAAO,EAAE,CAAC;;;;;;;IAOV,iBAAiB,EAAE,UAAU;GAC9B;;;;;;;;;EASD,KAAK,EAAE;;IAEL,KAAK,EAAE,GAAG;;IAEV,OAAO,EAAE,KAAK;;IAEd,EAAE,EAAE,KAAK;GACV;;;;;;;;;;;;EAYD,IAAI,EAAE;;IAEJ,KAAK,EAAE,GAAG;;IAEV,OAAO,EAAE,IAAI;;IAEb,EAAE,EAAE,IAAI;GACT;;;;;;;;;;;;;;;;;EAiBD,YAAY,EAAE;;IAEZ,KAAK,EAAE,GAAG;;IAEV,OAAO,EAAE,IAAI;;IAEb,EAAE,EAAE,YAAY;;;;;;IAMhB,eAAe,EAAE,IAAI;;;;;;IAMrB,CAAC,EAAE,QAAQ;;;;;;IAMX,CAAC,EAAE,OAAO;GACX;;;;;;;;;;;;;;;;;EAiBD,UAAU,EAAE;;IAEV,KAAK,EAAE,GAAG;;IAEV,OAAO,EAAE,IAAI;;IAEb,EAAE,EAAE,UAAU;;IAEd,MAAM,EAAE,gBAAgB;;;;;;;IAOxB,eAAe,EAAE,SAAS;GAC3B;CACF,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqCF,IAAI,QAAQ,GAAG;;;;;EAKb,SAAS,EAAE,QAAQ;;;;;;EAMnB,aAAa,EAAE,IAAI;;;;;;;EAOnB,eAAe,EAAE,KAAK;;;;;;;;EAQtB,QAAQ,EAAE,SAAS,QAAQ,GAAG,EAAE;;;;;;;;;;EAUhC,QAAQ,EAAE,SAAS,QAAQ,GAAG,EAAE;;;;;;;EAOhC,SAAS,EAAE,SAAS;CACrB,CAAC;;;;;;;;;;;;;;AAcF,IAAI,MAAM,GAAG,YAAY;;;;;;;;;EASvB,SAAS,MAAM,CAAC,SAAS,EAAE,MAAM,EAAE;IACjC,IAAI,KAAK,GAAG,IAAI,CAAC;;IAEjB,IAAI,OAAO,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,KAAK,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;IACrF,cAAc,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;;IAE7B,IAAI,CAAC,cAAc,GAAG,YAAY;MAChC,OAAO,qBAAqB,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;KAC5C,CAAC;;;IAGF,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;;;IAG/C,IAAI,CAAC,OAAO,GAAGA,UAAQ,CAAC,EAAE,EAAE,MAAM,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;;;IAGtD,IAAI,CAAC,KAAK,GAAG;MACX,WAAW,EAAE,KAAK;MAClB,SAAS,EAAE,KAAK;MAChB,aAAa,EAAE,EAAE;KAClB,CAAC;;;IAGF,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC,MAAM,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC;IAC7D,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC;;;IAGjD,IAAI,CAAC,OAAO,CAAC,SAAS,GAAG,EAAE,CAAC;IAC5B,MAAM,CAAC,IAAI,CAACA,UAAQ,CAAC,EAAE,EAAE,MAAM,CAAC,QAAQ,CAAC,SAAS,EAAE,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,UAAU,IAAI,EAAE;MAC9F,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,GAAGA,UAAQ,CAAC,EAAE,EAAE,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,OAAO,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;KACvI,CAAC,CAAC;;;IAGH,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,UAAU,IAAI,EAAE;MACvE,OAAOA,UAAQ,CAAC;QACd,IAAI,EAAE,IAAI;OACX,EAAE,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;KACnC,CAAC;;KAED,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE;MACpB,OAAO,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;KAC1B,CAAC,CAAC;;;;;;IAMH,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,eAAe,EAAE;MAChD,IAAI,eAAe,CAAC,OAAO,IAAI,UAAU,CAAC,eAAe,CAAC,MAAM,CAAC,EAAE;QACjE,eAAe,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,MAAM,EAAE,KAAK,CAAC,OAAO,EAAE,eAAe,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;OACpG;KACF,CAAC,CAAC;;;IAGH,IAAI,CAAC,MAAM,EAAE,CAAC;;IAEd,IAAI,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC;IAC/C,IAAI,aAAa,EAAE;;MAEjB,IAAI,CAAC,oBAAoB,EAAE,CAAC;KAC7B;;IAED,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,aAAa,CAAC;GAC1C;;;;;;EAMDD,aAAW,CAAC,MAAM,EAAE,CAAC;IACnB,GAAG,EAAE,QAAQ;IACb,KAAK,EAAE,SAAS,SAAS,GAAG;MAC1B,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KAC1B;GACF,EAAE;IACD,GAAG,EAAE,SAAS;IACd,KAAK,EAAE,SAAS,UAAU,GAAG;MAC3B,OAAO,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KAC3B;GACF,EAAE;IACD,GAAG,EAAE,sBAAsB;IAC3B,KAAK,EAAE,SAAS,uBAAuB,GAAG;MACxC,OAAO,oBAAoB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KACxC;GACF,EAAE;IACD,GAAG,EAAE,uBAAuB;IAC5B,KAAK,EAAE,SAAS,wBAAwB,GAAG;MACzC,OAAO,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;KACzC;;;;;;;;;;;;;;;;;;;;;;;;;;GA0BF,CAAC,CAAC,CAAC;EACJ,OAAO,MAAM,CAAC;CACf,EAAE,CAAC;;;;;;;;;;;;;;;;;;;;;;;AAuBJ,MAAM,CAAC,KAAK,GAAG,CAAC,OAAO,MAAM,KAAK,WAAW,GAAG,MAAM,GAAG,MAAM,EAAE,WAAW,CAAC;AAC7E,MAAM,CAAC,UAAU,GAAG,UAAU,CAAC;AAC/B,MAAM,CAAC,QAAQ,GAAG,QAAQ,CAAC;;AC/3E3B;;;;;;;AAOA,IAAME,WAAY,YAAM;;;;;MAMlB,OAAOC,MAAP,KAAkB,WAAtB,EAAmC;UAC3B,IAAIvN,KAAJ,CAAU,8DAAV,CAAN;;;;;;;;;MASIG,OAA2B,UAAjC;MACMC,UAA2B,cAAjC;MACMC,WAA2B,aAAjC;MACMC,kBAA+BD,QAArC;MACME,eAA2B,WAAjC;MACMC,qBAA2B/D,EAAEuB,EAAF,CAAKmC,IAAL,CAAjC;MACMqN,iBAA2B,EAAjC,CAtBsB;;MAuBhBC,gBAA2B,EAAjC,CAvBsB;;MAwBhBC,cAA2B,CAAjC,CAxBsB;;MAyBhBC,mBAA2B,EAAjC,CAzBsB;;MA0BhBC,qBAA2B,EAAjC,CA1BsB;;MA2BhBC,2BAA2B,CAAjC,CA3BsB;;MA4BhBC,iBAA2B,IAAIhO,MAAJ,CAAc6N,gBAAd,SAAkCC,kBAAlC,SAAwDJ,cAAxD,CAAjC;MAEM7M,QAAQ;mBACcL,SADd;uBAEgBA,SAFhB;mBAGcA,SAHd;qBAIeA,SAJf;qBAKeA,SALf;8BAMeA,SAA3B,GAAuCC,YAN3B;kCAOiBD,SAA7B,GAAyCC,YAP7B;8BAQeD,SAA3B,GAAuCC;GARzC;MAWMK,YAAY;cACJ,UADI;UAEJ,MAFI;YAGJ,QAHI;eAIJ,qBAJI;cAKJ;GALd;MAQMF,WAAW;iBACC,0BADD;gBAEC,gBAFD;UAGC,gBAHD;gBAIC,aAJD;mBAKC;GALlB;MAQMqN,gBAAgB;SACR,WADQ;YAER,SAFQ;YAGR,cAHQ;eAIR;GAJd;MAOMpJ,UAAU;YACA,CADA;UAEA;GAFhB;MAKMC,cAAc;YACJ,0BADI;UAEJ;;;;;;;GAFhB;;MAYM0I,QAjFgB;;;sBAmFR9O,OAAZ,EAAqBY,MAArB,EAA6B;WACtByB,QAAL,GAAiBrC,OAAjB;WACKwP,OAAL,GAAiB,IAAjB;WACK5I,OAAL,GAAiB,KAAKC,UAAL,CAAgBjG,MAAhB,CAAjB;WACK6O,KAAL,GAAiB,KAAKC,eAAL,EAAjB;WACKC,SAAL,GAAiB,KAAKC,aAAL,EAAjB;;WAEK5I,kBAAL;KA1FkB;;;;;;WA8GpBtC,MA9GoB,qBA8GX;UACH,KAAKrC,QAAL,CAAcwN,QAAd,IAA0B5R,EAAE,KAAKoE,QAAP,EAAiBkB,QAAjB,CAA0BnB,UAAU0N,QAApC,CAA9B,EAA6E;;;;UAIvE9M,SAAW8L,SAASiB,qBAAT,CAA+B,KAAK1N,QAApC,CAAjB;;UACM2N,WAAW/R,EAAE,KAAKwR,KAAP,EAAclM,QAAd,CAAuBnB,UAAUkB,IAAjC,CAAjB;;eAES2M,WAAT;;UAEID,QAAJ,EAAc;;;;UAIRhG,gBAAgB;uBACJ,KAAK3H;OADvB;UAGM6N,YAAYjS,EAAEkE,KAAF,CAAQA,MAAMmB,IAAd,EAAoB0G,aAApB,CAAlB;QAEEhH,MAAF,EAAUxC,OAAV,CAAkB0P,SAAlB;;UAEIA,UAAUvN,kBAAV,EAAJ,EAAoC;;;;UAIhC3C,UAAU,KAAKqC,QAAnB,CAzBO;;UA2BHpE,EAAE+E,MAAF,EAAUO,QAAV,CAAmBnB,UAAU+N,MAA7B,CAAJ,EAA0C;YACpClS,EAAE,KAAKwR,KAAP,EAAclM,QAAd,CAAuBnB,UAAUgO,QAAjC,KAA8CnS,EAAE,KAAKwR,KAAP,EAAclM,QAAd,CAAuBnB,UAAUiO,SAAjC,CAAlD,EAA+F;oBACnFrN,MAAV;;;;WAGCwM,OAAL,GAAe,IAAIT,MAAJ,CAAW/O,OAAX,EAAoB,KAAKyP,KAAzB,EAAgC,KAAKa,gBAAL,EAAhC,CAAf,CAhCO;;;;;UAsCH,kBAAkBzR,SAASkK,eAA3B,IACD,CAAC9K,EAAE+E,MAAF,EAAUC,OAAV,CAAkBf,SAASqO,UAA3B,EAAuClQ,MAD3C,EACmD;UAC/C,MAAF,EAAUmK,QAAV,GAAqBpG,EAArB,CAAwB,WAAxB,EAAqC,IAArC,EAA2CnG,EAAEuS,IAA7C;;;WAGGnO,QAAL,CAAckD,KAAd;;WACKlD,QAAL,CAAcmD,YAAd,CAA2B,eAA3B,EAA4C,IAA5C;;QAEE,KAAKiK,KAAP,EAAchK,WAAd,CAA0BrD,UAAUkB,IAApC;QACEN,MAAF,EACGyC,WADH,CACerD,UAAUkB,IADzB,EAEG9C,OAFH,CAEWvC,EAAEkE,KAAF,CAAQA,MAAMqL,KAAd,EAAqBxD,aAArB,CAFX;KA7JkB;;WAkKpBnH,OAlKoB,sBAkKV;QACNC,UAAF,CAAa,KAAKT,QAAlB,EAA4BR,QAA5B;QACE,KAAKQ,QAAP,EAAiBkG,GAAjB,CAAqBzG,SAArB;WACKO,QAAL,GAAgB,IAAhB;WACKoN,KAAL,GAAa,IAAb;;UACI,KAAKD,OAAL,KAAiB,IAArB,EAA2B;aACpBA,OAAL,CAAaiB,OAAb;;;WAEGjB,OAAL,GAAe,IAAf;KA1KkB;;WA6KpBkB,MA7KoB,qBA6KX;WACFf,SAAL,GAAiB,KAAKC,aAAL,EAAjB;;UACI,KAAKJ,OAAL,KAAiB,IAArB,EAA2B;aACpBA,OAAL,CAAamB,cAAb;;KAhLgB;;;WAsLpB3J,kBAtLoB,iCAsLC;;;QACjB,KAAK3E,QAAP,EAAiB+B,EAAjB,CAAoBjC,MAAMyO,KAA1B,EAAiC,UAAC5S,KAAD,EAAW;cACpCmG,cAAN;cACM0M,eAAN;;cACKnM,MAAL;OAHF;KAvLkB;;WA8LpBmC,UA9LoB,uBA8LTjG,MA9LS,EA8LD;eACR3C,EAAEuK,MAAF,CACP,EADO,EAEP,KAAKsI,WAAL,CAAiB3K,OAFV,EAGPlI,EAAE,KAAKoE,QAAP,EAAiB2B,IAAjB,EAHO,EAIPpD,MAJO,CAAT;WAOK6H,eAAL,CACE9G,IADF,EAEEf,MAFF,EAGE,KAAKkQ,WAAL,CAAiB1K,WAHnB;aAMOxF,MAAP;KA5MkB;;WA+MpB8O,eA/MoB,8BA+MF;UACZ,CAAC,KAAKD,KAAV,EAAiB;YACTzM,SAAS8L,SAASiB,qBAAT,CAA+B,KAAK1N,QAApC,CAAf;;aACKoN,KAAL,GAAaxR,EAAE+E,MAAF,EAAU5C,IAAV,CAAe8B,SAAS6O,IAAxB,EAA8B,CAA9B,CAAb;;;aAEK,KAAKtB,KAAZ;KApNkB;;WAuNpBuB,aAvNoB,4BAuNJ;UACRC,kBAAkBhT,EAAE,KAAKoE,QAAP,EAAiBW,MAAjB,EAAxB;UACIkO,YAAY3B,cAAc4B,MAA9B,CAFc;;UAKVF,gBAAgB1N,QAAhB,CAAyBnB,UAAU+N,MAAnC,CAAJ,EAAgD;oBAClCZ,cAAc6B,GAA1B;;YACInT,EAAE,KAAKwR,KAAP,EAAclM,QAAd,CAAuBnB,UAAUiO,SAAjC,CAAJ,EAAiD;sBACnCd,cAAc8B,MAA1B;;OAHJ,MAKO,IAAIpT,EAAE,KAAKwR,KAAP,EAAclM,QAAd,CAAuBnB,UAAUiO,SAAjC,CAAJ,EAAiD;oBAC1Cd,cAAc+B,SAA1B;;;aAEKJ,SAAP;KApOkB;;WAuOpBtB,aAvOoB,4BAuOJ;aACP3R,EAAE,KAAKoE,QAAP,EAAiBY,OAAjB,CAAyB,SAAzB,EAAoC5C,MAApC,GAA6C,CAApD;KAxOkB;;WA2OpBiQ,gBA3OoB,+BA2OD;;;UACXiB,aAAa,EAAnB;;UACI,OAAO,KAAK3K,OAAL,CAAa4K,MAApB,KAA+B,UAAnC,EAA+C;mBAClChS,EAAX,GAAgB,UAACwE,IAAD,EAAU;eACnByN,OAAL,GAAexT,EAAEuK,MAAF,CAAS,EAAT,EAAaxE,KAAKyN,OAAlB,EAA2B,OAAK7K,OAAL,CAAa4K,MAAb,CAAoBxN,KAAKyN,OAAzB,KAAqC,EAAhE,CAAf;iBACOzN,IAAP;SAFF;OADF,MAKO;mBACMwN,MAAX,GAAoB,KAAK5K,OAAL,CAAa4K,MAAjC;;;UAEIE,eAAe;mBACP,KAAKV,aAAL,EADO;mBAEP;kBACDO,UADC;gBAEH;qBACK,KAAK3K,OAAL,CAAa+K;;SALR;;OAArB;;UAWI,KAAKhC,SAAT,EAAoB;qBACLiC,SAAb,CAAuBC,UAAvB,GAAoC;mBACzB,CAAC,KAAKlC;SADjB;;;aAIK+B,YAAP;KArQkB;;;aA0Qb7N,gBA1Qa,6BA0QIjD,MA1QJ,EA0QY;aACvB,KAAKkD,IAAL,CAAU,YAAY;YACvBE,OAAO/F,EAAE,IAAF,EAAQ+F,IAAR,CAAanC,QAAb,CAAX;;YACM+E,UAAU,OAAOhG,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC,IAAtD;;YAEI,CAACoD,IAAL,EAAW;iBACF,IAAI8K,QAAJ,CAAa,IAAb,EAAmBlI,OAAnB,CAAP;YACE,IAAF,EAAQ5C,IAAR,CAAanC,QAAb,EAAuBmC,IAAvB;;;YAGE,OAAOpD,MAAP,KAAkB,QAAtB,EAAgC;cAC1B,OAAOoD,KAAKpD,MAAL,CAAP,KAAwB,WAA5B,EAAyC;kBACjC,IAAIY,KAAJ,wBAA8BZ,MAA9B,QAAN;;;eAEGA,MAAL;;OAbG,CAAP;KA3QkB;;aA6RbqP,WA7Ra,wBA6RDjS,KA7RC,EA6RM;UACpBA,UAAUA,MAAMmL,KAAN,KAAgBkG,wBAAhB,IACZrR,MAAMgH,IAAN,KAAe,OAAf,IAA0BhH,MAAMmL,KAAN,KAAgB+F,WADxC,CAAJ,EAC0D;;;;UAIpD4C,UAAU7T,EAAEmL,SAAF,CAAYnL,EAAEiE,SAAS2C,WAAX,CAAZ,CAAhB;;WACK,IAAIsH,IAAI,CAAb,EAAgBA,IAAI2F,QAAQzR,MAA5B,EAAoC8L,GAApC,EAAyC;YACjCnJ,SAAgB8L,SAASiB,qBAAT,CAA+B+B,QAAQ3F,CAAR,CAA/B,CAAtB;;YACM4F,UAAgB9T,EAAE6T,QAAQ3F,CAAR,CAAF,EAAcnI,IAAd,CAAmBnC,QAAnB,CAAtB;YACMmI,gBAAgB;yBACJ8H,QAAQ3F,CAAR;SADlB;;YAII,CAAC4F,OAAL,EAAc;;;;YAIRC,eAAeD,QAAQtC,KAA7B;;YACI,CAACxR,EAAE+E,MAAF,EAAUO,QAAV,CAAmBnB,UAAUkB,IAA7B,CAAL,EAAyC;;;;YAIrCtF,UAAUA,MAAMgH,IAAN,KAAe,OAAf,IACV,kBAAkBzD,IAAlB,CAAuBvD,MAAME,MAAN,CAAagL,OAApC,CADU,IACsClL,MAAMgH,IAAN,KAAe,OAAf,IAA0BhH,MAAMmL,KAAN,KAAgB+F,WAD1F,KAEGjR,EAAEqH,QAAF,CAAWtC,MAAX,EAAmBhF,MAAME,MAAzB,CAFP,EAEyC;;;;YAInC+T,YAAYhU,EAAEkE,KAAF,CAAQA,MAAMyL,IAAd,EAAoB5D,aAApB,CAAlB;UACEhH,MAAF,EAAUxC,OAAV,CAAkByR,SAAlB;;YACIA,UAAUtP,kBAAV,EAAJ,EAAoC;;SAxBG;;;;YA8BnC,kBAAkB9D,SAASkK,eAA/B,EAAgD;YAC5C,MAAF,EAAUyB,QAAV,GAAqBjC,GAArB,CAAyB,WAAzB,EAAsC,IAAtC,EAA4CtK,EAAEuS,IAA9C;;;gBAGMrE,CAAR,EAAW3G,YAAX,CAAwB,eAAxB,EAAyC,OAAzC;UAEEwM,YAAF,EAAgB3O,WAAhB,CAA4BjB,UAAUkB,IAAtC;UACEN,MAAF,EACGK,WADH,CACejB,UAAUkB,IADzB,EAEG9C,OAFH,CAEWvC,EAAEkE,KAAF,CAAQA,MAAM4L,MAAd,EAAsB/D,aAAtB,CAFX;;KAzUgB;;aA+Ub+F,qBA/Ua,kCA+US/P,OA/UT,EA+UkB;UAChCgD,MAAJ;UACM/C,WAAW7C,KAAK2F,sBAAL,CAA4B/C,OAA5B,CAAjB;;UAEIC,QAAJ,EAAc;iBACHhC,EAAEgC,QAAF,EAAY,CAAZ,CAAT;;;aAGK+C,UAAUhD,QAAQkS,UAAzB;KAvVkB;;aA0VbC,sBA1Va,mCA0VUnU,KA1VV,EA0ViB;UAC/B,CAACsR,eAAe/N,IAAf,CAAoBvD,MAAMmL,KAA1B,CAAD,IAAqC,UAAU5H,IAAV,CAAevD,MAAME,MAAN,CAAagL,OAA5B,KAAwClL,MAAMmL,KAAN,KAAgB8F,aAA7F,IACD,kBAAkB1N,IAAlB,CAAuBvD,MAAME,MAAN,CAAagL,OAApC,CADH,EACiD;;;;YAI3C/E,cAAN;YACM0M,eAAN;;UAEI,KAAKhB,QAAL,IAAiB5R,EAAE,IAAF,EAAQsF,QAAR,CAAiBnB,UAAU0N,QAA3B,CAArB,EAA2D;;;;UAIrD9M,SAAW8L,SAASiB,qBAAT,CAA+B,IAA/B,CAAjB;;UACMC,WAAW/R,EAAE+E,MAAF,EAAUO,QAAV,CAAmBnB,UAAUkB,IAA7B,CAAjB;;UAEI,CAAC0M,QAAD,KAAchS,MAAMmL,KAAN,KAAgB6F,cAAhB,IAAkChR,MAAMmL,KAAN,KAAgB8F,aAAhE,KACCe,aAAahS,MAAMmL,KAAN,KAAgB6F,cAAhB,IAAkChR,MAAMmL,KAAN,KAAgB8F,aAA/D,CADL,EACoF;YAE9EjR,MAAMmL,KAAN,KAAgB6F,cAApB,EAAoC;cAC5BtK,SAASzG,EAAE+E,MAAF,EAAU5C,IAAV,CAAe8B,SAAS2C,WAAxB,EAAqC,CAArC,CAAf;YACEH,MAAF,EAAUlE,OAAV,CAAkB,OAAlB;;;UAGA,IAAF,EAAQA,OAAR,CAAgB,OAAhB;;;;UAII4R,QAAQnU,EAAE+E,MAAF,EAAU5C,IAAV,CAAe8B,SAASmQ,aAAxB,EAAuCC,GAAvC,EAAd;;UAEI,CAACF,MAAM/R,MAAX,EAAmB;;;;UAIf4H,QAAQmK,MAAM9I,OAAN,CAActL,MAAME,MAApB,CAAZ;;UAEIF,MAAMmL,KAAN,KAAgBgG,gBAAhB,IAAoClH,QAAQ,CAAhD,EAAmD;;;;;UAI/CjK,MAAMmL,KAAN,KAAgBiG,kBAAhB,IAAsCnH,QAAQmK,MAAM/R,MAAN,GAAe,CAAjE,EAAoE;;;;;UAIhE4H,QAAQ,CAAZ,EAAe;gBACL,CAAR;;;YAGIA,KAAN,EAAa1C,KAAb;KA1YkB;;;;0BAgGC;eACZ3D,OAAP;;;;0BAGmB;eACZuE,OAAP;;;;0BAGuB;eAChBC,WAAP;;;;;;;;;;;;IA6SFvH,QAAF,EACGuF,EADH,CACMjC,MAAMoQ,gBADZ,EAC8BrQ,SAAS2C,WADvC,EACqDiK,SAASqD,sBAD9D,EAEG/N,EAFH,CAEMjC,MAAMoQ,gBAFZ,EAE8BrQ,SAAS6O,IAFvC,EAE6CjC,SAASqD,sBAFtD,EAGG/N,EAHH,CAGSjC,MAAMkC,cAHf,SAGiClC,MAAMqQ,cAHvC,EAGyD1D,SAASmB,WAHlE,EAIG7L,EAJH,CAIMjC,MAAMkC,cAJZ,EAI4BnC,SAAS2C,WAJrC,EAIkD,UAAU7G,KAAV,EAAiB;UACzDmG,cAAN;UACM0M,eAAN;;aACShN,gBAAT,CAA0BlG,IAA1B,CAA+BM,EAAE,IAAF,CAA/B,EAAwC,QAAxC;GAPJ,EASGmG,EATH,CASMjC,MAAMkC,cATZ,EAS4BnC,SAASuQ,UATrC,EASiD,UAACC,CAAD,EAAO;MAClD7B,eAAF;GAVJ;;;;;;;IAoBErR,EAAF,CAAKmC,IAAL,IAAyBmN,SAASjL,gBAAlC;IACErE,EAAF,CAAKmC,IAAL,EAAW4C,WAAX,GAAyBuK,QAAzB;;IACEtP,EAAF,CAAKmC,IAAL,EAAW6C,UAAX,GAAyB,YAAY;MACjChF,EAAF,CAAKmC,IAAL,IAAaK,kBAAb;WACO8M,SAASjL,gBAAhB;GAFF;;SAKOiL,QAAP;CAjbe,CAmbd7Q,CAnbc,EAmbX8Q,MAnbW,CAAjB;;ACRA;;;;;;;AAOA,IAAM4D,QAAS,YAAM;;;;;;MASbhR,OAA+B,OAArC;MACMC,UAA+B,cAArC;MACMC,WAA+B,UAArC;MACMC,kBAAmCD,QAAzC;MACME,eAA+B,WAArC;MACMC,qBAA+B/D,EAAEuB,EAAF,CAAKmC,IAAL,CAArC;MACMM,sBAA+B,GAArC;MACM2Q,+BAA+B,GAArC;MACM5D,iBAA+B,EAArC,CAjBmB;;MAmBb7I,UAAU;cACH,IADG;cAEH,IAFG;WAGH,IAHG;UAIH;GAJb;MAOMC,cAAc;cACP,kBADO;cAEP,SAFO;WAGP,SAHO;UAIP;GAJb;MAOMjE,QAAQ;mBACeL,SADf;uBAEiBA,SAFjB;mBAGeA,SAHf;qBAIgBA,SAJhB;yBAKkBA,SALlB;uBAMiBA,SANjB;qCAOwBA,SAPxB;yCAQ0BA,SAR1B;yCAS0BA,SAT1B;6CAU4BA,SAV5B;8BAWgBA,SAA5B,GAAwCC;GAX1C;MAcMK,YAAY;wBACK,yBADL;cAEK,gBAFL;UAGK,YAHL;UAIK,MAJL;UAKK;GALvB;MAQMF,WAAW;YACM,eADN;iBAEM,uBAFN;kBAGM,wBAHN;mBAIM,mDAJN;oBAKM,aALN;oBAMM;;;;;;;GANvB;;MAgBMyQ,KAvEa;;;mBAyEL3S,OAAZ,EAAqBY,MAArB,EAA6B;WACtBgG,OAAL,GAA4B,KAAKC,UAAL,CAAgBjG,MAAhB,CAA5B;WACKyB,QAAL,GAA4BrC,OAA5B;WACK6S,OAAL,GAA4B5U,EAAE+B,OAAF,EAAWI,IAAX,CAAgB8B,SAAS4Q,MAAzB,EAAiC,CAAjC,CAA5B;WACKC,SAAL,GAA4B,IAA5B;WACKC,QAAL,GAA4B,KAA5B;WACKC,kBAAL,GAA4B,KAA5B;WACKC,oBAAL,GAA4B,KAA5B;WACKC,oBAAL,GAA4B,CAA5B;WACKC,eAAL,GAA4B,CAA5B;KAlFe;;;;;;WAmGjB1O,MAnGiB,mBAmGVsF,aAnGU,EAmGK;aACb,KAAKgJ,QAAL,GAAgB,KAAKtG,IAAL,EAAhB,GAA8B,KAAKC,IAAL,CAAU3C,aAAV,CAArC;KApGe;;WAuGjB2C,IAvGiB,iBAuGZ3C,aAvGY,EAuGG;;;UACd,KAAK+B,gBAAL,IAAyB,KAAKiH,QAAlC,EAA4C;;;;UAIxC5V,KAAKsC,qBAAL,MAAgCzB,EAAE,KAAKoE,QAAP,EAAiBkB,QAAjB,CAA0BnB,UAAUoB,IAApC,CAApC,EAA+E;aACxEuI,gBAAL,GAAwB,IAAxB;;;UAGImE,YAAYjS,EAAEkE,KAAF,CAAQA,MAAMmB,IAAd,EAAoB;;OAApB,CAAlB;QAIE,KAAKjB,QAAP,EAAiB7B,OAAjB,CAAyB0P,SAAzB;;UAEI,KAAK8C,QAAL,IAAiB9C,UAAUvN,kBAAV,EAArB,EAAqD;;;;WAIhDqQ,QAAL,GAAgB,IAAhB;;WAEKK,eAAL;;WACKC,aAAL;;WAEKC,aAAL;;QAEE1U,SAAS2U,IAAX,EAAiB/I,QAAjB,CAA0BrI,UAAUqR,IAApC;;WAEKC,eAAL;;WACKC,eAAL;;QAEE,KAAKtR,QAAP,EAAiB+B,EAAjB,CACEjC,MAAMyR,aADR,EAEE1R,SAAS2R,YAFX,EAGE,UAAC7V,KAAD;eAAW,MAAK0O,IAAL,CAAU1O,KAAV,CAAX;OAHF;QAME,KAAK6U,OAAP,EAAgBzO,EAAhB,CAAmBjC,MAAM2R,iBAAzB,EAA4C,YAAM;UAC9C,MAAKzR,QAAP,EAAiBjD,GAAjB,CAAqB+C,MAAM4R,eAA3B,EAA4C,UAAC/V,KAAD,EAAW;cACjDC,EAAED,MAAME,MAAR,EAAgBC,EAAhB,CAAmB,MAAKkE,QAAxB,CAAJ,EAAuC;kBAChC6Q,oBAAL,GAA4B,IAA5B;;SAFJ;OADF;;WAQKc,aAAL,CAAmB;eAAM,MAAKC,YAAL,CAAkBjK,aAAlB,CAAN;OAAnB;KApJe;;WAuJjB0C,IAvJiB,iBAuJZ1O,KAvJY,EAuJL;;;UACNA,KAAJ,EAAW;cACHmG,cAAN;;;UAGE,KAAK4H,gBAAL,IAAyB,CAAC,KAAKiH,QAAnC,EAA6C;;;;UAIvCf,YAAYhU,EAAEkE,KAAF,CAAQA,MAAMyL,IAAd,CAAlB;QAEE,KAAKvL,QAAP,EAAiB7B,OAAjB,CAAyByR,SAAzB;;UAEI,CAAC,KAAKe,QAAN,IAAkBf,UAAUtP,kBAAV,EAAtB,EAAsD;;;;WAIjDqQ,QAAL,GAAgB,KAAhB;UAEM3V,aAAaD,KAAKsC,qBAAL,MAAgCzB,EAAE,KAAKoE,QAAP,EAAiBkB,QAAjB,CAA0BnB,UAAUoB,IAApC,CAAnD;;UAEInG,UAAJ,EAAgB;aACT0O,gBAAL,GAAwB,IAAxB;;;WAGG2H,eAAL;;WACKC,eAAL;;QAEE9U,QAAF,EAAY0J,GAAZ,CAAgBpG,MAAM+R,OAAtB;QAEE,KAAK7R,QAAP,EAAiBgB,WAAjB,CAA6BjB,UAAUkB,IAAvC;QAEE,KAAKjB,QAAP,EAAiBkG,GAAjB,CAAqBpG,MAAMyR,aAA3B;QACE,KAAKf,OAAP,EAAgBtK,GAAhB,CAAoBpG,MAAM2R,iBAA1B;;UAEIzW,UAAJ,EAAgB;UAEZ,KAAKgF,QAAP,EACGjD,GADH,CACOhC,KAAKiC,cADZ,EAC4B,UAACrB,KAAD;iBAAW,OAAKmW,UAAL,CAAgBnW,KAAhB,CAAX;SAD5B,EAEGyB,oBAFH,CAEwBwC,mBAFxB;OAFF,MAKO;aACAkS,UAAL;;KAhMa;;WAoMjBtR,OApMiB,sBAoMP;QACNC,UAAF,CAAa,KAAKT,QAAlB,EAA4BR,QAA5B;QAEEnD,MAAF,EAAUG,QAAV,EAAoB,KAAKwD,QAAzB,EAAmC,KAAK0Q,SAAxC,EAAmDxK,GAAnD,CAAuDzG,SAAvD;WAEK8E,OAAL,GAA4B,IAA5B;WACKvE,QAAL,GAA4B,IAA5B;WACKwQ,OAAL,GAA4B,IAA5B;WACKE,SAAL,GAA4B,IAA5B;WACKC,QAAL,GAA4B,IAA5B;WACKC,kBAAL,GAA4B,IAA5B;WACKC,oBAAL,GAA4B,IAA5B;WACKE,eAAL,GAA4B,IAA5B;KAhNe;;WAmNjBgB,YAnNiB,2BAmNF;WACRb,aAAL;KApNe;;;WAyNjB1M,UAzNiB,uBAyNNjG,MAzNM,EAyNE;eACR3C,EAAEuK,MAAF,CAAS,EAAT,EAAarC,OAAb,EAAsBvF,MAAtB,CAAT;WACK6H,eAAL,CAAqB9G,IAArB,EAA2Bf,MAA3B,EAAmCwF,WAAnC;aACOxF,MAAP;KA5Ne;;WA+NjBqT,YA/NiB,yBA+NJjK,aA/NI,EA+NW;;;UACpB3M,aAAaD,KAAKsC,qBAAL,MACjBzB,EAAE,KAAKoE,QAAP,EAAiBkB,QAAjB,CAA0BnB,UAAUoB,IAApC,CADF;;UAGI,CAAC,KAAKnB,QAAL,CAAc6P,UAAf,IACD,KAAK7P,QAAL,CAAc6P,UAAd,CAAyBxR,QAAzB,KAAsC2T,KAAKC,YAD9C,EAC4D;;iBAEjDd,IAAT,CAAce,WAAd,CAA0B,KAAKlS,QAA/B;;;WAGGA,QAAL,CAAcrD,KAAd,CAAoBwV,OAApB,GAA8B,OAA9B;;WACKnS,QAAL,CAAcoS,eAAd,CAA8B,aAA9B;;WACKpS,QAAL,CAAcqS,SAAd,GAA0B,CAA1B;;UAEIrX,UAAJ,EAAgB;aACT8N,MAAL,CAAY,KAAK9I,QAAjB;;;QAGA,KAAKA,QAAP,EAAiBoI,QAAjB,CAA0BrI,UAAUkB,IAApC;;UAEI,KAAKsD,OAAL,CAAarB,KAAjB,EAAwB;aACjBoP,aAAL;;;UAGIC,aAAa3W,EAAEkE,KAAF,CAAQA,MAAMqL,KAAd,EAAqB;;OAArB,CAAnB;;UAIMqH,qBAAqB,SAArBA,kBAAqB,GAAM;YAC3B,OAAKjO,OAAL,CAAarB,KAAjB,EAAwB;iBACjBlD,QAAL,CAAckD,KAAd;;;eAEGwG,gBAAL,GAAwB,KAAxB;UACE,OAAK1J,QAAP,EAAiB7B,OAAjB,CAAyBoU,UAAzB;OALF;;UAQIvX,UAAJ,EAAgB;UACZ,KAAKwV,OAAP,EACGzT,GADH,CACOhC,KAAKiC,cADZ,EAC4BwV,kBAD5B,EAEGpV,oBAFH,CAEwBwC,mBAFxB;OADF,MAIO;;;KAvQQ;;WA4QjB0S,aA5QiB,4BA4QD;;;QACZ9V,QAAF,EACG0J,GADH,CACOpG,MAAM+R,OADb;OAEG9P,EAFH,CAEMjC,MAAM+R,OAFZ,EAEqB,UAAClW,KAAD,EAAW;YACxBa,aAAab,MAAME,MAAnB,IACA,OAAKmE,QAAL,KAAkBrE,MAAME,MADxB,IAEA,CAACD,EAAE,OAAKoE,QAAP,EAAiByS,GAAjB,CAAqB9W,MAAME,MAA3B,EAAmCmC,MAFxC,EAEgD;iBACzCgC,QAAL,CAAckD,KAAd;;OANN;KA7Qe;;WAwRjBmO,eAxRiB,8BAwRC;;;UACZ,KAAKV,QAAL,IAAiB,KAAKpM,OAAL,CAAa8B,QAAlC,EAA4C;UACxC,KAAKrG,QAAP,EAAiB+B,EAAjB,CAAoBjC,MAAM4S,eAA1B,EAA2C,UAAC/W,KAAD,EAAW;cAChDA,MAAMmL,KAAN,KAAgB6F,cAApB,EAAoC;kBAC5B7K,cAAN;;mBACKuI,IAAL;;SAHJ;OADF,MAQO,IAAI,CAAC,KAAKsG,QAAV,EAAoB;UACvB,KAAK3Q,QAAP,EAAiBkG,GAAjB,CAAqBpG,MAAM4S,eAA3B;;KAlSa;;WAsSjBpB,eAtSiB,8BAsSC;;;UACZ,KAAKX,QAAT,EAAmB;UACftU,MAAF,EAAU0F,EAAV,CAAajC,MAAM6S,MAAnB,EAA2B,UAAChX,KAAD;iBAAW,OAAKoW,YAAL,CAAkBpW,KAAlB,CAAX;SAA3B;OADF,MAEO;UACHU,MAAF,EAAU6J,GAAV,CAAcpG,MAAM6S,MAApB;;KA1Sa;;WA8SjBb,UA9SiB,yBA8SJ;;;WACN9R,QAAL,CAAcrD,KAAd,CAAoBwV,OAApB,GAA8B,MAA9B;;WACKnS,QAAL,CAAcmD,YAAd,CAA2B,aAA3B,EAA0C,IAA1C;;WACKuG,gBAAL,GAAwB,KAAxB;;WACKiI,aAAL,CAAmB,YAAM;UACrBnV,SAAS2U,IAAX,EAAiBnQ,WAAjB,CAA6BjB,UAAUqR,IAAvC;;eACKwB,iBAAL;;eACKC,eAAL;;UACE,OAAK7S,QAAP,EAAiB7B,OAAjB,CAAyB2B,MAAM4L,MAA/B;OAJF;KAlTe;;WA0TjBoH,eA1TiB,8BA0TC;UACZ,KAAKpC,SAAT,EAAoB;UAChB,KAAKA,SAAP,EAAkBnP,MAAlB;aACKmP,SAAL,GAAiB,IAAjB;;KA7Ta;;WAiUjBiB,aAjUiB,0BAiUHoB,QAjUG,EAiUO;;;UAChBC,UAAUpX,EAAE,KAAKoE,QAAP,EAAiBkB,QAAjB,CAA0BnB,UAAUoB,IAApC,IACdpB,UAAUoB,IADI,GACG,EADnB;;UAGI,KAAKwP,QAAL,IAAiB,KAAKpM,OAAL,CAAa0O,QAAlC,EAA4C;YACpCC,YAAYnY,KAAKsC,qBAAL,MAAgC2V,OAAlD;aAEKtC,SAAL,GAAiBlU,SAASC,aAAT,CAAuB,KAAvB,CAAjB;aACKiU,SAAL,CAAeyC,SAAf,GAA2BpT,UAAUqT,QAArC;;YAEIJ,OAAJ,EAAa;YACT,KAAKtC,SAAP,EAAkBtI,QAAlB,CAA2B4K,OAA3B;;;UAGA,KAAKtC,SAAP,EAAkB2C,QAAlB,CAA2B7W,SAAS2U,IAApC;UAEE,KAAKnR,QAAP,EAAiB+B,EAAjB,CAAoBjC,MAAMyR,aAA1B,EAAyC,UAAC5V,KAAD,EAAW;cAC9C,OAAKkV,oBAAT,EAA+B;mBACxBA,oBAAL,GAA4B,KAA5B;;;;cAGElV,MAAME,MAAN,KAAiBF,MAAMyQ,aAA3B,EAA0C;;;;cAGtC,OAAK7H,OAAL,CAAa0O,QAAb,KAA0B,QAA9B,EAAwC;mBACjCjT,QAAL,CAAckD,KAAd;WADF,MAEO;mBACAmH,IAAL;;SAXJ;;YAeI6I,SAAJ,EAAe;eACRpK,MAAL,CAAY,KAAK4H,SAAjB;;;UAGA,KAAKA,SAAP,EAAkBtI,QAAlB,CAA2BrI,UAAUkB,IAArC;;YAEI,CAAC8R,QAAL,EAAe;;;;YAIX,CAACG,SAAL,EAAgB;;;;;UAKd,KAAKxC,SAAP,EACG3T,GADH,CACOhC,KAAKiC,cADZ,EAC4B+V,QAD5B,EAEG3V,oBAFH,CAEwBmT,4BAFxB;OA1CF,MA8CO,IAAI,CAAC,KAAKI,QAAN,IAAkB,KAAKD,SAA3B,EAAsC;UACzC,KAAKA,SAAP,EAAkB1P,WAAlB,CAA8BjB,UAAUkB,IAAxC;;YAEMqS,iBAAiB,SAAjBA,cAAiB,GAAM;iBACtBR,eAAL;;cACIC,QAAJ,EAAc;;;SAFhB;;YAOIhY,KAAKsC,qBAAL,MACDzB,EAAE,KAAKoE,QAAP,EAAiBkB,QAAjB,CAA0BnB,UAAUoB,IAApC,CADH,EAC8C;YAC1C,KAAKuP,SAAP,EACG3T,GADH,CACOhC,KAAKiC,cADZ,EAC4BsW,cAD5B,EAEGlW,oBAFH,CAEwBmT,4BAFxB;SAFF,MAKO;;;OAfF,MAmBA,IAAIwC,QAAJ,EAAc;;;KAtYN;;;;;;WAiZjB7B,aAjZiB,4BAiZD;UACRqC,qBACJ,KAAKvT,QAAL,CAAcwT,YAAd,GAA6BhX,SAASkK,eAAT,CAAyB+M,YADxD;;UAGI,CAAC,KAAK7C,kBAAN,IAA4B2C,kBAAhC,EAAoD;aAC7CvT,QAAL,CAAcrD,KAAd,CAAoB+W,WAApB,GAAqC,KAAK3C,eAA1C;;;UAGE,KAAKH,kBAAL,IAA2B,CAAC2C,kBAAhC,EAAoD;aAC7CvT,QAAL,CAAcrD,KAAd,CAAoBgX,YAApB,GAAsC,KAAK5C,eAA3C;;KA1Za;;WA8ZjB6B,iBA9ZiB,gCA8ZG;WACb5S,QAAL,CAAcrD,KAAd,CAAoB+W,WAApB,GAAkC,EAAlC;WACK1T,QAAL,CAAcrD,KAAd,CAAoBgX,YAApB,GAAmC,EAAnC;KAhae;;WAmajB3C,eAnaiB,8BAmaC;UACV4C,OAAOpX,SAAS2U,IAAT,CAAc3F,qBAAd,EAAb;WACKoF,kBAAL,GAA0BgD,KAAKC,IAAL,GAAYD,KAAKE,KAAjB,GAAyBzX,OAAO0X,UAA1D;WACKhD,eAAL,GAAuB,KAAKiD,kBAAL,EAAvB;KAtae;;WAyajB/C,aAzaiB,4BAyaD;;;UACV,KAAKL,kBAAT,EAA6B;;;;UAKzB/Q,SAASoU,aAAX,EAA0BxS,IAA1B,CAA+B,UAACmE,KAAD,EAAQjI,OAAR,EAAoB;cAC3CuW,gBAAgBtY,EAAE+B,OAAF,EAAW,CAAX,EAAchB,KAAd,CAAoBgX,YAA1C;cACMQ,oBAAoBvY,EAAE+B,OAAF,EAAWsH,GAAX,CAAe,eAAf,CAA1B;YACEtH,OAAF,EAAWgE,IAAX,CAAgB,eAAhB,EAAiCuS,aAAjC,EAAgDjP,GAAhD,CAAoD,eAApD,EAAwEmP,WAAWD,iBAAX,IAAgC,OAAKpD,eAA7G;SAHF,EAL2B;;UAYzBlR,SAASwU,cAAX,EAA2B5S,IAA3B,CAAgC,UAACmE,KAAD,EAAQjI,OAAR,EAAoB;cAC5C2W,eAAe1Y,EAAE+B,OAAF,EAAW,CAAX,EAAchB,KAAd,CAAoB4X,WAAzC;cACMC,mBAAmB5Y,EAAE+B,OAAF,EAAWsH,GAAX,CAAe,cAAf,CAAzB;YACEtH,OAAF,EAAWgE,IAAX,CAAgB,cAAhB,EAAgC2S,YAAhC,EAA8CrP,GAA9C,CAAkD,cAAlD,EAAqEmP,WAAWI,gBAAX,IAA+B,OAAKzD,eAAzG;SAHF,EAZ2B;;UAmBzBlR,SAAS4U,cAAX,EAA2BhT,IAA3B,CAAgC,UAACmE,KAAD,EAAQjI,OAAR,EAAoB;cAC5C2W,eAAe1Y,EAAE+B,OAAF,EAAW,CAAX,EAAchB,KAAd,CAAoB4X,WAAzC;cACMC,mBAAmB5Y,EAAE+B,OAAF,EAAWsH,GAAX,CAAe,cAAf,CAAzB;YACEtH,OAAF,EAAWgE,IAAX,CAAgB,cAAhB,EAAgC2S,YAAhC,EAA8CrP,GAA9C,CAAkD,cAAlD,EAAqEmP,WAAWI,gBAAX,IAA+B,OAAKzD,eAAzG;SAHF,EAnB2B;;YA0BrBmD,gBAAgB1X,SAAS2U,IAAT,CAAcxU,KAAd,CAAoBgX,YAA1C;YACMQ,oBAAoBvY,EAAE,MAAF,EAAUqJ,GAAV,CAAc,eAAd,CAA1B;UACE,MAAF,EAAUtD,IAAV,CAAe,eAAf,EAAgCuS,aAAhC,EAA+CjP,GAA/C,CAAmD,eAAnD,EAAuEmP,WAAWD,iBAAX,IAAgC,KAAKpD,eAA5G;;KAtca;;WA0cjB8B,eA1ciB,8BA0cC;;QAEdhT,SAASoU,aAAX,EAA0BxS,IAA1B,CAA+B,UAACmE,KAAD,EAAQjI,OAAR,EAAoB;YAC3C+W,UAAU9Y,EAAE+B,OAAF,EAAWgE,IAAX,CAAgB,eAAhB,CAAhB;;YACI,OAAO+S,OAAP,KAAmB,WAAvB,EAAoC;YAChC/W,OAAF,EAAWsH,GAAX,CAAe,eAAf,EAAgCyP,OAAhC,EAAyCjU,UAAzC,CAAoD,eAApD;;OAHJ,EAFgB;;QAUXZ,SAASwU,cAAd,UAAiCxU,SAAS4U,cAA1C,EAA4DhT,IAA5D,CAAiE,UAACmE,KAAD,EAAQjI,OAAR,EAAoB;YAC7EgX,SAAS/Y,EAAE+B,OAAF,EAAWgE,IAAX,CAAgB,cAAhB,CAAf;;YACI,OAAOgT,MAAP,KAAkB,WAAtB,EAAmC;YAC/BhX,OAAF,EAAWsH,GAAX,CAAe,cAAf,EAA+B0P,MAA/B,EAAuClU,UAAvC,CAAkD,cAAlD;;OAHJ,EAVgB;;UAkBViU,UAAU9Y,EAAE,MAAF,EAAU+F,IAAV,CAAe,eAAf,CAAhB;;UACI,OAAO+S,OAAP,KAAmB,WAAvB,EAAoC;UAChC,MAAF,EAAUzP,GAAV,CAAc,eAAd,EAA+ByP,OAA/B,EAAwCjU,UAAxC,CAAmD,eAAnD;;KA9da;;WAkejBuT,kBAleiB,iCAkeI;;UACbY,YAAYpY,SAASC,aAAT,CAAuB,KAAvB,CAAlB;gBACU0W,SAAV,GAAsBpT,UAAU8U,kBAAhC;eACS1D,IAAT,CAAce,WAAd,CAA0B0C,SAA1B;UACME,iBAAiBF,UAAUpJ,qBAAV,GAAkCuJ,KAAlC,GAA0CH,UAAUI,WAA3E;eACS7D,IAAT,CAAc8D,WAAd,CAA0BL,SAA1B;aACOE,cAAP;KAxee;;;UA8eVtT,gBA9eU,6BA8eOjD,MA9eP,EA8eeoJ,aA9ef,EA8e8B;aACtC,KAAKlG,IAAL,CAAU,YAAY;YACvBE,OAAY/F,EAAE,IAAF,EAAQ+F,IAAR,CAAanC,QAAb,CAAhB;;YACM+E,UAAU3I,EAAEuK,MAAF,CACd,EADc,EAEdmK,MAAMxM,OAFQ,EAGdlI,EAAE,IAAF,EAAQ+F,IAAR,EAHc,EAId,OAAOpD,MAAP,KAAkB,QAAlB,IAA8BA,MAJhB,CAAhB;;YAOI,CAACoD,IAAL,EAAW;iBACF,IAAI2O,KAAJ,CAAU,IAAV,EAAgB/L,OAAhB,CAAP;YACE,IAAF,EAAQ5C,IAAR,CAAanC,QAAb,EAAuBmC,IAAvB;;;YAGE,OAAOpD,MAAP,KAAkB,QAAtB,EAAgC;cAC1B,OAAOoD,KAAKpD,MAAL,CAAP,KAAwB,WAA5B,EAAyC;kBACjC,IAAIY,KAAJ,wBAA8BZ,MAA9B,QAAN;;;eAEGA,MAAL,EAAaoJ,aAAb;SAJF,MAKO,IAAIpD,QAAQ+F,IAAZ,EAAkB;eAClBA,IAAL,CAAU3C,aAAV;;OApBG,CAAP;KA/ee;;;;0BAwFI;eACZpI,OAAP;;;;0BAGmB;eACZuE,OAAP;;;;;;;;;;;;IAobFtH,QAAF,EAAYuF,EAAZ,CAAejC,MAAMkC,cAArB,EAAqCnC,SAAS2C,WAA9C,EAA2D,UAAU7G,KAAV,EAAiB;;;QACtEE,MAAJ;QACM+B,WAAW7C,KAAK2F,sBAAL,CAA4B,IAA5B,CAAjB;;QAEI9C,QAAJ,EAAc;eACHhC,EAAEgC,QAAF,EAAY,CAAZ,CAAT;;;QAGIW,SAAS3C,EAAEC,MAAF,EAAU8F,IAAV,CAAenC,QAAf,IACb,QADa,GACF5D,EAAEuK,MAAF,CAAS,EAAT,EAAavK,EAAEC,MAAF,EAAU8F,IAAV,EAAb,EAA+B/F,EAAE,IAAF,EAAQ+F,IAAR,EAA/B,CADb;;QAGI,KAAKkF,OAAL,KAAiB,GAAjB,IAAwB,KAAKA,OAAL,KAAiB,MAA7C,EAAqD;YAC7C/E,cAAN;;;QAGIwK,UAAU1Q,EAAEC,MAAF,EAAUkB,GAAV,CAAc+C,MAAMmB,IAApB,EAA0B,UAAC4M,SAAD,EAAe;UACnDA,UAAUvN,kBAAV,EAAJ,EAAoC;;;;;cAK5BvD,GAAR,CAAY+C,MAAM4L,MAAlB,EAA0B,YAAM;YAC1B9P,WAAQE,EAAR,CAAW,UAAX,CAAJ,EAA4B;kBACrBoH,KAAL;;OAFJ;KANc,CAAhB;;UAaM1B,gBAAN,CAAuBlG,IAAvB,CAA4BM,EAAEC,MAAF,CAA5B,EAAuC0C,MAAvC,EAA+C,IAA/C;GA5BF;;;;;;;IAsCEpB,EAAF,CAAKmC,IAAL,IAAyBgR,MAAM9O,gBAA/B;IACErE,EAAF,CAAKmC,IAAL,EAAW4C,WAAX,GAAyBoO,KAAzB;;IACEnT,EAAF,CAAKmC,IAAL,EAAW6C,UAAX,GAAyB,YAAY;MACjChF,EAAF,CAAKmC,IAAL,IAAaK,kBAAb;WACO2Q,MAAM9O,gBAAb;GAFF;;SAKO8O,KAAP;CA9jBY,CAgkBX1U,CAhkBW,CAAd;;ACNA;;;;;;;AAOA,IAAMsZ,UAAW,YAAM;;;;;MAMjB,OAAOxI,MAAP,KAAkB,WAAtB,EAAmC;UAC3B,IAAIvN,KAAJ,CAAU,8DAAV,CAAN;;;;;;;;;MAUIG,OAAsB,SAA5B;MACMC,UAAsB,cAA5B;MACMC,WAAsB,YAA5B;MACMC,kBAA0BD,QAAhC;MACMG,qBAAsB/D,EAAEuB,EAAF,CAAKmC,IAAL,CAA5B;MACMM,sBAAsB,GAA5B;MACMuV,eAAsB,YAA5B;MACMC,qBAAqB,IAAInW,MAAJ,aAAqBkW,YAArB,WAAyC,GAAzC,CAA3B;MAEMpR,cAAc;eACI,SADJ;cAEI,QAFJ;WAGI,2BAHJ;aAII,QAJJ;WAKI,iBALJ;UAMI,SANJ;cAOI,kBAPJ;eAQI,mBARJ;YASI,iBATJ;eAUI,0BAVJ;uBAWI;GAXxB;MAcMmJ,gBAAgB;UACX,MADW;SAEX,KAFW;WAGX,OAHW;YAIX,QAJW;UAKX;GALX;MAQMpJ,UAAU;eACQ,IADR;cAEQ,yCACA,2BADA,GAEA,yCAJR;aAKQ,aALR;WAMQ,EANR;WAOQ,CAPR;UAQQ,KARR;cASQ,KATR;eAUQ,KAVR;YAWQ,CAXR;eAYQ,KAZR;uBAaQ;GAbxB;MAgBMuR,aAAa;UACV,MADU;SAEV;GAFT;MAKMvV,QAAQ;mBACQL,SADR;uBAEUA,SAFV;mBAGQA,SAHR;qBAISA,SAJT;2BAKYA,SALZ;qBAMSA,SANT;yBAOWA,SAPX;2BAQYA,SARZ;+BAScA,SATd;+BAUcA;GAV5B;MAaMM,YAAY;UACT,MADS;UAET;GAFT;MAKMF,WAAW;aACC,UADD;mBAEC,gBAFD;WAGC;GAHlB;MAMMyV,UAAU;WACL,OADK;WAEL,OAFK;WAGL,OAHK;YAIL;;;;;;;GAJX;;MAcMJ,OA3Ge;;;qBA6GPvX,OAAZ,EAAqBY,MAArB,EAA6B;;WAGtBgX,UAAL,GAAsB,IAAtB;WACKC,QAAL,GAAsB,CAAtB;WACKC,WAAL,GAAsB,EAAtB;WACKC,cAAL,GAAsB,EAAtB;WACKvI,OAAL,GAAsB,IAAtB,CAP2B;;WAUtBxP,OAAL,GAAeA,OAAf;WACKY,MAAL,GAAe,KAAKiG,UAAL,CAAgBjG,MAAhB,CAAf;WACKoX,GAAL,GAAe,IAAf;;WAEKC,aAAL;KA3HiB;;;;;;WAiKnBC,MAjKmB,qBAiKV;WACFN,UAAL,GAAkB,IAAlB;KAlKiB;;WAqKnBO,OArKmB,sBAqKT;WACHP,UAAL,GAAkB,KAAlB;KAtKiB;;WAyKnBQ,aAzKmB,4BAyKH;WACTR,UAAL,GAAkB,CAAC,KAAKA,UAAxB;KA1KiB;;WA6KnBlT,MA7KmB,mBA6KZ1G,KA7KY,EA6KL;UACR,CAAC,KAAK4Z,UAAV,EAAsB;;;;UAIlB5Z,KAAJ,EAAW;YACHqa,UAAU,KAAKvH,WAAL,CAAiBjP,QAAjC;YACIkQ,UAAU9T,EAAED,MAAMyQ,aAAR,EAAuBzK,IAAvB,CAA4BqU,OAA5B,CAAd;;YAEI,CAACtG,OAAL,EAAc;oBACF,IAAI,KAAKjB,WAAT,CACR9S,MAAMyQ,aADE,EAER,KAAK6J,kBAAL,EAFQ,CAAV;YAIEta,MAAMyQ,aAAR,EAAuBzK,IAAvB,CAA4BqU,OAA5B,EAAqCtG,OAArC;;;gBAGMgG,cAAR,CAAuBQ,KAAvB,GAA+B,CAACxG,QAAQgG,cAAR,CAAuBQ,KAAvD;;YAEIxG,QAAQyG,oBAAR,EAAJ,EAAoC;kBAC1BC,MAAR,CAAe,IAAf,EAAqB1G,OAArB;SADF,MAEO;kBACG2G,MAAR,CAAe,IAAf,EAAqB3G,OAArB;;OAjBJ,MAoBO;YAED9T,EAAE,KAAK0a,aAAL,EAAF,EAAwBpV,QAAxB,CAAiCnB,UAAUkB,IAA3C,CAAJ,EAAsD;eAC/CoV,MAAL,CAAY,IAAZ,EAAkB,IAAlB;;;;;aAIGD,MAAL,CAAY,IAAZ,EAAkB,IAAlB;;KA7Me;;WAiNnB5V,OAjNmB,sBAiNT;mBACK,KAAKgV,QAAlB;QAEE/U,UAAF,CAAa,KAAK9C,OAAlB,EAA2B,KAAK8Q,WAAL,CAAiBjP,QAA5C;QAEE,KAAK7B,OAAP,EAAgBuI,GAAhB,CAAoB,KAAKuI,WAAL,CAAiBhP,SAArC;QACE,KAAK9B,OAAP,EAAgBiD,OAAhB,CAAwB,QAAxB,EAAkCsF,GAAlC,CAAsC,eAAtC;;UAEI,KAAKyP,GAAT,EAAc;UACV,KAAKA,GAAP,EAAYpU,MAAZ;;;WAGGgU,UAAL,GAAsB,IAAtB;WACKC,QAAL,GAAsB,IAAtB;WACKC,WAAL,GAAsB,IAAtB;WACKC,cAAL,GAAsB,IAAtB;;UACI,KAAKvI,OAAL,KAAiB,IAArB,EAA2B;aACpBA,OAAL,CAAaiB,OAAb;;;WAGGjB,OAAL,GAAe,IAAf;WACKxP,OAAL,GAAe,IAAf;WACKY,MAAL,GAAe,IAAf;WACKoX,GAAL,GAAe,IAAf;KAxOiB;;WA2OnBrL,IA3OmB,mBA2OZ;;;UACD1O,EAAE,KAAK+B,OAAP,EAAgBsH,GAAhB,CAAoB,SAApB,MAAmC,MAAvC,EAA+C;cACvC,IAAI9F,KAAJ,CAAU,qCAAV,CAAN;;;UAGI0O,YAAYjS,EAAEkE,KAAF,CAAQ,KAAK2O,WAAL,CAAiB3O,KAAjB,CAAuBmB,IAA/B,CAAlB;;UACI,KAAKsV,aAAL,MAAwB,KAAKhB,UAAjC,EAA6C;UACzC,KAAK5X,OAAP,EAAgBQ,OAAhB,CAAwB0P,SAAxB;YAEM2I,aAAa5a,EAAEqH,QAAF,CACjB,KAAKtF,OAAL,CAAa8Y,aAAb,CAA2B/P,eADV,EAEjB,KAAK/I,OAFY,CAAnB;;YAKIkQ,UAAUvN,kBAAV,MAAkC,CAACkW,UAAvC,EAAmD;;;;YAI7Cb,MAAQ,KAAKW,aAAL,EAAd;YACMI,QAAQ3b,KAAK4b,MAAL,CAAY,KAAKlI,WAAL,CAAiBnP,IAA7B,CAAd;YAEI6D,YAAJ,CAAiB,IAAjB,EAAuBuT,KAAvB;aACK/Y,OAAL,CAAawF,YAAb,CAA0B,kBAA1B,EAA8CuT,KAA9C;aAEKE,UAAL;;YAEI,KAAKrY,MAAL,CAAYsY,SAAhB,EAA2B;YACvBlB,GAAF,EAAOvN,QAAP,CAAgBrI,UAAUoB,IAA1B;;;YAGI0N,YAAa,OAAO,KAAKtQ,MAAL,CAAYsQ,SAAnB,KAAiC,UAAjC,GACjB,KAAKtQ,MAAL,CAAYsQ,SAAZ,CAAsBvT,IAAtB,CAA2B,IAA3B,EAAiCqa,GAAjC,EAAsC,KAAKhY,OAA3C,CADiB,GAEjB,KAAKY,MAAL,CAAYsQ,SAFd;;YAIMiI,aAAa,KAAKC,cAAL,CAAoBlI,SAApB,CAAnB;;aACKmI,kBAAL,CAAwBF,UAAxB;YAEMG,YAAY,KAAK1Y,MAAL,CAAY0Y,SAAZ,KAA0B,KAA1B,GAAkCza,SAAS2U,IAA3C,GAAkDvV,EAAE,KAAK2C,MAAL,CAAY0Y,SAAd,CAApE;UAEEtB,GAAF,EAAOhU,IAAP,CAAY,KAAK8M,WAAL,CAAiBjP,QAA7B,EAAuC,IAAvC;;YAEI,CAAC5D,EAAEqH,QAAF,CAAW,KAAKtF,OAAL,CAAa8Y,aAAb,CAA2B/P,eAAtC,EAAuD,KAAKiP,GAA5D,CAAL,EAAuE;YACnEA,GAAF,EAAOtC,QAAP,CAAgB4D,SAAhB;;;UAGA,KAAKtZ,OAAP,EAAgBQ,OAAhB,CAAwB,KAAKsQ,WAAL,CAAiB3O,KAAjB,CAAuBoX,QAA/C;aAEK/J,OAAL,GAAe,IAAIT,MAAJ,CAAW,KAAK/O,OAAhB,EAAyBgY,GAAzB,EAA8B;qBAChCmB,UADgC;qBAEhC;oBACD;sBACE,KAAKvY,MAAL,CAAY4Q;aAFb;kBAIH;wBACM,KAAK5Q,MAAL,CAAY4Y;aALf;mBAOF;uBACItX,SAASuX;;WAVqB;oBAajC,kBAACzV,IAAD,EAAU;gBACdA,KAAK0V,iBAAL,KAA2B1V,KAAKkN,SAApC,EAA+C;oBACxCyI,4BAAL,CAAkC3V,IAAlC;;WAfuC;oBAkBhC,kBAACA,IAAD,EAAU;kBACd2V,4BAAL,CAAkC3V,IAAlC;;SAnBW,CAAf;UAuBEgU,GAAF,EAAOvN,QAAP,CAAgBrI,UAAUkB,IAA1B,EAhE2C;;;;;YAsEvC,kBAAkBzE,SAASkK,eAA/B,EAAgD;YAC5C,MAAF,EAAUyB,QAAV,GAAqBpG,EAArB,CAAwB,WAAxB,EAAqC,IAArC,EAA2CnG,EAAEuS,IAA7C;;;YAGIjD,WAAW,SAAXA,QAAW,GAAM;cACjB,MAAK3M,MAAL,CAAYsY,SAAhB,EAA2B;kBACpBU,cAAL;;;cAEIC,iBAAiB,MAAK/B,WAA5B;gBACKA,WAAL,GAAuB,IAAvB;YAEE,MAAK9X,OAAP,EAAgBQ,OAAhB,CAAwB,MAAKsQ,WAAL,CAAiB3O,KAAjB,CAAuBqL,KAA/C;;cAEIqM,mBAAmBnC,WAAWoC,GAAlC,EAAuC;kBAChCpB,MAAL,CAAY,IAAZ;;SAVJ;;YAcItb,KAAKsC,qBAAL,MAAgCzB,EAAE,KAAK+Z,GAAP,EAAYzU,QAAZ,CAAqBnB,UAAUoB,IAA/B,CAApC,EAA0E;YACtE,KAAKwU,GAAP,EACG5Y,GADH,CACOhC,KAAKiC,cADZ,EAC4BkO,QAD5B,EAEG9N,oBAFH,CAEwB8X,QAAQwC,oBAFhC;SADF,MAIO;;;;KA7UQ;;WAmVnBrN,IAnVmB,iBAmVd0I,QAnVc,EAmVJ;;;UACP4C,MAAY,KAAKW,aAAL,EAAlB;UACM1G,YAAYhU,EAAEkE,KAAF,CAAQ,KAAK2O,WAAL,CAAiB3O,KAAjB,CAAuByL,IAA/B,CAAlB;;UACML,WAAY,SAAZA,QAAY,GAAM;YAClB,OAAKuK,WAAL,KAAqBJ,WAAWpU,IAAhC,IAAwC0U,IAAI9F,UAAhD,EAA4D;cACtDA,UAAJ,CAAeoF,WAAf,CAA2BU,GAA3B;;;eAGGgC,cAAL;;eACKha,OAAL,CAAayU,eAAb,CAA6B,kBAA7B;;UACE,OAAKzU,OAAP,EAAgBQ,OAAhB,CAAwB,OAAKsQ,WAAL,CAAiB3O,KAAjB,CAAuB4L,MAA/C;;YACI,OAAKyB,OAAL,KAAiB,IAArB,EAA2B;iBACpBA,OAAL,CAAaiB,OAAb;;;YAGE2E,QAAJ,EAAc;;;OAZhB;;QAiBE,KAAKpV,OAAP,EAAgBQ,OAAhB,CAAwByR,SAAxB;;UAEIA,UAAUtP,kBAAV,EAAJ,EAAoC;;;;QAIlCqV,GAAF,EAAO3U,WAAP,CAAmBjB,UAAUkB,IAA7B,EA1Ba;;;UA8BT,kBAAkBzE,SAASkK,eAA/B,EAAgD;UAC5C,MAAF,EAAUyB,QAAV,GAAqBjC,GAArB,CAAyB,WAAzB,EAAsC,IAAtC,EAA4CtK,EAAEuS,IAA9C;;;WAGGuH,cAAL,CAAoBJ,QAAQ/G,KAA5B,IAAqC,KAArC;WACKmH,cAAL,CAAoBJ,QAAQ7R,KAA5B,IAAqC,KAArC;WACKiS,cAAL,CAAoBJ,QAAQsC,KAA5B,IAAqC,KAArC;;UAEI7c,KAAKsC,qBAAL,MACAzB,EAAE,KAAK+Z,GAAP,EAAYzU,QAAZ,CAAqBnB,UAAUoB,IAA/B,CADJ,EAC0C;UAEtCwU,GAAF,EACG5Y,GADH,CACOhC,KAAKiC,cADZ,EAC4BkO,QAD5B,EAEG9N,oBAFH,CAEwBwC,mBAFxB;OAHF,MAOO;;;;WAIF6V,WAAL,GAAmB,EAAnB;KApYiB;;WAwYnBpH,MAxYmB,qBAwYV;UACH,KAAKlB,OAAL,KAAiB,IAArB,EAA2B;aACpBA,OAAL,CAAamB,cAAb;;KA1Ye;;;WAgZnBiI,aAhZmB,4BAgZH;aACPnY,QAAQ,KAAKyZ,QAAL,EAAR,CAAP;KAjZiB;;WAoZnBb,kBApZmB,+BAoZAF,UApZA,EAoZY;QAC3B,KAAKR,aAAL,EAAF,EAAwBlO,QAAxB,CAAoC+M,YAApC,SAAoD2B,UAApD;KArZiB;;WAwZnBR,aAxZmB,4BAwZH;WACTX,GAAL,GAAW,KAAKA,GAAL,IAAY/Z,EAAE,KAAK2C,MAAL,CAAYuZ,QAAd,EAAwB,CAAxB,CAAvB;aACO,KAAKnC,GAAZ;KA1ZiB;;WA6ZnBiB,UA7ZmB,yBA6ZN;UACLmB,OAAOnc,EAAE,KAAK0a,aAAL,EAAF,CAAb;WACK0B,iBAAL,CAAuBD,KAAKha,IAAL,CAAU8B,SAASoY,aAAnB,CAAvB,EAA0D,KAAKJ,QAAL,EAA1D;WACK7W,WAAL,CAAoBjB,UAAUoB,IAA9B,SAAsCpB,UAAUkB,IAAhD;KAhaiB;;WAmanB+W,iBAnamB,8BAmaDtW,QAnaC,EAmaSwW,OAnaT,EAmakB;UAC7BC,OAAO,KAAK5Z,MAAL,CAAY4Z,IAAzB;;UACI,OAAOD,OAAP,KAAmB,QAAnB,KAAgCA,QAAQ7Z,QAAR,IAAoB6Z,QAAQnM,MAA5D,CAAJ,EAAyE;;YAEnEoM,IAAJ,EAAU;cACJ,CAACvc,EAAEsc,OAAF,EAAWvX,MAAX,GAAoB7E,EAApB,CAAuB4F,QAAvB,CAAL,EAAuC;qBAC5B0W,KAAT,GAAiBC,MAAjB,CAAwBH,OAAxB;;SAFJ,MAIO;mBACII,IAAT,CAAc1c,EAAEsc,OAAF,EAAWI,IAAX,EAAd;;OAPJ,MASO;iBACIH,OAAO,MAAP,GAAgB,MAAzB,EAAiCD,OAAjC;;KA/ae;;WAmbnBL,QAnbmB,uBAmbR;UACLU,QAAQ,KAAK5a,OAAL,CAAaE,YAAb,CAA0B,qBAA1B,CAAZ;;UAEI,CAAC0a,KAAL,EAAY;gBACF,OAAO,KAAKha,MAAL,CAAYga,KAAnB,KAA6B,UAA7B,GACN,KAAKha,MAAL,CAAYga,KAAZ,CAAkBjd,IAAlB,CAAuB,KAAKqC,OAA5B,CADM,GAEN,KAAKY,MAAL,CAAYga,KAFd;;;aAKKA,KAAP;KA5biB;;;WAkcnBxB,cAlcmB,2BAkcJlI,SAlcI,EAkcO;aACjB3B,cAAc2B,UAAUzP,WAAV,EAAd,CAAP;KAnciB;;WAscnBwW,aAtcmB,4BAscH;;;UACR4C,WAAW,KAAKja,MAAL,CAAYJ,OAAZ,CAAoBsa,KAApB,CAA0B,GAA1B,CAAjB;eAESC,OAAT,CAAiB,UAACva,OAAD,EAAa;YACxBA,YAAY,OAAhB,EAAyB;YACrB,OAAKR,OAAP,EAAgBoE,EAAhB,CACE,OAAK0M,WAAL,CAAiB3O,KAAjB,CAAuByO,KADzB,EAEE,OAAKhQ,MAAL,CAAYX,QAFd,EAGE,UAACjC,KAAD;mBAAW,OAAK0G,MAAL,CAAY1G,KAAZ,CAAX;WAHF;SADF,MAOO,IAAIwC,YAAYmX,QAAQqD,MAAxB,EAAgC;cAC/BC,UAAWza,YAAYmX,QAAQsC,KAApB,GACf,OAAKnJ,WAAL,CAAiB3O,KAAjB,CAAuB0G,UADR,GAEf,OAAKiI,WAAL,CAAiB3O,KAAjB,CAAuB+R,OAFzB;cAGMgH,WAAW1a,YAAYmX,QAAQsC,KAApB,GACf,OAAKnJ,WAAL,CAAiB3O,KAAjB,CAAuB2G,UADR,GAEf,OAAKgI,WAAL,CAAiB3O,KAAjB,CAAuBgZ,QAFzB;YAIE,OAAKnb,OAAP,EACGoE,EADH,CAEI6W,OAFJ,EAGI,OAAKra,MAAL,CAAYX,QAHhB,EAII,UAACjC,KAAD;mBAAW,OAAKya,MAAL,CAAYza,KAAZ,CAAX;WAJJ,EAMGoG,EANH,CAOI8W,QAPJ,EAQI,OAAKta,MAAL,CAAYX,QARhB,EASI,UAACjC,KAAD;mBAAW,OAAK0a,MAAL,CAAY1a,KAAZ,CAAX;WATJ;;;UAaA,OAAKgC,OAAP,EAAgBiD,OAAhB,CAAwB,QAAxB,EAAkCmB,EAAlC,CACE,eADF,EAEE;iBAAM,OAAKsI,IAAL,EAAN;SAFF;OA7BF;;UAmCI,KAAK9L,MAAL,CAAYX,QAAhB,EAA0B;aACnBW,MAAL,GAAc3C,EAAEuK,MAAF,CAAS,EAAT,EAAa,KAAK5H,MAAlB,EAA0B;mBAC3B,QAD2B;oBAE3B;SAFC,CAAd;OADF,MAKO;aACAwa,SAAL;;KAlfe;;WAsfnBA,SAtfmB,wBAsfP;UACJC,YAAY,OAAO,KAAKrb,OAAL,CAAaE,YAAb,CAA0B,qBAA1B,CAAzB;;UACI,KAAKF,OAAL,CAAaE,YAAb,CAA0B,OAA1B,KACDmb,cAAc,QADjB,EAC2B;aACpBrb,OAAL,CAAawF,YAAb,CACE,qBADF,EAEE,KAAKxF,OAAL,CAAaE,YAAb,CAA0B,OAA1B,KAAsC,EAFxC;aAIKF,OAAL,CAAawF,YAAb,CAA0B,OAA1B,EAAmC,EAAnC;;KA9fe;;WAkgBnBiT,MAlgBmB,mBAkgBZza,KAlgBY,EAkgBL+T,OAlgBK,EAkgBI;UACfsG,UAAU,KAAKvH,WAAL,CAAiBjP,QAAjC;gBAEUkQ,WAAW9T,EAAED,MAAMyQ,aAAR,EAAuBzK,IAAvB,CAA4BqU,OAA5B,CAArB;;UAEI,CAACtG,OAAL,EAAc;kBACF,IAAI,KAAKjB,WAAT,CACR9S,MAAMyQ,aADE,EAER,KAAK6J,kBAAL,EAFQ,CAAV;UAIEta,MAAMyQ,aAAR,EAAuBzK,IAAvB,CAA4BqU,OAA5B,EAAqCtG,OAArC;;;UAGE/T,KAAJ,EAAW;gBACD+Z,cAAR,CACE/Z,MAAMgH,IAAN,KAAe,SAAf,GAA2B2S,QAAQ7R,KAAnC,GAA2C6R,QAAQsC,KADrD,IAEI,IAFJ;;;UAKEhc,EAAE8T,QAAQ4G,aAAR,EAAF,EAA2BpV,QAA3B,CAAoCnB,UAAUkB,IAA9C,KACDyO,QAAQ+F,WAAR,KAAwBJ,WAAWpU,IADtC,EAC4C;gBAClCwU,WAAR,GAAsBJ,WAAWpU,IAAjC;;;;mBAIWyO,QAAQ8F,QAArB;cAEQC,WAAR,GAAsBJ,WAAWpU,IAAjC;;UAEI,CAACyO,QAAQnR,MAAR,CAAe0a,KAAhB,IAAyB,CAACvJ,QAAQnR,MAAR,CAAe0a,KAAf,CAAqB3O,IAAnD,EAAyD;gBAC/CA,IAAR;;;;cAIMkL,QAAR,GAAmB5O,WAAW,YAAM;YAC9B8I,QAAQ+F,WAAR,KAAwBJ,WAAWpU,IAAvC,EAA6C;kBACnCqJ,IAAR;;OAFe,EAIhBoF,QAAQnR,MAAR,CAAe0a,KAAf,CAAqB3O,IAJL,CAAnB;KApiBiB;;WA2iBnB+L,MA3iBmB,mBA2iBZ1a,KA3iBY,EA2iBL+T,OA3iBK,EA2iBI;UACfsG,UAAU,KAAKvH,WAAL,CAAiBjP,QAAjC;gBAEUkQ,WAAW9T,EAAED,MAAMyQ,aAAR,EAAuBzK,IAAvB,CAA4BqU,OAA5B,CAArB;;UAEI,CAACtG,OAAL,EAAc;kBACF,IAAI,KAAKjB,WAAT,CACR9S,MAAMyQ,aADE,EAER,KAAK6J,kBAAL,EAFQ,CAAV;UAIEta,MAAMyQ,aAAR,EAAuBzK,IAAvB,CAA4BqU,OAA5B,EAAqCtG,OAArC;;;UAGE/T,KAAJ,EAAW;gBACD+Z,cAAR,CACE/Z,MAAMgH,IAAN,KAAe,UAAf,GAA4B2S,QAAQ7R,KAApC,GAA4C6R,QAAQsC,KADtD,IAEI,KAFJ;;;UAKElI,QAAQyG,oBAAR,EAAJ,EAAoC;;;;mBAIvBzG,QAAQ8F,QAArB;cAEQC,WAAR,GAAsBJ,WAAWoC,GAAjC;;UAEI,CAAC/H,QAAQnR,MAAR,CAAe0a,KAAhB,IAAyB,CAACvJ,QAAQnR,MAAR,CAAe0a,KAAf,CAAqB5O,IAAnD,EAAyD;gBAC/CA,IAAR;;;;cAIMmL,QAAR,GAAmB5O,WAAW,YAAM;YAC9B8I,QAAQ+F,WAAR,KAAwBJ,WAAWoC,GAAvC,EAA4C;kBAClCpN,IAAR;;OAFe,EAIhBqF,QAAQnR,MAAR,CAAe0a,KAAf,CAAqB5O,IAJL,CAAnB;KA3kBiB;;WAklBnB8L,oBAllBmB,mCAklBI;WAChB,IAAMhY,OAAX,IAAsB,KAAKuX,cAA3B,EAA2C;YACrC,KAAKA,cAAL,CAAoBvX,OAApB,CAAJ,EAAkC;iBACzB,IAAP;;;;aAIG,KAAP;KAzlBiB;;WA4lBnBqG,UA5lBmB,uBA4lBRjG,MA5lBQ,EA4lBA;eACR3C,EAAEuK,MAAF,CACP,EADO,EAEP,KAAKsI,WAAL,CAAiB3K,OAFV,EAGPlI,EAAE,KAAK+B,OAAP,EAAgBgE,IAAhB,EAHO,EAIPpD,MAJO,CAAT;;UAOI,OAAOA,OAAO0a,KAAd,KAAwB,QAA5B,EAAsC;eAC7BA,KAAP,GAAe;gBACN1a,OAAO0a,KADD;gBAEN1a,OAAO0a;SAFhB;;;UAME,OAAO1a,OAAOga,KAAd,KAAwB,QAA5B,EAAsC;eAC7BA,KAAP,GAAeha,OAAOga,KAAP,CAAald,QAAb,EAAf;;;UAGE,OAAOkD,OAAO2Z,OAAd,KAA0B,QAA9B,EAAwC;eAC/BA,OAAP,GAAiB3Z,OAAO2Z,OAAP,CAAe7c,QAAf,EAAjB;;;WAGG+K,eAAL,CACE9G,IADF,EAEEf,MAFF,EAGE,KAAKkQ,WAAL,CAAiB1K,WAHnB;aAMOxF,MAAP;KAznBiB;;WA4nBnB0X,kBA5nBmB,iCA4nBE;UACb1X,SAAS,EAAf;;UAEI,KAAKA,MAAT,EAAiB;aACV,IAAM2a,GAAX,IAAkB,KAAK3a,MAAvB,EAA+B;cACzB,KAAKkQ,WAAL,CAAiB3K,OAAjB,CAAyBoV,GAAzB,MAAkC,KAAK3a,MAAL,CAAY2a,GAAZ,CAAtC,EAAwD;mBAC/CA,GAAP,IAAc,KAAK3a,MAAL,CAAY2a,GAAZ,CAAd;;;;;aAKC3a,MAAP;KAvoBiB;;WA0oBnBoZ,cA1oBmB,6BA0oBF;UACTI,OAAOnc,EAAE,KAAK0a,aAAL,EAAF,CAAb;UACM6C,WAAWpB,KAAK/M,IAAL,CAAU,OAAV,EAAmBzP,KAAnB,CAAyB6Z,kBAAzB,CAAjB;;UACI+D,aAAa,IAAb,IAAqBA,SAASnb,MAAT,GAAkB,CAA3C,EAA8C;aACvCgD,WAAL,CAAiBmY,SAASC,IAAT,CAAc,EAAd,CAAjB;;KA9oBe;;WAkpBnB9B,4BAlpBmB,yCAkpBU3V,IAlpBV,EAkpBgB;WAC5BgW,cAAL;;WACKX,kBAAL,CAAwB,KAAKD,cAAL,CAAoBpV,KAAKkN,SAAzB,CAAxB;KAppBiB;;WAupBnB0I,cAvpBmB,6BAupBF;UACT5B,MAAsB,KAAKW,aAAL,EAA5B;UACM+C,sBAAsB,KAAK9a,MAAL,CAAYsY,SAAxC;;UACIlB,IAAI9X,YAAJ,CAAiB,aAAjB,MAAoC,IAAxC,EAA8C;;;;QAG5C8X,GAAF,EAAO3U,WAAP,CAAmBjB,UAAUoB,IAA7B;WACK5C,MAAL,CAAYsY,SAAZ,GAAwB,KAAxB;WACKxM,IAAL;WACKC,IAAL;WACK/L,MAAL,CAAYsY,SAAZ,GAAwBwC,mBAAxB;KAjqBiB;;;YAsqBZ7X,gBAtqBY,6BAsqBKjD,MAtqBL,EAsqBa;aACvB,KAAKkD,IAAL,CAAU,YAAY;YACvBE,OAAY/F,EAAE,IAAF,EAAQ+F,IAAR,CAAanC,QAAb,CAAhB;;YACM+E,UAAU,OAAOhG,MAAP,KAAkB,QAAlB,IAA8BA,MAA9C;;YAEI,CAACoD,IAAD,IAAS,eAAezC,IAAf,CAAoBX,MAApB,CAAb,EAA0C;;;;YAItC,CAACoD,IAAL,EAAW;iBACF,IAAIuT,OAAJ,CAAY,IAAZ,EAAkB3Q,OAAlB,CAAP;YACE,IAAF,EAAQ5C,IAAR,CAAanC,QAAb,EAAuBmC,IAAvB;;;YAGE,OAAOpD,MAAP,KAAkB,QAAtB,EAAgC;cAC1B,OAAOoD,KAAKpD,MAAL,CAAP,KAAwB,WAA5B,EAAyC;kBACjC,IAAIY,KAAJ,wBAA8BZ,MAA9B,QAAN;;;eAEGA,MAAL;;OAjBG,CAAP;KAvqBiB;;;;0BAkIE;eACZgB,OAAP;;;;0BAGmB;eACZuE,OAAP;;;;0BAGgB;eACTxE,IAAP;;;;0BAGoB;eACbE,QAAP;;;;0BAGiB;eACVM,KAAP;;;;0BAGqB;eACdL,SAAP;;;;0BAGuB;eAChBsE,WAAP;;;;;;;;;;;;IA0iBF5G,EAAF,CAAKmC,IAAL,IAAyB4V,QAAQ1T,gBAAjC;IACErE,EAAF,CAAKmC,IAAL,EAAW4C,WAAX,GAAyBgT,OAAzB;;IACE/X,EAAF,CAAKmC,IAAL,EAAW6C,UAAX,GAAyB,YAAY;MACjChF,EAAF,CAAKmC,IAAL,IAAaK,kBAAb;WACOuV,QAAQ1T,gBAAf;GAFF;;SAKO0T,OAAP;CA5sBc,CA8sBbtZ,CA9sBa,EA8sBV8Q,MA9sBU,CAAhB;;ACRA;;;;;;;AAOA,IAAM4M,UAAW,YAAM;;;;;;MASfha,OAAsB,SAA5B;MACMC,UAAsB,cAA5B;MACMC,WAAsB,YAA5B;MACMC,kBAA0BD,QAAhC;MACMG,qBAAsB/D,EAAEuB,EAAF,CAAKmC,IAAL,CAA5B;MACM6V,eAAsB,YAA5B;MACMC,qBAAsB,IAAInW,MAAJ,aAAqBkW,YAArB,WAAyC,GAAzC,CAA5B;MAEMrR,UAAUlI,EAAEuK,MAAF,CAAS,EAAT,EAAa+O,QAAQpR,OAArB,EAA8B;eAChC,OADgC;aAEhC,OAFgC;aAGhC,EAHgC;cAIhC,yCACA,2BADA,GAEA,kCAFA,GAGA;GAPE,CAAhB;MAUMC,cAAcnI,EAAEuK,MAAF,CAAS,EAAT,EAAa+O,QAAQnR,WAArB,EAAkC;aAC1C;GADQ,CAApB;MAIMhE,YAAY;UACT,MADS;UAET;GAFT;MAKMF,WAAW;WACL,iBADK;aAEL;GAFZ;MAKMC,QAAQ;mBACQL,SADR;uBAEUA,SAFV;mBAGQA,SAHR;qBAISA,SAJT;2BAKYA,SALZ;qBAMSA,SANT;yBAOWA,SAPX;2BAQYA,SARZ;+BAScA,SATd;+BAUcA;;;;;;;GAV5B;;MAoBM6Z,OA7De;;;;;;;;;;;;WAiGnB/C,aAjGmB,4BAiGH;aACP,KAAKsB,QAAL,MAAmB,KAAK0B,WAAL,EAA1B;KAlGiB;;WAqGnBvC,kBArGmB,+BAqGAF,UArGA,EAqGY;QAC3B,KAAKR,aAAL,EAAF,EAAwBlO,QAAxB,CAAoC+M,YAApC,SAAoD2B,UAApD;KAtGiB;;WAyGnBR,aAzGmB,4BAyGH;WACTX,GAAL,GAAW,KAAKA,GAAL,IAAY/Z,EAAE,KAAK2C,MAAL,CAAYuZ,QAAd,EAAwB,CAAxB,CAAvB;aACO,KAAKnC,GAAZ;KA3GiB;;WA8GnBiB,UA9GmB,yBA8GN;UACLmB,OAAOnc,EAAE,KAAK0a,aAAL,EAAF,CAAb,CADW;;WAIN0B,iBAAL,CAAuBD,KAAKha,IAAL,CAAU8B,SAAS2Z,KAAnB,CAAvB,EAAkD,KAAK3B,QAAL,EAAlD;WACKG,iBAAL,CAAuBD,KAAKha,IAAL,CAAU8B,SAAS4Z,OAAnB,CAAvB,EAAoD,KAAKF,WAAL,EAApD;WAEKvY,WAAL,CAAoBjB,UAAUoB,IAA9B,SAAsCpB,UAAUkB,IAAhD;KArHiB;;;WA0HnBsY,WA1HmB,0BA0HL;aACL,KAAK5b,OAAL,CAAaE,YAAb,CAA0B,cAA1B,MACD,OAAO,KAAKU,MAAL,CAAY2Z,OAAnB,KAA+B,UAA/B,GACE,KAAK3Z,MAAL,CAAY2Z,OAAZ,CAAoB5c,IAApB,CAAyB,KAAKqC,OAA9B,CADF,GAEE,KAAKY,MAAL,CAAY2Z,OAHb,CAAP;KA3HiB;;WAiInBP,cAjImB,6BAiIF;UACTI,OAAOnc,EAAE,KAAK0a,aAAL,EAAF,CAAb;UACM6C,WAAWpB,KAAK/M,IAAL,CAAU,OAAV,EAAmBzP,KAAnB,CAAyB6Z,kBAAzB,CAAjB;;UACI+D,aAAa,IAAb,IAAqBA,SAASnb,MAAT,GAAkB,CAA3C,EAA8C;aACvCgD,WAAL,CAAiBmY,SAASC,IAAT,CAAc,EAAd,CAAjB;;KArIe;;;YA4IZ5X,gBA5IY,6BA4IKjD,MA5IL,EA4Ia;aACvB,KAAKkD,IAAL,CAAU,YAAY;YACvBE,OAAY/F,EAAE,IAAF,EAAQ+F,IAAR,CAAanC,QAAb,CAAhB;;YACM+E,UAAU,OAAOhG,MAAP,KAAkB,QAAlB,GAA6BA,MAA7B,GAAsC,IAAtD;;YAEI,CAACoD,IAAD,IAAS,eAAezC,IAAf,CAAoBX,MAApB,CAAb,EAA0C;;;;YAItC,CAACoD,IAAL,EAAW;iBACF,IAAI2X,OAAJ,CAAY,IAAZ,EAAkB/U,OAAlB,CAAP;YACE,IAAF,EAAQ5C,IAAR,CAAanC,QAAb,EAAuBmC,IAAvB;;;YAGE,OAAOpD,MAAP,KAAkB,QAAtB,EAAgC;cAC1B,OAAOoD,KAAKpD,MAAL,CAAP,KAAwB,WAA5B,EAAyC;kBACjC,IAAIY,KAAJ,wBAA8BZ,MAA9B,QAAN;;;eAEGA,MAAL;;OAjBG,CAAP;KA7IiB;;;;;0BAkEE;eACZgB,OAAP;;;;0BAGmB;eACZuE,OAAP;;;;0BAGgB;eACTxE,IAAP;;;;0BAGoB;eACbE,QAAP;;;;0BAGiB;eACVM,KAAP;;;;0BAGqB;eACdL,SAAP;;;;0BAGuB;eAChBsE,WAAP;;;;IA9BkBmR,OA7DD;;;;;;;;IA2KnB/X,EAAF,CAAKmC,IAAL,IAAyBga,QAAQ9X,gBAAjC;IACErE,EAAF,CAAKmC,IAAL,EAAW4C,WAAX,GAAyBoX,OAAzB;;IACEnc,EAAF,CAAKmC,IAAL,EAAW6C,UAAX,GAAyB,YAAY;MACjChF,EAAF,CAAKmC,IAAL,IAAaK,kBAAb;WACO2Z,QAAQ9X,gBAAf;GAFF;;SAKO8X,OAAP;CAlLc,CAoLb1d,CApLa,CAAhB;;ACPA;;;;;;;AAOA,IAAM8d,YAAa,YAAM;;;;;;MASjBpa,OAAqB,WAA3B;MACMC,UAAqB,cAA3B;MACMC,WAAqB,cAA3B;MACMC,kBAAyBD,QAA/B;MACME,eAAqB,WAA3B;MACMC,qBAAqB/D,EAAEuB,EAAF,CAAKmC,IAAL,CAA3B;MAEMwE,UAAU;YACL,EADK;YAEL,MAFK;YAGL;GAHX;MAMMC,cAAc;YACT,QADS;YAET,QAFS;YAGT;GAHX;MAMMjE,QAAQ;2BACeL,SADf;uBAEaA,SAFb;4BAGWA,SAAvB,GAAmCC;GAHrC;MAMMK,YAAY;mBACA,eADA;mBAEA,eAFA;YAGA;GAHlB;MAMMF,WAAW;cACG,qBADH;YAEG,SAFH;oBAGG,mBAHH;eAIG,WAJH;eAKG,WALH;gBAMG,kBANH;cAOG,WAPH;oBAQG,gBARH;qBASG;GATpB;MAYM8Z,eAAe;YACR,QADQ;cAER;;;;;;;GAFb;;MAYMD,SAhEiB;;;uBAkET/b,OAAZ,EAAqBY,MAArB,EAA6B;;;WACtByB,QAAL,GAAsBrC,OAAtB;WACKic,cAAL,GAAsBjc,QAAQkJ,OAAR,KAAoB,MAApB,GAA6BxK,MAA7B,GAAsCsB,OAA5D;WACK4G,OAAL,GAAsB,KAAKC,UAAL,CAAgBjG,MAAhB,CAAtB;WACKsb,SAAL,GAAyB,KAAKtV,OAAL,CAAa1I,MAAhB,SAA0BgE,SAASia,SAAnC,UACG,KAAKvV,OAAL,CAAa1I,MADhB,SAC0BgE,SAASka,UADnC,WAEG,KAAKxV,OAAL,CAAa1I,MAFhB,SAE0BgE,SAASma,cAFnC,CAAtB;WAGKC,QAAL,GAAsB,EAAtB;WACKC,QAAL,GAAsB,EAAtB;WACKC,aAAL,GAAsB,IAAtB;WACKC,aAAL,GAAsB,CAAtB;QAEE,KAAKR,cAAP,EAAuB7X,EAAvB,CAA0BjC,MAAMua,MAAhC,EAAwC,UAAC1e,KAAD;eAAW,MAAK2e,QAAL,CAAc3e,KAAd,CAAX;OAAxC;WAEK4e,OAAL;;WACKD,QAAL;KAjFmB;;;;;;WAkGrBC,OAlGqB,sBAkGX;;;UACFC,aAAa,KAAKZ,cAAL,KAAwB,KAAKA,cAAL,CAAoBvd,MAA5C,GACjBsd,aAAac,QADI,GACOd,aAAae,MADvC;UAGMC,eAAe,KAAKpW,OAAL,CAAaqW,MAAb,KAAwB,MAAxB,GACnBJ,UADmB,GACN,KAAKjW,OAAL,CAAaqW,MAD5B;UAGMC,aAAaF,iBAAiBhB,aAAac,QAA9B,GACjB,KAAKK,aAAL,EADiB,GACM,CADzB;WAGKb,QAAL,GAAgB,EAAhB;WACKC,QAAL,GAAgB,EAAhB;WAEKE,aAAL,GAAqB,KAAKW,gBAAL,EAArB;UAEMC,UAAUpf,EAAEmL,SAAF,CAAYnL,EAAE,KAAKie,SAAP,CAAZ,CAAhB;cAGGoB,GADH,CACO,UAACtd,OAAD,EAAa;YACZ9B,MAAJ;YACMqf,iBAAiBngB,KAAK2F,sBAAL,CAA4B/C,OAA5B,CAAvB;;YAEIud,cAAJ,EAAoB;mBACTtf,EAAEsf,cAAF,EAAkB,CAAlB,CAAT;;;YAGErf,MAAJ,EAAY;cACJsf,YAAYtf,OAAO2P,qBAAP,EAAlB;;cACI2P,UAAUpG,KAAV,IAAmBoG,UAAUC,MAAjC,EAAyC;;mBAEhC,CACLxf,EAAEC,MAAF,EAAU8e,YAAV,IAA0BU,GAA1B,GAAgCR,UAD3B,EAELK,cAFK,CAAP;;;;eAMG,IAAP;OAnBJ,EAqBGlR,MArBH,CAqBU,UAACsR,IAAD;eAAWA,IAAX;OArBV,EAsBGC,IAtBH,CAsBQ,UAACC,CAAD,EAAIC,CAAJ;eAAaD,EAAE,CAAF,IAAOC,EAAE,CAAF,CAApB;OAtBR,EAuBG/C,OAvBH,CAuBW,UAAC4C,IAAD,EAAU;eACZrB,QAAL,CAAchQ,IAAd,CAAmBqR,KAAK,CAAL,CAAnB;;eACKpB,QAAL,CAAcjQ,IAAd,CAAmBqR,KAAK,CAAL,CAAnB;OAzBJ;KAnHmB;;WAgJrB9a,OAhJqB,sBAgJX;QACNC,UAAF,CAAa,KAAKT,QAAlB,EAA4BR,QAA5B;QACE,KAAKoa,cAAP,EAAuB1T,GAAvB,CAA2BzG,SAA3B;WAEKO,QAAL,GAAsB,IAAtB;WACK4Z,cAAL,GAAsB,IAAtB;WACKrV,OAAL,GAAsB,IAAtB;WACKsV,SAAL,GAAsB,IAAtB;WACKI,QAAL,GAAsB,IAAtB;WACKC,QAAL,GAAsB,IAAtB;WACKC,aAAL,GAAsB,IAAtB;WACKC,aAAL,GAAsB,IAAtB;KA3JmB;;;WAiKrB5V,UAjKqB,uBAiKVjG,MAjKU,EAiKF;eACR3C,EAAEuK,MAAF,CAAS,EAAT,EAAarC,OAAb,EAAsBvF,MAAtB,CAAT;;UAEI,OAAOA,OAAO1C,MAAd,KAAyB,QAA7B,EAAuC;YACjC+N,KAAKhO,EAAE2C,OAAO1C,MAAT,EAAiBmP,IAAjB,CAAsB,IAAtB,CAAT;;YACI,CAACpB,EAAL,EAAS;eACF7O,KAAK4b,MAAL,CAAYrX,IAAZ,CAAL;YACEf,OAAO1C,MAAT,EAAiBmP,IAAjB,CAAsB,IAAtB,EAA4BpB,EAA5B;;;eAEK/N,MAAP,SAAoB+N,EAApB;;;WAGGxD,eAAL,CAAqB9G,IAArB,EAA2Bf,MAA3B,EAAmCwF,WAAnC;aAEOxF,MAAP;KA/KmB;;WAkLrBuc,aAlLqB,4BAkLL;aACP,KAAKlB,cAAL,KAAwBvd,MAAxB,GACH,KAAKud,cAAL,CAAoB8B,WADjB,GAC+B,KAAK9B,cAAL,CAAoBvH,SAD1D;KAnLmB;;WAuLrB0I,gBAvLqB,+BAuLF;aACV,KAAKnB,cAAL,CAAoBpG,YAApB,IAAoChW,KAAKme,GAAL,CACzCnf,SAAS2U,IAAT,CAAcqC,YAD2B,EAEzChX,SAASkK,eAAT,CAAyB8M,YAFgB,CAA3C;KAxLmB;;WA8LrBoI,gBA9LqB,+BA8LF;aACV,KAAKhC,cAAL,KAAwBvd,MAAxB,GACHA,OAAOwf,WADJ,GACkB,KAAKjC,cAAL,CAAoBpO,qBAApB,GAA4C4P,MADrE;KA/LmB;;WAmMrBd,QAnMqB,uBAmMV;UACHjI,YAAe,KAAKyI,aAAL,KAAuB,KAAKvW,OAAL,CAAa4K,MAAzD;;UACMqE,eAAe,KAAKuH,gBAAL,EAArB;;UACMe,YAAe,KAAKvX,OAAL,CAAa4K,MAAb,GACjBqE,YADiB,GAEjB,KAAKoI,gBAAL,EAFJ;;UAII,KAAKxB,aAAL,KAAuB5G,YAA3B,EAAyC;aAClC+G,OAAL;;;UAGElI,aAAayJ,SAAjB,EAA4B;YACpBjgB,SAAS,KAAKqe,QAAL,CAAc,KAAKA,QAAL,CAAclc,MAAd,GAAuB,CAArC,CAAf;;YAEI,KAAKmc,aAAL,KAAuBte,MAA3B,EAAmC;eAC5BkgB,SAAL,CAAelgB,MAAf;;;;;;UAKA,KAAKse,aAAL,IAAsB9H,YAAY,KAAK4H,QAAL,CAAc,CAAd,CAAlC,IAAsD,KAAKA,QAAL,CAAc,CAAd,IAAmB,CAA7E,EAAgF;aACzEE,aAAL,GAAqB,IAArB;;aACK6B,MAAL;;;;;WAIG,IAAIlS,IAAI,KAAKmQ,QAAL,CAAcjc,MAA3B,EAAmC8L,GAAnC,GAAyC;YACjCmS,iBAAiB,KAAK9B,aAAL,KAAuB,KAAKD,QAAL,CAAcpQ,CAAd,CAAvB,IAChBuI,aAAa,KAAK4H,QAAL,CAAcnQ,CAAd,CADG,KAEf,OAAO,KAAKmQ,QAAL,CAAcnQ,IAAI,CAAlB,CAAP,KAAgC,WAAhC,IACAuI,YAAY,KAAK4H,QAAL,CAAcnQ,IAAI,CAAlB,CAHG,CAAvB;;YAKImS,cAAJ,EAAoB;eACbF,SAAL,CAAe,KAAK7B,QAAL,CAAcpQ,CAAd,CAAf;;;KApOe;;WAyOrBiS,SAzOqB,sBAyOXlgB,MAzOW,EAyOH;WACXse,aAAL,GAAqBte,MAArB;;WAEKmgB,MAAL;;UAEIE,UAAU,KAAKrC,SAAL,CAAepB,KAAf,CAAqB,GAArB,CAAd,CALgB;;;gBAOFyD,QAAQjB,GAAR,CAAY,UAACrd,QAAD,EAAc;eAC5BA,QAAH,uBAA4B/B,MAA5B,aACG+B,QADH,gBACqB/B,MADrB,SAAP;OADY,CAAd;UAKMsgB,QAAQvgB,EAAEsgB,QAAQ9C,IAAR,CAAa,GAAb,CAAF,CAAd;;UAEI+C,MAAMjb,QAAN,CAAenB,UAAUqc,aAAzB,CAAJ,EAA6C;cACrCxb,OAAN,CAAcf,SAASwc,QAAvB,EAAiCte,IAAjC,CAAsC8B,SAASyc,eAA/C,EAAgElU,QAAhE,CAAyErI,UAAU8C,MAAnF;cACMuF,QAAN,CAAerI,UAAU8C,MAAzB;OAFF,MAGO;;cAECuF,QAAN,CAAerI,UAAU8C,MAAzB,EAFK;;;cAKC0Z,OAAN,CAAc1c,SAAS2c,cAAvB,EAAuCtX,IAAvC,CAA+CrF,SAASia,SAAxD,UAAsEja,SAASka,UAA/E,EAA6F3R,QAA7F,CAAsGrI,UAAU8C,MAAhH,EALK;;cAOC0Z,OAAN,CAAc1c,SAAS2c,cAAvB,EAAuCtX,IAAvC,CAA4CrF,SAAS4c,SAArD,EAAgEtU,QAAhE,CAAyEtI,SAASia,SAAlF,EAA6F1R,QAA7F,CAAsGrI,UAAU8C,MAAhH;;;QAGA,KAAK+W,cAAP,EAAuBzb,OAAvB,CAA+B2B,MAAM4c,QAArC,EAA+C;uBAC9B7gB;OADjB;KApQmB;;WAyQrBmgB,MAzQqB,qBAyQZ;QACL,KAAKnC,SAAP,EAAkB7P,MAAlB,CAAyBnK,SAASgD,MAAlC,EAA0C7B,WAA1C,CAAsDjB,UAAU8C,MAAhE;KA1QmB;;;cAgRdrB,gBAhRc,6BAgRGjD,MAhRH,EAgRW;aACvB,KAAKkD,IAAL,CAAU,YAAY;YACvBE,OAAY/F,EAAE,IAAF,EAAQ+F,IAAR,CAAanC,QAAb,CAAhB;;YACM+E,UAAU,OAAOhG,MAAP,KAAkB,QAAlB,IAA8BA,MAA9C;;YAEI,CAACoD,IAAL,EAAW;iBACF,IAAI+X,SAAJ,CAAc,IAAd,EAAoBnV,OAApB,CAAP;YACE,IAAF,EAAQ5C,IAAR,CAAanC,QAAb,EAAuBmC,IAAvB;;;YAGE,OAAOpD,MAAP,KAAkB,QAAtB,EAAgC;cAC1B,OAAOoD,KAAKpD,MAAL,CAAP,KAAwB,WAA5B,EAAyC;kBACjC,IAAIY,KAAJ,wBAA8BZ,MAA9B,QAAN;;;eAEGA,MAAL;;OAbG,CAAP;KAjRmB;;;;0BAuFA;eACZgB,OAAP;;;;0BAGmB;eACZuE,OAAP;;;;;;;;;;;;IAiNFzH,MAAF,EAAU0F,EAAV,CAAajC,MAAMuJ,aAAnB,EAAkC,YAAM;QAChCsT,aAAa/gB,EAAEmL,SAAF,CAAYnL,EAAEiE,SAAS+c,QAAX,CAAZ,CAAnB;;SAEK,IAAI9S,IAAI6S,WAAW3e,MAAxB,EAAgC8L,GAAhC,GAAsC;UAC9B+S,OAAOjhB,EAAE+gB,WAAW7S,CAAX,CAAF,CAAb;;gBACUtI,gBAAV,CAA2BlG,IAA3B,CAAgCuhB,IAAhC,EAAsCA,KAAKlb,IAAL,EAAtC;;GALJ;;;;;;;IAgBExE,EAAF,CAAKmC,IAAL,IAAyBoa,UAAUlY,gBAAnC;IACErE,EAAF,CAAKmC,IAAL,EAAW4C,WAAX,GAAyBwX,SAAzB;;IACEvc,EAAF,CAAKmC,IAAL,EAAW6C,UAAX,GAAyB,YAAY;MACjChF,EAAF,CAAKmC,IAAL,IAAaK,kBAAb;WACO+Z,UAAUlY,gBAAjB;GAFF;;SAKOkY,SAAP;CApUgB,CAsUf9d,CAtUe,CAAlB;;ACPA;;;;;;;AAOA,IAAMkhB,MAAO,YAAM;;;;;;MASXxd,OAAsB,KAA5B;MACMC,UAAsB,cAA5B;MACMC,WAAsB,QAA5B;MACMC,kBAA0BD,QAAhC;MACME,eAAsB,WAA5B;MACMC,qBAAsB/D,EAAEuB,EAAF,CAAKmC,IAAL,CAA5B;MACMM,sBAAsB,GAA5B;MAEME,QAAQ;mBACYL,SADZ;uBAEcA,SAFd;mBAGYA,SAHZ;qBAIaA,SAJb;8BAKaA,SAAzB,GAAqCC;GALvC;MAQMK,YAAY;mBACA,eADA;YAEA,QAFA;cAGA,UAHA;UAIA,MAJA;UAKA;GALlB;MAQMF,WAAW;cACS,WADT;oBAES,mBAFT;YAGS,SAHT;eAIS,gBAJT;iBAKS,iEALT;qBAMS,kBANT;2BAOS;;;;;;;GAP1B;;MAiBMid,GAlDW;;;iBAoDHnf,OAAZ,EAAqB;WACdqC,QAAL,GAAgBrC,OAAhB;KArDa;;;;;;WAkEf2M,IAlEe,mBAkER;;;UACD,KAAKtK,QAAL,CAAc6P,UAAd,IACA,KAAK7P,QAAL,CAAc6P,UAAd,CAAyBxR,QAAzB,KAAsC2T,KAAKC,YAD3C,IAEArW,EAAE,KAAKoE,QAAP,EAAiBkB,QAAjB,CAA0BnB,UAAU8C,MAApC,CAFA,IAGAjH,EAAE,KAAKoE,QAAP,EAAiBkB,QAAjB,CAA0BnB,UAAU0N,QAApC,CAHJ,EAGmD;;;;UAI/C5R,MAAJ;UACIkhB,QAAJ;UACMC,cAAcphB,EAAE,KAAKoE,QAAP,EAAiBY,OAAjB,CAAyBf,SAAS2c,cAAlC,EAAkD,CAAlD,CAApB;UACM5e,WAAc7C,KAAK2F,sBAAL,CAA4B,KAAKV,QAAjC,CAApB;;UAEIgd,WAAJ,EAAiB;YACTC,eAAeD,YAAYE,QAAZ,KAAyB,IAAzB,GAAgCrd,SAASsd,SAAzC,GAAqDtd,SAASgD,MAAnF;mBACWjH,EAAEmL,SAAF,CAAYnL,EAAEohB,WAAF,EAAejf,IAAf,CAAoBkf,YAApB,CAAZ,CAAX;mBACWF,SAASA,SAAS/e,MAAT,GAAkB,CAA3B,CAAX;;;UAGI4R,YAAYhU,EAAEkE,KAAF,CAAQA,MAAMyL,IAAd,EAAoB;uBACrB,KAAKvL;OADJ,CAAlB;UAIM6N,YAAYjS,EAAEkE,KAAF,CAAQA,MAAMmB,IAAd,EAAoB;uBACrB8b;OADC,CAAlB;;UAIIA,QAAJ,EAAc;UACVA,QAAF,EAAY5e,OAAZ,CAAoByR,SAApB;;;QAGA,KAAK5P,QAAP,EAAiB7B,OAAjB,CAAyB0P,SAAzB;;UAEIA,UAAUvN,kBAAV,MACDsP,UAAUtP,kBAAV,EADH,EACmC;;;;UAI/B1C,QAAJ,EAAc;iBACHhC,EAAEgC,QAAF,EAAY,CAAZ,CAAT;;;WAGGme,SAAL,CACE,KAAK/b,QADP,EAEEgd,WAFF;;UAKM9R,WAAW,SAAXA,QAAW,GAAM;YACfkS,cAAcxhB,EAAEkE,KAAF,CAAQA,MAAM4L,MAAd,EAAsB;yBACzB,MAAK1L;SADF,CAApB;YAIMuS,aAAa3W,EAAEkE,KAAF,CAAQA,MAAMqL,KAAd,EAAqB;yBACvB4R;SADE,CAAnB;UAIEA,QAAF,EAAY5e,OAAZ,CAAoBif,WAApB;UACE,MAAKpd,QAAP,EAAiB7B,OAAjB,CAAyBoU,UAAzB;OAVF;;UAaI1W,MAAJ,EAAY;aACLkgB,SAAL,CAAelgB,MAAf,EAAuBA,OAAOgU,UAA9B,EAA0C3E,QAA1C;OADF,MAEO;;;KAhIM;;WAqIf1K,OArIe,sBAqIL;QACNC,UAAF,CAAa,KAAKT,QAAlB,EAA4BR,QAA5B;WACKQ,QAAL,GAAgB,IAAhB;KAvIa;;;WA6If+b,SA7Ie,sBA6ILpe,OA7IK,EA6IIsZ,SA7IJ,EA6IelE,QA7If,EA6IyB;;;UAClCsK,cAAJ;;UACIpG,UAAUiG,QAAV,KAAuB,IAA3B,EAAiC;yBACdthB,EAAEqb,SAAF,EAAalZ,IAAb,CAAkB8B,SAASsd,SAA3B,CAAjB;OADF,MAEO;yBACYvhB,EAAEqb,SAAF,EAAa9O,QAAb,CAAsBtI,SAASgD,MAA/B,CAAjB;;;UAGIya,SAAkBD,eAAe,CAAf,CAAxB;UACM1R,kBAAkBoH,YACnBhY,KAAKsC,qBAAL,EADmB,IAElBigB,UAAU1hB,EAAE0hB,MAAF,EAAUpc,QAAV,CAAmBnB,UAAUoB,IAA7B,CAFhB;;UAIM+J,WAAW,SAAXA,QAAW;eAAM,OAAKqS,mBAAL,CACrB5f,OADqB,EAErB2f,MAFqB,EAGrB3R,eAHqB,EAIrBoH,QAJqB,CAAN;OAAjB;;UAOIuK,UAAU3R,eAAd,EAA+B;UAC3B2R,MAAF,EACGvgB,GADH,CACOhC,KAAKiC,cADZ,EAC4BkO,QAD5B,EAEG9N,oBAFH,CAEwBwC,mBAFxB;OADF,MAKO;;;;UAIH0d,MAAJ,EAAY;UACRA,MAAF,EAAUtc,WAAV,CAAsBjB,UAAUkB,IAAhC;;KA3KW;;WA+Kfsc,mBA/Ke,gCA+KK5f,OA/KL,EA+Kc2f,MA/Kd,EA+KsB3R,eA/KtB,EA+KuCoH,QA/KvC,EA+KiD;UAC1DuK,MAAJ,EAAY;UACRA,MAAF,EAAUtc,WAAV,CAAsBjB,UAAU8C,MAAhC;YAEM2a,gBAAgB5hB,EAAE0hB,OAAOzN,UAAT,EAAqB9R,IAArB,CACpB8B,SAAS4d,qBADW,EAEpB,CAFoB,CAAtB;;YAIID,aAAJ,EAAmB;YACfA,aAAF,EAAiBxc,WAAjB,CAA6BjB,UAAU8C,MAAvC;;;YAGEya,OAAOzf,YAAP,CAAoB,MAApB,MAAgC,KAApC,EAA2C;iBAClCsF,YAAP,CAAoB,eAApB,EAAqC,KAArC;;;;QAIFxF,OAAF,EAAWyK,QAAX,CAAoBrI,UAAU8C,MAA9B;;UACIlF,QAAQE,YAAR,CAAqB,MAArB,MAAiC,KAArC,EAA4C;gBAClCsF,YAAR,CAAqB,eAArB,EAAsC,IAAtC;;;UAGEwI,eAAJ,EAAqB;aACd7C,MAAL,CAAYnL,OAAZ;UACEA,OAAF,EAAWyK,QAAX,CAAoBrI,UAAUkB,IAA9B;OAFF,MAGO;UACHtD,OAAF,EAAWqD,WAAX,CAAuBjB,UAAUoB,IAAjC;;;UAGExD,QAAQkS,UAAR,IACAjU,EAAE+B,QAAQkS,UAAV,EAAsB3O,QAAtB,CAA+BnB,UAAU2d,aAAzC,CADJ,EAC6D;YAErDC,kBAAkB/hB,EAAE+B,OAAF,EAAWiD,OAAX,CAAmBf,SAASwc,QAA5B,EAAsC,CAAtC,CAAxB;;YACIsB,eAAJ,EAAqB;YACjBA,eAAF,EAAmB5f,IAAnB,CAAwB8B,SAASyc,eAAjC,EAAkDlU,QAAlD,CAA2DrI,UAAU8C,MAArE;;;gBAGMM,YAAR,CAAqB,eAArB,EAAsC,IAAtC;;;UAGE4P,QAAJ,EAAc;;;KAvND;;;QA+NRvR,gBA/NQ,6BA+NSjD,MA/NT,EA+NiB;aACvB,KAAKkD,IAAL,CAAU,YAAY;YACrB0K,QAAQvQ,EAAE,IAAF,CAAd;YACI+F,OAAUwK,MAAMxK,IAAN,CAAWnC,QAAX,CAAd;;YAEI,CAACmC,IAAL,EAAW;iBACF,IAAImb,GAAJ,CAAQ,IAAR,CAAP;gBACMnb,IAAN,CAAWnC,QAAX,EAAqBmC,IAArB;;;YAGE,OAAOpD,MAAP,KAAkB,QAAtB,EAAgC;cAC1B,OAAOoD,KAAKpD,MAAL,CAAP,KAAwB,WAA5B,EAAyC;kBACjC,IAAIY,KAAJ,wBAA8BZ,MAA9B,QAAN;;;eAEGA,MAAL;;OAbG,CAAP;KAhOa;;;;0BA2DM;eACZgB,OAAP;;;;;;;;;;;;IA+LF/C,QAAF,EACGuF,EADH,CACMjC,MAAMkC,cADZ,EAC4BnC,SAAS2C,WADrC,EACkD,UAAU7G,KAAV,EAAiB;UACzDmG,cAAN;;QACIN,gBAAJ,CAAqBlG,IAArB,CAA0BM,EAAE,IAAF,CAA1B,EAAmC,MAAnC;GAHJ;;;;;;;IAaEuB,EAAF,CAAKmC,IAAL,IAAyBwd,IAAItb,gBAA7B;IACErE,EAAF,CAAKmC,IAAL,EAAW4C,WAAX,GAAyB4a,GAAzB;;IACE3f,EAAF,CAAKmC,IAAL,EAAW6C,UAAX,GAAyB,YAAY;MACjChF,EAAF,CAAKmC,IAAL,IAAaK,kBAAb;WACOmd,IAAItb,gBAAX;GAFF;;SAKOsb,GAAP;CA/QU,CAiRTlhB,CAjRS,CAAZ;;ACEA;;;;;;;AAOA,CAAC,YAAM;MACD,OAAOA,CAAP,KAAa,WAAjB,EAA8B;UACtB,IAAIuD,KAAJ,CAAU,kGAAV,CAAN;;;MAGIye,UAAUhiB,EAAEuB,EAAF,CAAK4O,MAAL,CAAY0M,KAAZ,CAAkB,GAAlB,EAAuB,CAAvB,EAA0BA,KAA1B,CAAgC,GAAhC,CAAhB;MACMoF,WAAW,CAAjB;MACMC,UAAW,CAAjB;MACMC,WAAW,CAAjB;MACMC,WAAW,CAAjB;MACMC,WAAW,CAAjB;;MAEIL,QAAQ,CAAR,IAAaE,OAAb,IAAwBF,QAAQ,CAAR,IAAaG,QAArC,IAAiDH,QAAQ,CAAR,MAAeC,QAAf,IAA2BD,QAAQ,CAAR,MAAeG,QAA1C,IAAsDH,QAAQ,CAAR,IAAaI,QAApH,IAAgIJ,QAAQ,CAAR,KAAcK,QAAlJ,EAA4J;UACpJ,IAAI9e,KAAJ,CAAU,8EAAV,CAAN;;CAbJ,EAeGvD,CAfH;;;;;;;;;;;;;;;;;;;;"}